# TypeScript 错误修复标准

基于项目文件：[src/api/aiChat/index.ts](mdc:src/api/aiChat/index.ts)

## 常见错误类型与修复方案

### 1. 数组类型错误修复

#### 当前错误示例
```typescript
❌ // 错误：类型"string"的参数不能赋给类型"never"的参数
const arr = []
for (const key in auth) {
  arr.push(`${key}=${encodeURIComponent(auth[key])}`)
}
```

#### 正确修复方案
```typescript
✅ // 方案1：明确数组类型
const arr: string[] = []
for (const key in auth) {
  arr.push(`${key}=${encodeURIComponent(auth[key])}`)
}

✅ // 方案2：使用Object.entries
export function buildFullUrl(url: string, auth: Record<string, string>): string {
  const params = Object.entries(auth)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')
  return `${url}?${params}`
}

✅ // 方案3：使用URLSearchParams (推荐)
export function buildFullUrl(url: string, auth: Record<string, string>): string {
  const searchParams = new URLSearchParams()
  Object.entries(auth).forEach(([key, value]) => {
    searchParams.append(key, value)
  })
  return `${url}?${searchParams.toString()}`
}
```

### 2. API函数类型安全改进

#### 当前问题
```typescript
❌ // 使用any类型，缺乏类型安全
export function getAiChatSign(data: any): any {
  return request({
    url: '/clientApi/v1/txy/im/anon/enter/group',
    method: 'post',
    data: data,
  })
}
```

#### 改进后的代码
```typescript
✅ // 类型安全的API函数
interface AiChatSignParams {
  groupId: string
  userId: string
  [key: string]: unknown
}

interface AiChatSignResponse {
  code: number
  message: string
  data: {
    userSig: string
    token: string
    expiredTime: number
  }
}

export function getAiChatSign(data: AiChatSignParams): Promise<AiChatSignResponse> {
  return request({
    url: '/clientApi/v1/txy/im/anon/enter/group',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}
```

### 3. 错误处理模式

#### 统一的错误处理
```typescript
// 通用API响应类型
interface ApiResponse<T = unknown> {
  code: number
  msg: string
  data: T
  timestamp?: number
}

// 错误处理包装器
async function safeApiCall<T>(
  apiCall: () => Promise<T>
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    const data = await apiCall()
    return { success: true, data }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error('API调用失败:', errorMessage)
    return { success: false, error: errorMessage }
  }
}

// 使用示例
const result = await safeApiCall(() => getAiChatSign(params))
if (result.success) {
  // 类型安全地使用 result.data
  console.log(result.data.userSig)
} else {
  // 处理错误
  ElMessage.error(result.error)
}
```

## 项目特定错误修复

### TIM SDK 相关错误
```typescript
// TIM消息发送的类型安全实现
interface TimMessageParams {
  content: string
  type: 'text' | 'image' | 'file'
  to: string
  conversationType: 'C2C' | 'GROUP'
}

interface TimMessageResponse {
  code: number
  data: {
    messageId: string
    timestamp: number
  }
}

export async function sendTimMessage(
  data: TimMessageParams, 
  params: { fromAccount: string; groupId: string }
): Promise<TimMessageResponse> {
  const token = localStorage.getItem('aiChatToken')
  
  try {
    const response = await axios({
      url: `/clientApi/v1/txy/im/anon/special/send/msg`,
      method: 'post',
      params: {
        fromAccount: params.fromAccount,
        groupId: params.groupId
      },
      data,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: token })
      },
    })
    
    return response.data
  } catch (error) {
    console.error('TIM消息发送失败:', error)
    throw new Error('消息发送失败，请重试')
  }
}
```

### 文件上传类型安全
```typescript
interface UploadProgressCallback {
  (progress: { loaded: number; total: number; percent: number }): void
}

interface ImageCutParams {
  imageFile: File
  cutArea: {
    x: number
    y: number
    width: number
    height: number
  }
  quality?: number
}

export function getImageCutDiagram(
  params: ImageCutParams,
  onUploadProgress?: UploadProgressCallback,
  onDownloadProgress?: UploadProgressCallback
): Promise<ApiResponse<{ cutImageUrl: string }>> {
  const formData = new FormData()
  formData.append('file', params.imageFile)
  formData.append('cutArea', JSON.stringify(params.cutArea))
  
  return request({
    url: '/clientApi/v1/draw/anon/cut/diagram',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: onUploadProgress ? (progressEvent) => {
      if (progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onUploadProgress({
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percent
        })
      }
    } : undefined,
    onDownloadProgress
  })
}
```

## 代码质量检查清单

### 必须修复的错误类型
1. **TypeScript编译错误** - 所有 `tsc` 报告的错误
2. **未定义变量** - ESLint `no-undef` 错误
3. **类型断言滥用** - 避免 `as any` 和不安全的断言
4. **缺失的返回类型** - 所有函数必须有明确的返回类型
5. **未处理的Promise** - 所有异步操作必须有错误处理

### 代码质量改进建议
```typescript
✅ // 1. 使用类型守卫替代类型断言
function isValidResponse(response: unknown): response is ApiResponse {
  return typeof response === 'object' && 
         response !== null && 
         typeof (response as ApiResponse).code === 'number'
}

✅ // 2. 使用枚举替代魔法字符串
const enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  AUDIO = 'audio'
}

✅ // 3. 使用const断言提升类型推导
const API_ENDPOINTS = {
  CHAT_SIGN: '/clientApi/v1/txy/im/anon/enter/group',
  SEND_MESSAGE: '/clientApi/v1/txy/im/anon/special/send/msg',
  CLEAR_MESSAGE: '/clientApi/v1/txy/im/anon/clear/c2c/msg'
} as const

✅ // 4. 使用Record类型约束对象
const HTTP_STATUS_MESSAGES: Record<number, string> = {
  200: '请求成功',
  400: '请求参数错误',
  401: '未授权访问',
  500: '服务器内部错误'
}
```

## 自动修复建议

### VSCode设置优化
```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.quoteStyle": "single",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  }
}
```

### ESLint规则建议
```javascript
// .eslintrc.js 推荐规则
module.exports = {
  rules: {
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/prefer-const': 'error',
    'prefer-template': 'error',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn'
  }
}
```

这些标准确保：
- 零TypeScript编译错误
- 类型安全的API调用
- 统一的错误处理模式
- 高质量的代码结构
- 自动化的代码修复
description:
globs:
alwaysApply: false
---
