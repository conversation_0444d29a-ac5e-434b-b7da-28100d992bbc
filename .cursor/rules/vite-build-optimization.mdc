# Vite 构建优化与配置标准

## 基础配置规范

### 标准 vite.config.ts 结构
```typescript
import { defineConfig, loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

import createVitePlugins from './vite/plugins'

export default defineConfig(({ mode, command }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  
  return {
    // 基础配置
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    
    // 插件配置
    plugins: createVitePlugins(env, command === 'build', command),
    
    // 路径解析
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '#': resolve(__dirname, 'types'),
        '~': resolve(__dirname, 'public')
      }
    },
    
    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: VITE_PORT,
      open: true,
      proxy: createProxy(env)
    },
    
    // 构建配置
    build: {
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: 'dist',
      assetsDir: 'assets',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: VITE_APP_ENV === 'production',
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          manualChunks(id) {
            // 代码分割策略
            if (id.includes('node_modules')) {
              if (id.includes('vue') || id.includes('@vue')) {
                return 'vue-vendor'
              }
              if (id.includes('element-plus')) {
                return 'element-vendor'
              }
              if (id.includes('echarts')) {
                return 'charts-vendor'
              }
              return 'vendor'
            }
          }
        }
      },
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2000
    },
    
    // CSS配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/assets/styles/variables.scss";`
        }
      }
    }
  }
})
```

## 插件配置标准

### 自动导入配置 (auto-import.js)
```javascript
import autoImport from 'unplugin-auto-import/vite'

export default function createAutoImport() {
  return autoImport({
    // 自动导入的库
    imports: [
      'vue',
      'vue-router',
      'pinia',
      {
        '@/utils/auth': ['getToken', 'setToken', 'removeToken'],
        '@/utils/request': ['request'],
        '@/stores': ['useUserStore', 'useChatStore', 'useAppStore']
      }
    ],
    
    // 组件自动导入
    resolvers: [
      // Element Plus 自动导入
      ElementPlusResolver({
        importStyle: 'sass'
      })
    ],
    
    // 类型声明文件
    dts: 'types/auto-imports.d.ts',
    
    // 包含的文件类型
    include: [
      /\.[tj]sx?$/,
      /\.vue$/,
      /\.vue\?vue/,
      /\.md$/
    ],
    
    // ESLint 支持
    eslintrc: {
      enabled: true,
      filepath: './.eslintrc-auto-import.json',
      globalsPropValue: true
    }
  })
}
```

### 组件自动导入配置
```javascript
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default function createComponents() {
  return Components({
    // 组件文件夹
    dirs: ['src/components'],
    
    // 包含的文件类型
    extensions: ['vue'],
    
    // 解析器
    resolvers: [
      ElementPlusResolver({
        importStyle: 'sass'
      })
    ],
    
    // 类型声明
    dts: 'types/components.d.ts',
    
    // 包含模式
    include: [/\.vue$/, /\.vue\?vue/]
  })
}
```

### SVG 图标配置 (svg-icon.js)
```javascript
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'

export default function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    // 图标文件夹
    iconDirs: [
      path.resolve(process.cwd(), 'src/assets/icons/svg')
    ],
    
    // 图标ID格式
    symbolId: 'icon-[dir]-[name]',
    
    // SVGO 优化选项
    svgoOptions: isBuild && {
      plugins: [
        'preset-default',
        {
          name: 'removeViewBox',
          active: false
        }
      ]
    }
  })
}
```

## 性能优化配置

### 代码分割策略
```javascript
// rollup 代码分割配置
const manualChunks = (id) => {
  if (id.includes('node_modules')) {
    // Vue 生态
    if (id.includes('vue') || id.includes('@vue') || id.includes('pinia')) {
      return 'vue-vendor'
    }
    
    // Element Plus
    if (id.includes('element-plus')) {
      return 'element-vendor'
    }
    
    // 图表库
    if (id.includes('echarts') || id.includes('chart')) {
      return 'charts-vendor'
    }
    
    // TIM SDK
    if (id.includes('tim-js-sdk') || id.includes('tim-upload-plugin')) {
      return 'tim-vendor'
    }
    
    // 工具库
    if (id.includes('lodash') || id.includes('dayjs') || id.includes('crypto-js')) {
      return 'utils-vendor'
    }
    
    // 其他第三方库
    return 'vendor'
  }
  
  // 业务代码分割
  if (id.includes('/src/views/')) {
    const dirs = id.split('/src/views/')[1].split('/')
    return `views-${dirs[0]}`
  }
  
  if (id.includes('/src/components/')) {
    return 'components'
  }
}
```

### 压缩配置 (compression.js)
```javascript
import compression from 'vite-plugin-compression'

export default function createCompression(env) {
  const { VITE_BUILD_COMPRESS } = env
  const plugins = []
  
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(',')
    
    // Gzip 压缩
    if (compressList.includes('gzip')) {
      plugins.push(
        compression({
          ext: '.gz',
          deleteOriginFile: false,
          threshold: 10240, // 10KB 以上才压缩
          algorithm: 'gzip'
        })
      )
    }
    
    // Brotli 压缩
    if (compressList.includes('brotli')) {
      plugins.push(
        compression({
          ext: '.br',
          deleteOriginFile: false,
          threshold: 10240,
          algorithm: 'brotliCompress'
        })
      )
    }
  }
  
  return plugins
}
```

## 环境变量管理

### 环境变量命名规范
```bash
# .env 文件结构
# 应用基础配置
VITE_APP_TITLE=美术宝管理系统
VITE_APP_ENV=development
VITE_PORT=8080
VITE_OPEN=true

# API配置
VITE_APP_BASE_API=/dev-api
VITE_APP_UPLOAD_URL=/upload

# 腾讯云TIM配置
VITE_TIM_APPID=140xxxxx
VITE_TIM_SECRET_KEY=xxx

# 阿里云OSS配置
VITE_OSS_ACCESS_KEY=xxx
VITE_OSS_SECRET_KEY=xxx
VITE_OSS_BUCKET=xxx
VITE_OSS_REGION=xxx

# 构建配置
VITE_BUILD_COMPRESS=gzip,brotli
VITE_DROP_CONSOLE=true
```

### 类型安全的环境变量
```typescript
// types/env.d.ts
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_ENV: 'development' | 'production' | 'staging'
  readonly VITE_PORT: number
  readonly VITE_APP_BASE_API: string
  readonly VITE_TIM_APPID: string
  readonly VITE_OSS_ACCESS_KEY: string
  readonly VITE_OSS_SECRET_KEY: string
  readonly VITE_OSS_BUCKET: string
  readonly VITE_OSS_REGION: string
  readonly VITE_BUILD_COMPRESS: string
  readonly VITE_DROP_CONSOLE: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
```

## 开发体验优化

### Mock 数据配置
```javascript
import { viteMockServe } from 'vite-plugin-mock'

export default function createMock(command, isBuild) {
  return viteMockServe({
    // Mock 文件路径
    mockPath: 'mock',
    
    // 是否启用 Mock
    localEnabled: command === 'serve',
    prodEnabled: isBuild && false,
    
    // 监听文件变化
    watchFiles: true,
    
    // 日志输出
    logger: true,
    
    // 注入代码
    injectCode: `
      import { setupMockServer } from '../mock/index.js'
      setupMockServer()
    `
  })
}
```

### 热更新优化
```javascript
// vite.config.ts 中的 HMR 配置
export default defineConfig({
  server: {
    hmr: {
      overlay: true  // 显示错误覆盖层
    }
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus/es',
      'element-plus/es/components/message/style/css',
      'element-plus/es/components/loading/style/css',
      'element-plus/es/components/notification/style/css',
      'element-plus/es/components/message-box/style/css',
      '@element-plus/icons-vue'
    ],
    exclude: ['vue-demi']
  }
})
```

## 禁止的配置模式

### 不推荐的做法
```javascript
❌ // 不要全量导入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
app.use(ElementPlus)

❌ // 不要在 vite.config 中硬编码环境值
const config = {
  base: '/my-app/',  // 应该通过环境变量控制
  server: {
    port: 3000      // 应该通过环境变量控制
  }
}

❌ // 不要忽略代码分割
build: {
  rollupOptions: {
    output: {
      manualChunks: undefined  // 会导致单个大文件
    }
  }
}
```

### 推荐的做法
```javascript
✅ // 按需导入 Element Plus
import { createApp } from 'vue'
// 通过插件自动导入

✅ // 使用环境变量控制配置
const config = {
  base: env.VITE_APP_BASE_URL,
  server: {
    port: Number(env.VITE_PORT)
  }
}

✅ // 合理的代码分割
build: {
  rollupOptions: {
    output: {
      manualChunks: manualChunks  // 使用分割策略函数
    }
  }
}
```

这些配置确保：
- 快速的开发体验
- 优化的生产构建
- 自动化的代码分割
- 类型安全的环境管理
- 高效的资源加载
description:
globs:
alwaysApply: false
---
