# API 接口开发标准

## TypeScript 类型定义规范

### 必需的接口类型定义
```typescript
// 所有API函数必须有明确的返回类型声明
export function getAiChatSign(data: ChatSignParams): Promise<ApiResponse<ChatSignResult>>

// 参数接口定义
interface ChatSignParams {
  groupId: string
  userId: string
  timestamp: number
}

// 响应数据接口定义
interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}
```

### API函数标准结构
1. **函数命名**：使用动词+名词格式（get/list/add/update/delete + 资源名）
2. **参数类型**：禁止使用 `any`，必须定义具体接口
3. **返回类型**：统一返回 `Promise<ApiResponse<T>>`
4. **JSDoc注释**：包含功能描述、参数说明、返回值说明

### request 封装使用规范
```typescript
import request from '@/utils/request'

// 标准GET请求
export function listUsers(query: UserQueryParams): Promise<ApiResponse<UserListResult>> {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query,
    headers: {
      needOrgId: true  // 根据业务需要添加特殊headers
    }
  })
}

// 标准POST请求  
export function createUser(data: CreateUserParams): Promise<ApiResponse<User>> {
  return request({
    url: '/system/user',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
```

## 错误处理与安全规范

### 必需的错误处理
1. **类型安全**：使用 TypeScript 严格模式，避免运行时类型错误
2. **参数验证**：在API调用前进行必要的参数校验
3. **响应验证**：使用 Zod 或类似库验证API响应结构

### 安全规则
1. **敏感信息**：禁止在API文件中硬编码 token、密钥等敏感信息
2. **Headers管理**：统一通过 request 拦截器处理认证headers
3. **URL参数**：使用 encodeURIComponent 处理URL参数，防止注入

## 文件组织规范

### 目录结构
```
src/api/
├── common.ts          # 通用API接口
├── types/            # API相关类型定义
│   ├── request.ts    # 请求参数类型
│   ├── response.ts   # 响应数据类型
│   └── common.ts     # 通用类型
├── modules/          # 按业务模块分组
│   ├── user/
│   ├── chat/
│   └── system/
```

### 导入导出规范
```typescript
// 统一从模块入口导出
export * from './user'
export * from './chat'
export * from './system'

// 类型定义单独导出
export type { UserQueryParams, ChatMessage } from './types'
```

## 腾讯云TIM SDK集成规范

### TIM API调用标准
```typescript
// TIM相关API必须包含错误处理
export function sendTimMessage(data: TimMessageParams): Promise<ApiResponse<TimMessageResult>> {
  return request({
    url: '/clientApi/v1/txy/im/anon/special/send/msg',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
      // 避免直接获取localStorage，通过store或utils处理
    }
  }).catch(error => {
    console.error('TIM消息发送失败:', error)
    throw error
  })
}
```

### 禁止的做法
❌ `export function getUsers(data: any): any`
❌ 直接使用 `localStorage.getItem()` 在API函数中
❌ 硬编码URL参数
❌ 缺少错误处理的TIM API调用

### 推荐的做法
✅ 明确的TypeScript类型定义
✅ 统一的错误处理机制
✅ 通过utils或store管理token
✅ 使用参数对象而非字符串拼接URL
description:
globs:
alwaysApply: false
---
