# Vue 3 组件开发标准

## Composition API 编码规范

### 必需的组件结构

```vue
<template>
  <!-- 模板内容，使用语义化HTML -->
</template>

<script setup lang="ts">
// 1. 导入语句
import { ref, reactive, computed, watch, onMounted } from 'vue'
import type { ComponentProps, ComponentEmits } from './types'

// 2. 类型定义
interface Props {
  modelValue?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

// 3. Props和Emits定义
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false,
})

const emit = defineEmits<Emits>()

// 4. 响应式数据
const loading = ref(false)
const formData = reactive({
  name: '',
  email: '',
})

// 5. 计算属性
const isValid = computed(() => {
  return formData.name && formData.email
})

// 6. 监听器
watch(
  () => props.modelValue,
  newVal => {
    // 处理props变化
  }
)

// 7. 方法定义
const handleSubmit = async () => {
  try {
    loading.value = true
    // 业务逻辑
  } finally {
    loading.value = false
  }
}

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 9. 暴露给父组件的方法/属性
defineExpose({
  handleSubmit,
  formData,
})
</script>

<style scoped lang="scss">
/* 样式使用SCSS，作用域限定 */
</style>
```

## Element Plus 集成规范

### 组件使用标准

```vue
<template>
  <!-- 表单组件 -->
  <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" @submit.prevent="handleSubmit">
    <el-form-item label="用户名" prop="username">
      <el-input v-model="formData.username" placeholder="请输入用户名" :disabled="loading" clearable />
    </el-form-item>
  </el-form>

  <!-- 表格组件 - 使用虚拟化 -->
  <el-table-v2 :columns="columns" :data="tableData" :width="800" :height="400" fixed />

  <!-- 图标使用 -->
  <el-button type="primary" :icon="Edit" @click="handleEdit"> 编辑 </el-button>
</template>

<script setup lang="ts">
import { Edit } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 表单引用
const formRef = ref<FormInstance>()

// 表单验证规则
const rules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
}
</script>
```

### 无障碍访问规范

```vue
<template>
  <!-- 必需的无障碍属性 -->
  <el-button :aria-label="buttonLabel" :tabindex="disabled ? -1 : 0" @keydown.enter="handleClick" @keydown.space.prevent="handleClick">
    {{ buttonText }}
  </el-button>

  <!-- 表格无障碍 -->
  <el-table :data="tableData" :aria-rowcount="tableData.length" :aria-colcount="columns.length" role="table">
    <!-- 表格内容 -->
  </el-table>

  <!-- 表单错误提示 -->
  <div v-if="errorMessage" role="alert" aria-live="polite" class="error-message">
    {{ errorMessage }}
  </div>
</template>
```

## TIM Chat SDK 集成组件规范

### 聊天组件标准结构

```vue
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'

// TIM实例管理
const tim = ref<any>(null)
const isTimReady = ref(false)

// 初始化TIM
const initTIM = async () => {
  try {
    tim.value = TIM.create({
      SDKAppID: import.meta.env.VITE_TIM_APPID,
    })

    // 注册插件
    tim.value.registerPlugin({
      'tim-upload-plugin': TIMUploadPlugin,
    })

    // 监听事件
    tim.value.on(TIM.EVENT.SDK_READY, onTimReady)
    tim.value.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived)

    // 登录
    await tim.value.login({
      userID: userInfo.value.id,
      userSig: userInfo.value.sig,
    })
  } catch (error) {
    console.error('TIM初始化失败:', error)
  }
}

// TIM事件处理
const onTimReady = () => {
  isTimReady.value = true
}

const onMessageReceived = (event: any) => {
  // 消息接收处理
}

// 组件销毁时清理
onUnmounted(() => {
  if (tim.value) {
    tim.value.destroy()
  }
})
</script>
```

## 组件命名与文件组织

### 命名规范

1. **组件文件名**：使用 PascalCase（如：`UserProfileDialog.vue`）
2. **组件注册名**：与文件名保持一致
3. **事件命名**：使用 kebab-case（如：`user-updated`）
4. **方法命名**：使用 handle + 动作（如：`handleSubmit`）

### 目录结构

```
src/components/
├── base/              # 基础通用组件
│   ├── BaseButton.vue
│   ├── BaseModal.vue
│   └── BaseTable.vue
├── business/          # 业务组件
│   ├── UserProfile.vue
│   ├── ChatWindow.vue
│   └── FileUpload.vue
├── layout/            # 布局组件
│   ├── AppHeader.vue
│   ├── AppSidebar.vue
│   └── AppFooter.vue
```

## 性能优化规范

### 必需的优化实践

```vue
<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// 异步组件加载
const AsyncComponent = defineAsyncComponent(() => import('@/components/HeavyComponent.vue'))

// 防抖处理
import { debounce } from 'lodash-es'
const debouncedSearch = debounce((query: string) => {
  // 搜索逻辑
}, 300)

// 计算属性缓存
const expensiveCalculation = computed(() => {
  return someExpensiveOperation(props.data)
})

// ECharts resize监听
onMounted(() => {
  const resizeObserver = new ResizeObserver(() => {
    chartInstance.value?.resize()
  })
  resizeObserver.observe(chartContainer.value)
})
</script>
```

### 禁止的做法

❌ 在template中直接使用复杂计算
❌ 直接操作DOM（除非必要）
❌ 在watch中执行同步的重复操作
❌ 未使用key的v-for循环
❌ 直接变更store state

### 推荐的做法

✅ 使用computed缓存计算结果
✅ 使用defineAsyncComponent懒加载
✅ 合理使用v-memo优化列表渲染
✅ 使用防抖节流优化用户交互
✅ 通过store actions修改状态
description:
globs:
alwaysApply: false

---
