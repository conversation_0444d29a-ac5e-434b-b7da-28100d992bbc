# TypeScript 严格类型系统规范

## 基础类型定义规范

### 接口定义标准
```typescript
// 统一的接口命名：使用PascalCase + 描述性前缀
interface ApiResponse<T = unknown> {
  readonly code: number
  readonly message: string
  readonly data: T
  readonly timestamp: number
}

interface UserInfo {
  readonly id: string
  readonly username: string
  readonly email: string
  readonly avatar?: string  // 可选属性使用?
  readonly createdAt: Date
  readonly roles: readonly string[]  // 只读数组
}

// 请求参数接口
interface UserQueryParams {
  pageNum: number
  pageSize: number
  username?: string
  status?: UserStatus
  dateRange?: readonly [Date, Date]
}
```

### 枚举与联合类型
```typescript
// 优先使用const enum减少编译后体积
const enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

// 字符串字面量联合类型
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE'
type ThemeMode = 'light' | 'dark' | 'auto'

// 复杂联合类型
type ApiError = 
  | { type: 'network'; message: string }
  | { type: 'auth'; code: number }
  | { type: 'validation'; errors: ValidationError[] }
```

## 泛型与高级类型规范

### 工具类型的正确使用
```typescript
// 从现有接口提取子集
type CreateUserParams = Pick<UserInfo, 'username' | 'email'>
type UpdateUserParams = Partial<Pick<UserInfo, 'username' | 'email' | 'avatar'>>

// 排除特定属性
type UserDisplayInfo = Omit<UserInfo, 'id' | 'createdAt'>

// 记录类型
type UserRolePermissions = Record<string, readonly string[]>

// 条件类型
type NonNullable<T> = T extends null | undefined ? never : T
```

### 泛型约束与映射类型
```typescript
// 泛型约束
interface Repository<T extends { id: string }> {
  findById(id: string): Promise<T | null>
  create(data: Omit<T, 'id'>): Promise<T>
  update(id: string, data: Partial<T>): Promise<T>
}

// 映射类型
type MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
type ApiEndpoints<T> = {
  readonly [K in keyof T]: {
    readonly url: string
    readonly method: HttpMethod
  }
}
```

## Vue 3 + TypeScript 集成规范

### 组件Props类型定义
```typescript
// 组件Props接口
interface ChatComponentProps {
  readonly modelValue: string
  readonly disabled?: boolean
  readonly maxLength?: number
  readonly placeholder?: string
  readonly validateRules?: readonly ValidationRule[]
}

// 使用withDefaults提供默认值
const props = withDefaults(defineProps<ChatComponentProps>(), {
  disabled: false,
  maxLength: 500,
  placeholder: '请输入消息内容...'
})

// Emits类型定义
interface ChatComponentEmits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', message: ChatMessage): void
  (e: 'focus', event: FocusEvent): void
}

const emit = defineEmits<ChatComponentEmits>()
```

### 组合式函数类型定义
```typescript
// Composable 返回值类型
interface UseChatReturn {
  readonly messages: Readonly<Ref<readonly ChatMessage[]>>
  readonly loading: Readonly<Ref<boolean>>
  readonly error: Readonly<Ref<string | null>>
  readonly sendMessage: (content: string) => Promise<void>
  readonly clearHistory: () => void
}

// 通用的异步状态管理
interface UseAsyncStateReturn<T> {
  readonly data: Readonly<Ref<T | null>>
  readonly loading: Readonly<Ref<boolean>>
  readonly error: Readonly<Ref<Error | null>>
  readonly execute: (...args: any[]) => Promise<void>
  readonly refresh: () => Promise<void>
}
```

## Pinia Store 类型定义规范

### Store状态类型定义
```typescript
// Store State 接口
interface ChatStoreState {
  readonly currentConversation: ConversationInfo | null
  readonly messages: readonly ChatMessage[]
  readonly unreadCount: number
  readonly connectionStatus: 'connected' | 'disconnected' | 'connecting'
  readonly timInstance: any | null
}

// Store Actions 类型
interface ChatStoreActions {
  initTIM(): Promise<void>
  sendMessage(content: string, type: MessageType): Promise<void>
  loadHistory(conversationId: string): Promise<void>
  markAsRead(conversationId: string): Promise<void>
  disconnect(): void
}

// 完整Store类型
interface ChatStore extends ChatStoreState {
  // Getters
  readonly hasUnreadMessages: boolean
  readonly isConnected: boolean
  readonly currentMessages: readonly ChatMessage[]
  
  // Actions
  readonly actions: ChatStoreActions
}
```

## 错误处理与类型安全

### 类型安全的错误处理
```typescript
// 结果类型模式
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E }

// API调用包装
async function safeApiCall<T>(
  apiFunction: () => Promise<T>
): Promise<Result<T, ApiError>> {
  try {
    const data = await apiFunction()
    return { success: true, data }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error(String(error))
    }
  }
}

// 使用示例
const result = await safeApiCall(() => getUserInfo(userId))
if (result.success) {
  // TypeScript 知道这里 result.data 是 UserInfo 类型
  console.log(result.data.username)
} else {
  // TypeScript 知道这里 result.error 是 Error 类型
  console.error(result.error.message)
}
```

### 类型守卫与断言
```typescript
// 类型守卫函数
function isUserInfo(value: unknown): value is UserInfo {
  return typeof value === 'object' && 
         value !== null && 
         typeof (value as UserInfo).id === 'string' &&
         typeof (value as UserInfo).username === 'string'
}

// 运行时类型验证
function validateApiResponse<T>(
  response: unknown,
  validator: (value: unknown) => value is T
): T {
  if (!validator(response)) {
    throw new Error('Invalid API response format')
  }
  return response
}
```

## 禁止的类型模式

### 严格禁止使用的类型
```typescript
❌ // 禁止使用 any
function processData(data: any): any {
  return data.someProperty
}

❌ // 禁止使用 object
function handleObject(obj: object): void {
  // 无法访问任何属性
}

❌ // 禁止使用 Function
function callback(fn: Function): void {
  fn() // 不安全的调用
}

❌ // 禁止不必要的类型断言
const userInfo = data as UserInfo // 应该使用类型守卫
```

### 推荐的类型安全模式
```typescript
✅ // 使用 unknown 替代 any
function processData(data: unknown): string {
  if (isUserInfo(data)) {
    return data.username
  }
  throw new Error('Invalid data format')
}

✅ // 使用具体的对象类型
function handleUserInfo(user: UserInfo): void {
  console.log(user.username) // 类型安全
}

✅ // 使用具体的函数签名
function withCallback(fn: (result: string) => void): void {
  fn('success')
}

✅ // 使用类型守卫而非断言
function getUsername(data: unknown): string {
  if (isUserInfo(data)) {
    return data.username
  }
  throw new Error('Not a valid user info')
}
```

## 项目特定类型定义

### TIM SDK 类型增强
```typescript
// 扩展TIM SDK类型
declare module 'tim-js-sdk' {
  interface TIMInstance {
    login(options: { userID: string; userSig: string }): Promise<void>
    logout(): Promise<void>
    on(eventName: string, handler: (...args: any[]) => void): void
    off(eventName: string, handler?: (...args: any[]) => void): void
  }
}

// TIM消息类型定义
interface TIMMessage {
  readonly ID: string
  readonly type: 'TIMTextElem' | 'TIMImageElem' | 'TIMFileElem'
  readonly payload: TIMMessagePayload
  readonly from: string
  readonly to: string
  readonly time: number
}
```

这些规则确保：
- 100% 类型覆盖率
- 运行时类型安全
- 清晰的错误处理
- 可维护的代码结构
- 团队开发一致性
description:
globs:
alwaysApply: false
---
