# 项目结构组织与文件管理规范

参考文件结构：[package.json](mdc:package.json)、[vite.config.js](mdc:vite.config.js)、[main.ts](mdc:src/main.ts)

## 标准目录结构

### 根目录组织
```
mb-users-magic-brush/
├── .cursor/                    # Cursor IDE 配置
│   └── rules/                 # 代码规范文件
├── bin/                       # 脚本文件
├── public/                    # 静态资源
│   ├── favicon.ico
│   └── tinymce/              # 富文本编辑器资源
├── src/                      # 源代码主目录
├── vite/                     # Vite 配置模块
├── types/                    # TypeScript 类型声明
├── mock/                     # Mock 数据
├── .env.development          # 开发环境变量
├── .env.production           # 生产环境变量
├── vite.config.js            # Vite 配置文件
├── package.json              # 项目依赖配置
└── tsconfig.json             # TypeScript 配置
```

### src 目录详细结构
```
src/
├── api/                      # API 接口层
│   ├── modules/             # 按业务模块分组
│   │   ├── aiChat/         # AI聊天相关接口
│   │   ├── system/         # 系统管理接口
│   │   ├── monitor/        # 监控相关接口
│   │   └── course/         # 课程管理接口
│   ├── types/              # API 类型定义
│   └── common.ts           # 通用API接口
├── assets/                  # 静态资源
│   ├── images/             # 图片资源
│   ├── icons/              # 图标资源
│   │   └── svg/           # SVG 图标
│   ├── styles/             # 样式文件
│   └── logo/               # Logo 文件
├── components/              # 组件库
│   ├── base/               # 基础通用组件
│   ├── business/           # 业务特定组件
│   ├── layout/             # 布局组件
│   └── TUIKit/             # 腾讯云TIM组件
├── views/                   # 页面视图
│   ├── aiChat/             # AI聊天页面
│   ├── system/             # 系统管理页面
│   ├── monitor/            # 监控页面
│   └── login.vue           # 登录页面
├── router/                  # 路由配置
├── store/                   # 状态管理 (Pinia)
│   └── modules/            # 按功能模块分组
├── utils/                   # 工具函数
├── directive/               # 自定义指令
├── plugins/                 # 插件配置
├── layout/                  # 布局组件
├── permission.ts            # 权限控制
└── main.ts                  # 应用入口
```

## 文件命名规范

### 组件文件命名
```typescript
// ✅ 推荐：PascalCase 命名
UserProfileDialog.vue
ChatMessageBubble.vue
FileUploadProgress.vue
AIToolsSelector.vue

// ❌ 禁止：kebab-case 或其他格式
user-profile-dialog.vue
chatMessageBubble.vue
file_upload_progress.vue
```

### 页面文件命名
```typescript
// ✅ 推荐：小写 + 功能描述
src/views/
├── login.vue              # 登录页面
├── register.vue           # 注册页面
├── index.vue              # 首页
├── aiChat/
│   └── index.vue          # AI聊天主页
├── system/
│   ├── user/
│   │   ├── index.vue      # 用户列表
│   │   └── profile.vue    # 用户详情
│   └── role/
│       └── index.vue      # 角色管理
```

### API 文件命名
```typescript
// ✅ 推荐：按模块组织，使用 index.ts 作为入口
src/api/
├── aiChat/
│   └── index.ts           # AI聊天接口
├── system/
│   ├── user.ts           # 用户管理接口
│   ├── role.ts           # 角色管理接口
│   └── menu.ts           # 菜单管理接口
├── monitor/
│   ├── cache.ts          # 缓存监控
│   └── server.ts         # 服务器监控
└── common.ts             # 通用接口
```

## 模块组织规范

### 按功能域划分模块
```typescript
// 功能域边界清晰的模块划分
src/
├── modules/                # 主要业务模块
│   ├── chat/              # 聊天功能模块
│   │   ├── components/    # 聊天相关组件
│   │   ├── store/         # 聊天状态管理
│   │   ├── api/           # 聊天接口
│   │   ├── types/         # 聊天类型定义
│   │   └── utils/         # 聊天工具函数
│   ├── user/              # 用户管理模块
│   │   ├── components/
│   │   ├── views/
│   │   ├── store/
│   │   └── api/
│   └── course/            # 课程管理模块
```

### 共享模块组织
```typescript
// 跨模块共享的代码
src/shared/
├── components/            # 共享组件
│   ├── ui/               # UI基础组件
│   │   ├── Button/
│   │   ├── Modal/
│   │   └── Table/
│   └── business/         # 业务共享组件
├── utils/                # 共享工具函数
│   ├── auth.ts          # 认证工具
│   ├── request.ts       # 请求封装
│   ├── storage.ts       # 存储工具
│   └── validators.ts    # 验证工具
├── types/                # 共享类型定义
│   ├── api.ts           # API类型
│   ├── user.ts          # 用户类型
│   └── common.ts        # 通用类型
└── constants/            # 共享常量
    ├── api.ts           # API常量
    ├── routes.ts        # 路由常量
    └── config.ts        # 配置常量
```

## 导入路径管理

### 路径别名配置
```typescript
// vite.config.ts 中的别名配置
export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/store': resolve(__dirname, 'src/store'),
      '@/assets': resolve(__dirname, 'src/assets'),
      '#': resolve(__dirname, 'types'),
      '~': resolve(__dirname, 'public')
    }
  }
})
```

### 导入顺序规范
```typescript
// ✅ 推荐的导入顺序
// 1. Node.js 内置模块
import path from 'path'

// 2. 第三方库
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import TIM from 'tim-js-sdk'

// 3. 项目内部模块 (按层级排序)
import { useUserStore } from '@/store/modules/user'
import { getUserList } from '@/api/system/user'
import BaseTable from '@/components/base/BaseTable.vue'
import { formatDate } from '@/utils/dateUtils'

// 4. 类型导入 (放在最后)
import type { UserInfo, ApiResponse } from '@/types/user'
import type { ElForm } from 'element-plus'
```

### 导出模式规范
```typescript
// ✅ 推荐：命名导出 + 默认导出
// api/user.ts
export interface UserQueryParams {
  pageNum: number
  pageSize: number
}

export function getUserList(params: UserQueryParams) {
  // 实现
}

export function createUser(data: CreateUserParams) {
  // 实现
}

// 默认导出汇总
export default {
  getUserList,
  createUser
}
```

### 索引文件使用规范
```typescript
// ✅ 推荐：通过 index.ts 文件统一导出
// components/index.ts
export { default as UserProfile } from './UserProfile.vue'
export { default as ChatWindow } from './ChatWindow.vue'
export { default as FileUpload } from './FileUpload.vue'

// store/index.ts
export { useUserStore } from './modules/user'
export { useChatStore } from './modules/chat'
export { useAppStore } from './modules/app'

// 使用时可以简化导入
import { UserProfile, ChatWindow } from '@/components'
import { useUserStore, useChatStore } from '@/store'
```

## 资源文件组织

### 图片资源分类
```
src/assets/images/
├── avatar/                # 头像相关
├── icons/                 # 功能图标
├── illustrations/         # 插画图片
├── backgrounds/           # 背景图片
├── chat/                  # 聊天相关图片
│   ├── bubbles/          # 聊天气泡
│   ├── tools/            # 聊天工具
│   └── stickers/         # 表情贴纸
└── common/               # 通用图片
    ├── logo.png
    ├── placeholder.png
    └── loading.gif
```

### 样式文件组织
```
src/assets/styles/
├── base/                  # 基础样式
│   ├── reset.scss        # 样式重置
│   ├── variables.scss    # SCSS变量
│   └── mixins.scss       # SCSS混入
├── components/            # 组件样式
├── pages/                 # 页面样式
├── themes/                # 主题样式
│   ├── light.scss
│   └── dark.scss
└── index.scss            # 样式入口文件
```

## 禁止的组织模式

### 不推荐的目录结构
```typescript
❌ // 混乱的文件组织
src/
├── comp/                 # 命名不清晰
├── page/                 # 应该是 pages 或 views
├── util/                 # 应该是 utils
├── lib/                  # 功能不明确
└── stuff/                # 完全不知道是什么

❌ // 过深的嵌套
src/views/system/user/management/list/components/table/

❌ // 功能混杂
src/components/
├── UserTableChatButton.vue  # 组件职责不清
├── SystemUserChatMessage.vue # 跨域组件
```

### 推荐的组织原则
```typescript
✅ // 清晰的功能边界
src/
├── components/           # 可复用组件
├── views/               # 页面视图
├── utils/               # 工具函数
├── api/                 # 接口层
└── types/               # 类型定义

✅ // 合理的嵌套深度 (不超过4层)
src/views/system/user/index.vue

✅ // 单一职责的组件
src/components/
├── UserTable.vue        # 用户表格
├── ChatMessage.vue      # 聊天消息
├── FileUpload.vue       # 文件上传
```

这种项目结构确保：
- 清晰的模块边界
- 易于维护和扩展
- 团队协作效率
- 代码复用性
- 快速定位文件
description:
globs:
alwaysApply: false
---
