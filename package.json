{"name": "mb-users-magic-brush", "version": "0.0.1", "description": "妙笔AI神笔马良", "author": "msb-front-end", "license": "MIT", "type": "module", "scripts": {"dev": "vite --config vite.config.ts", "build:live": "vite build --mode live --config vite.config.ts", "build:test": "vite build --mode test --config vite.config.ts", "preview": "vite preview --config vite.config.ts"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@msb-next/swiper": "^6.8.4", "@msb-next/vite-plugins": "^1.4.0", "@tencentcloud/chat-uikit-engine": "^2.4.0", "@tencentcloud/chat-uikit-vue": "^2.4.0", "@tinymce/tinymce-vue": "^6.1.0", "@types/howler": "^2.2.12", "@types/markdown-it": "^14.1.2", "@types/video.js": "^7.3.58", "@videojs-player/vue": "^1.0.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "ali-oss": "^6.22.0", "axios": "0.28.1", "byted-ailab-speech-sdk": "^4.0.9", "clipboard": "2.0.11", "echarts": "5.5.1", "element-plus": "^2.9.5", "fabric": "^6.7.0", "file-saver": "2.0.5", "fuse.js": "6.6.2", "glob": "7.2.3", "highlight.js": "^11.11.1", "howler": "^2.2.4", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lunar-javascript": "^1.7.2", "markdown-it": "^14.1.0", "markdown-it-image-figures": "^2.1.1", "mavon-editor": "3.0.1", "md5": "^2.3.0", "msb-public-library": "git+https://opserver:<EMAIL>/msb-fe/msb-public-library.git#0.7.8", "nprogress": "0.2.0", "pinia": "2.1.7", "recorder-core": "^1.3.25011100", "splitpanes": "3.1.5", "three": "^0.174.0", "tim-js-sdk": "^2.27.6", "tinymce": "^7.8.0", "uuid": "^11.1.0", "video.js": "^8.22.0", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vuedraggable": "4.1.0", "xgplayer": "^3.0.21"}, "devDependencies": {"@erase2d/fabric": "^1.1.7", "@types/ali-oss": "^6.16.11", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/babel-plugin-jsx": "^1.2.5", "eruda": "^3.4.1", "mockjs": "^1.1.0", "prettier": "^3.5.3", "sass": "1.77.5", "sass-loader": "10.1.1", "tslib": "^2.8.1", "typescript": "^5.7.3", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "2.0.1"}}