<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-15 16:09:17
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 16:32:03
 * @FilePath: /miaobi-admin-magic-touch/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

## 简介

- 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。

## 原型

地址:https://modao.cc/proto/CeaRoUqnsq23fiykUKSTBx/sharing?view_mode=read_only&screen=rbpUZjoah439rYYEs

## 访问地址

### 神笔马良内嵌页面

```
本地：
http://***************/ai-chat?room=2&id=3&courseid=2&role=student&_t=213213
测试:
https://test.ml.miaobi.cn/ai-chat?room=2&id=3&courseid=2&role=student&_t=213213
线上:
https://ml.miaobi.cn/ai-chat?room=2&id=3&courseid=2&role=student&_t=213213

room: 房间id
id: 用户id
courseid: 课程id
role: 角色
_t: 清空消息对比字符
```

## 前端运行

```bash

# 安装依赖
yarn --registry=https://registry.npmmirror.com

# 启动服务
yarn dev

# 构建测试环境
yarn build:stage
# 构建生产环境
yarn build:prod
# 前端访问地址
http://localhost:80
```

## 项目目录结构与功能说明

```
├── src                        # 源代码
│   ├── api                    # API接口目录
│   │   ├── aiChat/           # AI聊天相关接口
│   │   ├── application/      # 应用相关接口
│   │   ├── common.ts         # 通用接口
│   │   ├── course/           # 课程相关接口
│   │   ├── equipment/        # 设备相关接口
│   │   ├── login.ts          # 登录认证接口
│   │   ├── menu.ts           # 菜单接口
│   │   ├── monitor/          # 监控相关接口
│   │   ├── organization/     # 组织机构接口
│   │   ├── room/             # 房间相关接口
│   │   ├── system/           # 系统管理接口
│   │   ├── teachersAndStudents/ # 师生相关接口
│   │   └── tool/             # 工具类接口
│   │
│   ├── assets                 # 静态资源文件(图片、样式等)
│   │
│   ├── components             # 全局公共组件
│   │   ├── AudioRecorder/    # 音频录制组件
│   │   ├── Breadcrumb/       # 面包屑组件
│   │   ├── Chat/             # 聊天组件
│   │   ├── ChatAiTool/       # AI聊天工具组件
│   │   ├── ChatInput/        # 聊天输入组件
│   │   ├── Crontab/          # 定时任务组件
│   │   ├── DictTag/          # 字典标签组件
│   │   ├── Editor/           # 富文本编辑器
│   │   ├── FileUpload/       # 文件上传组件
│   │   ├── Hamburger/        # 汉堡菜单组件
│   │   ├── HeaderSearch/     # 头部搜索组件
│   │   ├── ImagePreview/     # 图片预览组件
│   │   ├── ImageUpload/      # 图片上传组件
│   │   ├── Pagination/       # 分页组件
│   │   ├── ParentView/       # 父视图组件
│   │   ├── RightToolbar/     # 右侧工具栏
│   │   ├── Screenfull/       # 全屏组件
│   │   ├── SizeSelect/       # 尺寸选择组件
│   │   ├── SvgIcon/          # SVG图标组件
│   │   ├── SwiperUpload/     # 轮播图上传组件
│   │   ├── TopNav/           # 顶部导航组件
│   │   └── TUIKit/           # 腾讯云即时通信组件
│   │
│   ├── directive              # 全局自定义指令
│   │
│   ├── layout                 # 布局组件
│   │
│   ├── main.ts                # 入口文件
│   │
│   ├── permission.ts          # 路由权限控制
│   │
│   ├── plugins                # 插件目录
│   │
│   ├── router                 # 路由配置
│   │
│   ├── settings.ts            # 项目配置文件
│   │
│   ├── store                  # Pinia状态管理
│   │   ├── index.ts          # Store入口
│   │   └── modules/          # Store模块
│   │       ├── app.ts        # 应用配置状态
│   │       ├── chat.ts       # 聊天状态管理
│   │       ├── dict.ts       # 字典状态管理
│   │       ├── permission.ts # 权限状态管理
│   │       ├── settings.ts   # 设置状态管理
│   │       ├── tagsView.ts   # 标签页状态管理
│   │       └── user.ts       # 用户状态管理
│   │
│   ├── types                  # 类型定义文件
│   │   ├── global.d.ts       # 全局类型声明
│   │   ├── lib.d.ts          # 第三方库类型声明
│   │   ├── components.d.ts   # 组件类型声明
│   │   └── shims-vue.d.ts    # Vue相关类型声明
│   │
│   ├── utils                  # 工具函数
│   │   ├── auth.ts           # 认证相关工具
│   │   ├── customIM.ts       # 自定义IM工具
│   │   ├── dict.ts           # 字典工具
│   │   ├── errorCode.ts      # 错误码定义
│   │   ├── generator/        # 代码生成相关
│   │   ├── index.ts          # 工具函数入口
│   │   ├── jsencrypt.ts      # 加密工具
│   │   ├── permission.ts     # 权限工具
│   │   ├── request.ts        # axios请求工具
│   │   ├── ruoyi.ts          # 通用工具函数
│   │   ├── scroll-to.ts      # 滚动工具
│   │   ├── theme.ts          # 主题工具
│   │   └── validate.ts       # 验证工具
│   │
│   └── views                  # 视图文件
│       ├── aiChat/           # AI聊天页面
│       ├── application/      # 应用管理页面
│       ├── course/           # 课程管理页面
│       ├── equipment/        # 设备管理页面
│       ├── error/            # 错误页面
│       ├── index.vue         # 首页
│       ├── institution/      # 机构管理页面
│       ├── intelligentClassroom/ # 智能教室页面
│       ├── login.vue         # 登录页
│       ├── monitor/          # 监控页面
│       ├── redirect/         # 重定向页面
│       ├── register.vue      # 注册页
│       ├── scheduling/       # 排课页面
│       ├── system/           # 系统管理页面
│       ├── teachersAndStudents/ # 师生管理页面
│       ├── tool/             # 工具页面
│       └── welcome/          # 欢迎页面
│
├── public                     # 公共静态资源
│
├── index.html                 # HTML入口文件
│
├── vite.config.ts             # Vite配置文件
│
├── migrate-to-ts.js           # TS迁移脚本
│
├── tsconfig.json              # TypeScript配置
│
└── package.json               # 项目依赖与脚本
```

### 核心目录功能说明

1. **api目录**: 按业务模块组织的API接口，封装与后端的通信请求

   - `common.ts`: 通用API接口，如上传、下载等
   - `login.ts`: 身份认证相关接口
   - `menu.ts`: 系统菜单接口

2. **components目录**: 全局公共组件，可在多个页面复用

   - `DictTag`: 数据字典标签组件，用于展示字典数据
   - `Editor`: 富文本编辑器组件，用于内容编辑
   - `Pagination`: 统一分页组件，用于列表分页
   - `FileUpload/ImageUpload`: 文件/图片上传组件
   - `SvgIcon`: SVG图标组件，支持自定义图标

3. **store目录**: 基于Pinia的状态管理模块

   - `user.ts`: 用户信息、权限等状态管理
   - `chat.ts`: 聊天相关状态管理
   - `tagsView.ts`: 标签页状态管理，控制导航标签
   - `permission.ts`: 权限状态管理，控制路由权限

4. **utils目录**: 通用工具函数

   - `request.ts`: 基于axios的HTTP请求封装
   - `auth.ts`: 用户认证相关工具函数
   - `ruoyi.ts`: 项目通用工具函数集合
   - `validate.ts`: 数据验证工具函数

5. **views目录**: 业务页面，按功能模块组织

   - `login.vue`: 登录页面
   - `system/`: 系统管理模块页面
   - `application/`: 应用管理页面
   - `aiChat/`: AI对话功能页面

6. **types目录**: TypeScript类型定义文件
   - `global.d.ts`: 全局类型定义
   - `lib.d.ts`: 第三方库类型声明
   - `components.d.ts`: 组件类型声明

## TypeScript迁移指南

本项目正在从JavaScript逐步迁移到TypeScript。我们提供了一个自动化脚本来帮助完成大部分迁移工作。

### 迁移脚本使用方法

1. 确保已安装所有依赖：

```bash
npm install
# 或者
yarn
```

2. 运行迁移脚本：

```bash
node migrate-to-ts.js
```

脚本将自动：

- 创建必要的类型定义文件和目录
- 将JS文件转换为TS文件
- 添加基础类型注解
- 更新项目的主要入口文件
- 更新package.json中的脚本配置
- 创建TypeScript版本的Vite配置

### 脚本执行后的操作

迁移脚本会自动执行以下任务，但你可能需要手动修复一些问题：

1. 如果脚本运行后遇到Vite报错找不到main.js，请确保：

   - vite.config.ts文件已正确创建
   - package.json中的启动脚本已更新为使用TypeScript配置
   - 项目重新启动以应用新配置

2. 确保类型文件引用正确：
   - src/types/shims-vue.d.ts - Vue文件和静态资源声明
   - src/types/global.d.ts - 全局类型定义
   - src/types/router.d.ts - 路由类型扩展

### 手动类型优化

迁移脚本只能提供基本的类型转换，请在自动迁移后进行以下手动优化：

1. 检查并修复生成的TS文件中的类型错误
2. 为API响应数据创建更精确的类型定义
3. 完善Store模块的状态类型
4. 添加更具体的函数参数和返回值类型
5. 优化模块导入类型，尤其是第三方库

### 类型定义组织

- `src/types` - 全局类型定义
  - `api/` - API相关类型
  - `store/` - Store状态类型
  - `components/` - 组件Props类型
  - `global.d.ts` - 全局通用类型
  - `shims-vue.d.ts` - Vue文件和静态资源类型声明
  - `router.d.ts` - 路由类型扩展

### TypeScript配置

项目使用以下TypeScript配置选项：

- `tsconfig.json` - 主要TypeScript配置
- `tsconfig.node.json` - Node.js环境TypeScript配置

按照模块组织类型定义，确保类型文件结构清晰，便于管理。
