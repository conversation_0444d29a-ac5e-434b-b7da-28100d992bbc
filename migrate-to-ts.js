#!/usr/bin/env node

/**
 * 项目TypeScript迁移脚本
 * 用于将JS项目迁移到TypeScript，自动添加类型声明和转换文件
 *
 * 使用方法: node migrate-to-ts.js
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { promisify } from 'util'
import { exec } from 'child_process'

const execAsync = promisify(exec)
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 配置项
const CONFIG = {
  // 需要迁移的目录
  migrateDirectories: ['src/api', 'src/store', 'src/utils', 'src/router', 'src/plugins', 'src/directive'],
  // 忽略的文件和目录
  ignorePatterns: ['node_modules', 'dist', '.git', '**/*.d.ts', '**/types.ts', '.vscode', '**/*.vue'],
  // 需要迁移的文件扩展名
  fileExtensions: ['.js', '.jsx'],
  // 不需要添加类型的变量名
  noTypeVariables: ['Vue', 'window', 'document', 'console'],
  // 主要的类型定义文件所在目录
  typesDir: 'src/types',
}

// 辅助函数: 打印日志
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m%s\x1b[0m', // 青色
    success: '\x1b[32m%s\x1b[0m', // 绿色
    warn: '\x1b[33m%s\x1b[0m', // 黄色
    error: '\x1b[31m%s\x1b[0m', // 红色
  }

  console.log(colors[type], message)
}

// 检查并创建类型定义目录
async function ensureTypesDirectory() {
  if (!fs.existsSync(CONFIG.typesDir)) {
    fs.mkdirSync(CONFIG.typesDir, { recursive: true })
    log(`创建类型定义目录: ${CONFIG.typesDir}`, 'success')

    // 创建index.d.ts文件
    const indexContent = `/**
 * 全局类型定义
 */

export {};

declare global {
  // 在这里添加全局类型
  interface Window {
    // 添加window上的全局属性
  }
}
`

    fs.writeFileSync(path.join(CONFIG.typesDir, 'index.d.ts'), indexContent)
    log(`创建全局类型定义文件: ${CONFIG.typesDir}/index.d.ts`, 'success')
  }
}

// 查找所有需要迁移的文件
async function findFilesToMigrate() {
  const allFiles = []

  for (const dir of CONFIG.migrateDirectories) {
    if (!fs.existsSync(dir)) {
      log(`目录不存在，跳过: ${dir}`, 'warn')
      continue
    }

    await traverseDirectory(dir, allFiles)
  }

  return allFiles
}

// 递归遍历目录
async function traverseDirectory(dir, fileList) {
  const files = fs.readdirSync(dir)

  for (const file of files) {
    const fullPath = path.join(dir, file)

    // 检查是否应该忽略这个文件
    if (
      CONFIG.ignorePatterns.some(pattern => {
        if (pattern.includes('*')) {
          // 简单的通配符匹配
          const regex = new RegExp(pattern.replace(/\*/g, '.*'))
          return regex.test(fullPath)
        }
        return fullPath.includes(pattern)
      })
    ) {
      continue
    }

    const stat = fs.statSync(fullPath)

    if (stat.isDirectory()) {
      await traverseDirectory(fullPath, fileList)
    } else if (stat.isFile() && CONFIG.fileExtensions.includes(path.extname(fullPath))) {
      fileList.push(fullPath)
    }
  }
}

// 将JS文件转换成TS文件
async function convertJsToTs(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8')
  const newFilePath = filePath.replace(/\.jsx?$/, '.ts')
  const fileName = path.basename(filePath)

  // 检查文件是否已经是TS
  if (filePath.endsWith('.ts')) {
    log(`文件已经是TS，跳过: ${filePath}`, 'warn')
    return
  }

  // 基本的类型转换
  let tsContent = addBasicTypes(fileContent, fileName)

  // 添加默认导入defineStore
  if (filePath.includes('store/modules')) {
    tsContent = addStoreImport(tsContent)
  }

  // 添加文件头注释
  tsContent = addFileHeader(tsContent, newFilePath)

  // 写入新文件
  fs.writeFileSync(newFilePath, tsContent)
  log(`转换文件: ${filePath} -> ${newFilePath}`, 'success')

  // 删除原JS文件
  fs.unlinkSync(filePath)
  log(`删除原文件: ${filePath}`, 'info')
}

// 为store模块添加pinia导入
function addStoreImport(content) {
  if (content.includes('defineStore') && !content.includes('import { defineStore }')) {
    return `import { defineStore } from 'pinia';\n\n${content}`
  }
  return content
}

// 为文件添加基本的类型注解
function addBasicTypes(content, fileName) {
  // 替换常见的函数声明
  content = content.replace(/function\s+(\w+)\s*\((.*?)\)/g, (match, funcName, params) => {
    const paramWithTypes = params
      .split(',')
      .map(param => param.trim())
      .filter(param => !!param)
      .map(param => {
        const paramName = param.replace(/=.*$/, '').trim()
        return `${paramName}: any`
      })
      .join(', ')

    return `function ${funcName}(${paramWithTypes}): any`
  })

  // 替换箭头函数
  content = content.replace(/const\s+(\w+)\s*=\s*\((.*?)\)\s*=>/g, (match, funcName, params) => {
    const paramWithTypes = params
      .split(',')
      .map(param => param.trim())
      .filter(param => !!param)
      .map(param => {
        const paramName = param.replace(/=.*$/, '').trim()
        // 避免给已经有类型的参数添加类型
        if (paramName.includes(':')) return paramName
        return `${paramName}: any`
      })
      .join(', ')

    return `const ${funcName} = (${paramWithTypes}): any =>`
  })

  // 修复state函数，添加返回类型
  content = content.replace(/state:\s*\(\)\s*=>\s*\({/g, 'state: (): any => ({')

  // 修复条件语句中的类型错误 - 防止添加错误的类型注解
  content = content.replace(/if\s*\(\s*(.*?)\s*\)\s*:\s*any\s*\)\s*:\s*void\s*{/g, 'if ($1) {')
  content = content.replace(/}\s*else\s+if\s*\(\s*(.*?)\s*\)\s*:\s*any\s*\)\s*:\s*void\s*{/g, '} else if ($1) {')
  content = content.replace(/for\s*\(\s*const\s+(.*?)\s+of\s+(.*?)\s*\)\s*:\s*any\s*\)\s*:\s*void\s*{/g, 'for (const $1 of $2) {')

  // 修复正则表达式中的类型错误
  content = content.replace(/(\.replace\s*\()([^)]+)(\))\s*:\s*any\s*,\s*function\s*\((.*?)\)\s*:\s*void\s*{/g, '$1$2$3, function() {')

  // 为store actions中的方法添加类型
  if (content.includes('actions')) {
    content = content.replace(/(\w+)\((.*?)\)\s*{/g, (match, funcName, params) => {
      // 跳过已经有类型注解的函数
      if (params.includes(':')) return match

      const paramWithTypes = params
        .split(',')
        .map(param => param.trim())
        .filter(param => !!param)
        .map(param => {
          const cleanParam = param.replace(/[{}=]/g, '').trim()
          if (cleanParam.includes(':')) return param // 已有类型
          return `${cleanParam}: any`
        })
        .join(', ')

      return `${funcName}(${paramWithTypes}): void {`
    })
  }

  // 修复this.hasOwnProperty错误
  content = content.replace(/if\s*\(\s*this\.hasOwnProperty\s*\(\s*(.*?)\s*\)\s*\)\s*:\s*any\s*\)\s*:\s*void\s*{/g, 'if (Object.prototype.hasOwnProperty.call(this, $1)) {')
  content = content.replace(/if\s*\(\s*this\.hasOwnProperty\s*\(\s*(.*?)\s*\)\s*\)\s*{/g, 'if (Object.prototype.hasOwnProperty.call(this, $1)) {')

  // 修复this[key]的TypeScript错误
  content = content.replace(/this\[(.*?)\]\s*=/g, '(this as any)[$1] =')

  return content
}

// 添加文件头注释
function addFileHeader(content, filePath) {
  const fileName = path.basename(filePath)
  const timestamp = new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '')

  const header = `/*
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: ${filePath}
 * @Description: TypeScript版本 - 自动转换
 */
`

  // 如果文件已经有注释头，则不添加
  if (content.startsWith('/*')) {
    return content
  }

  return `${header}\n${content}`
}

// 为store模块创建类型定义
async function createStoreTypes() {
  const storeTypesDir = path.join(CONFIG.typesDir, 'store')

  if (!fs.existsSync(storeTypesDir)) {
    fs.mkdirSync(storeTypesDir, { recursive: true })
  }

  // 创建store类型定义
  const storeIndexContent = `/**
 * Store类型定义
 */

// 导入各个模块的类型
// import { UserState } from './user';
// import { AppState } from './app';

export interface RootState {
  // 添加各个模块
  // user: UserState;
  // app: AppState;
}
`

  fs.writeFileSync(path.join(storeTypesDir, 'index.d.ts'), storeIndexContent)
  log(`创建Store类型定义: ${storeTypesDir}/index.d.ts`, 'success')
}

// 为API模块创建类型定义
async function createApiTypes() {
  const apiTypesDir = path.join(CONFIG.typesDir, 'api')

  if (!fs.existsSync(apiTypesDir)) {
    fs.mkdirSync(apiTypesDir, { recursive: true })
  }

  // 创建通用API响应类型
  const apiResponseContent = `/**
 * API响应类型定义
 */

export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

export interface ApiListResponse<T = any> {
  code: number;
  msg: string;
  data: {
    list: T[];
    total: number;
    pageNum: number;
    pageSize: number;
  };
}

export interface ApiPageParams {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}
`

  fs.writeFileSync(path.join(apiTypesDir, 'index.d.ts'), apiResponseContent)
  log(`创建API类型定义: ${apiTypesDir}/index.d.ts`, 'success')
}

// 创建第三方库声明文件
async function createThirdPartyDeclares() {
  // 创建声明文件
  const libDefsContent = `/**
 * 第三方库类型声明
 */

// UI库
declare module 'element-plus/es/locale/lang/zh-cn';
declare module 'mavon-editor';
declare module 'element-plus';

// 工具库
declare module 'js-cookie';
declare module 'jsencrypt';
declare module 'nprogress';
declare module 'fuse.js';
declare module 'file-saver';
declare module 'clipboard';

// Vite相关
declare module 'virtual:svg-icons-register';
declare module '@vitejs/plugin-vue';
declare module '@vitejs/plugin-vue-jsx';
declare module 'vite-plugin-mock';
declare module 'vite-plugin-svg-icons';
declare module 'vite-plugin-compression';
declare module 'unplugin-auto-import';
declare module 'unplugin-vue-setup-extend-plus';

// 其他常用库
declare module 'axios';
declare module 'eruda';
declare module 'mockjs';
`

  const libDeclarePath = path.join(CONFIG.typesDir, 'lib.d.ts')
  fs.writeFileSync(libDeclarePath, libDefsContent)
  log(`创建第三方库声明文件: ${libDeclarePath}`, 'success')
}

// 更新项目的入口文件
async function updateEntryFiles() {
  // 主文件改名
  const mainJsPath = 'src/main.js'
  const mainTsPath = 'src/main.ts'

  if (fs.existsSync(mainJsPath)) {
    let content = fs.readFileSync(mainJsPath, 'utf8')

    // 添加基本类型
    content = addBasicTypes(content, 'main.js')

    // 添加文件头注释
    content = addFileHeader(content, mainTsPath)

    // 写入TS文件
    fs.writeFileSync(mainTsPath, content)
    log(`转换入口文件: ${mainJsPath} -> ${mainTsPath}`, 'success')

    // 删除原JS文件
    fs.unlinkSync(mainJsPath)
    log(`删除原入口文件: ${mainJsPath}`, 'info')
  }

  // 权限文件改名
  const permissionJsPath = 'src/permission.js'
  const permissionTsPath = 'src/permission.ts'

  if (fs.existsSync(permissionJsPath)) {
    let content = fs.readFileSync(permissionJsPath, 'utf8')

    // 添加基本类型
    content = addBasicTypes(content, 'permission.js')

    // 添加文件头注释
    content = addFileHeader(content, permissionTsPath)

    // 写入TS文件
    fs.writeFileSync(permissionTsPath, content)
    log(`转换权限文件: ${permissionJsPath} -> ${permissionTsPath}`, 'success')

    // 删除原JS文件
    fs.unlinkSync(permissionJsPath)
    log(`删除原权限文件: ${permissionJsPath}`, 'info')
  }

  // 设置文件改名
  const settingsJsPath = 'src/settings.js'
  const settingsTsPath = 'src/settings.ts'

  if (fs.existsSync(settingsJsPath)) {
    let content = fs.readFileSync(settingsJsPath, 'utf8')

    // 添加基本类型
    content = addBasicTypes(content, 'settings.js')

    // 添加文件头注释
    content = addFileHeader(content, settingsTsPath)

    // 写入TS文件
    fs.writeFileSync(settingsTsPath, content)
    log(`转换设置文件: ${settingsJsPath} -> ${settingsTsPath}`, 'success')

    // 删除原JS文件
    fs.unlinkSync(settingsJsPath)
    log(`删除原设置文件: ${settingsJsPath}`, 'info')
  }
}

// 更新vite配置
async function updateViteConfig() {
  const viteConfigPath = 'vite.config.js'
  const viteConfigTsPath = 'vite.config.ts'

  if (fs.existsSync(viteConfigPath)) {
    let content = fs.readFileSync(viteConfigPath, 'utf8')

    // 添加基本类型
    content = addBasicTypes(content, 'vite.config.js')

    // 添加文件头注释
    content = addFileHeader(content, viteConfigTsPath)

    // 写入TS文件
    fs.writeFileSync(viteConfigTsPath, content)
    log(`转换Vite配置: ${viteConfigPath} -> ${viteConfigTsPath}`, 'success')

    // 不删除原JS文件，避免出问题
    log(`保留原Vite配置文件: ${viteConfigPath}`, 'warn')
  }
}

// 更新package.json中的入口点
async function updatePackageJson() {
  const packageJsonPath = 'package.json'

  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

    // 备份原始package.json
    fs.writeFileSync(`${packageJsonPath}.backup`, JSON.stringify(packageJson, null, 2))
    log(`备份package.json到${packageJsonPath}.backup`, 'info')

    // 更新script配置，确保使用ts入口文件
    if (packageJson.scripts) {
      for (const [key, value] of Object.entries(packageJson.scripts)) {
        if (value.includes('vite') && !value.includes('--config')) {
          packageJson.scripts[key] = value + ' --config vite.config.ts'
        }
      }

      // 写入更新后的package.json
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
      log(`更新package.json入口点配置`, 'success')
    }
  }
}

// 更新HTML入口文件
async function updateHtmlEntry() {
  const htmlPath = 'index.html'

  if (fs.existsSync(htmlPath)) {
    let content = fs.readFileSync(htmlPath, 'utf8')

    // 更新脚本入口点
    const updatedContent = content.replace(/<script type="module" src="\/src\/main\.js"><\/script>/, '<script type="module" src="/src/main.ts"></script>')

    if (content !== updatedContent) {
      fs.writeFileSync(htmlPath, updatedContent)
      log(`更新HTML入口文件引用: ${htmlPath}`, 'success')
    }
  }
}

// 主函数
async function main() {
  try {
    log('开始项目TypeScript迁移', 'info')

    // 检查并创建类型定义目录
    await ensureTypesDirectory()

    // 创建store类型定义
    await createStoreTypes()

    // 创建API类型定义
    await createApiTypes()

    // 创建第三方库声明文件
    await createThirdPartyDeclares()

    // 查找需要迁移的文件
    const filesToMigrate = await findFilesToMigrate()
    log(`找到 ${filesToMigrate.length} 个文件需要迁移`, 'info')

    // 转换文件
    for (const file of filesToMigrate) {
      await convertJsToTs(file)
    }

    // 更新入口文件
    await updateEntryFiles()

    // 更新vite配置
    await updateViteConfig()

    // 更新package.json
    await updatePackageJson()

    // 更新HTML入口文件
    await updateHtmlEntry()

    log('项目TypeScript迁移完成!', 'success')
    log('请注意检查转换后的文件，并修复可能的类型错误', 'warn')
  } catch (error) {
    log(`迁移出错: ${error.message}`, 'error')
    console.error(error)
  }
}

// 执行主函数
main()
