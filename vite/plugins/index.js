/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-15 16:09:17
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-01-16 11:07:48
 * @FilePath: /miaobi-admin-magic-touch/vite/plugins/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import { viteMockServe } from "vite-plugin-mock";

import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import createSetupExtend from "./setup-extend";

export default function createVitePlugins(viteEnv, isBuild = false, command) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    viteMockServe({
      localEnabled: command === "serve", // 保证开发阶段可以使用 mock 接口
      mockPath: "mock", // 模拟数据的路径
      logger: true, // 是否在控制台输出日志
      supportTs: false, // 是否支持 TypeScript
      injectCode: `
        import { setupMockServer } from '@/mock/index';
        setupMockServer();
      `,
    }),
  ];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}
