/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-16 10:15:05
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-01-16 11:19:01
 * @FilePath: /miaobi-admin-magic-touch/src/mock/application.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Mock from "mockjs";

export const application = [
  {
    url: "/monitor/application/list",
    method: "get",
    response: () => {
      return {
        code: 200,
        message: "success",
        data: Mock.mock({
          "list|10": [
            {
              "id|+1": 1,
              name: "@cname",
              "age|18-30": 1,
              email: "@email",
              status: 1,
            },
          ],
        }),
      };
    },
  },
];
