/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-16 11:08:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-01-16 11:19:42
 * @FilePath: /miaobi-admin-magic-touch/src/mock/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/mock/index.js
import Mock from "mockjs";
import { application } from "./modules/application";

const { mock } = Mock;

mock(application);
export function setupMockServer() {
  // 这里可以添加一些全局的 Mock 配置
}
