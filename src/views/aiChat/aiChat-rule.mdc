---
description:
globs:
alwaysApply: false
---
Rule Name: aiChat-rule
Description: 
Rules specifically tailored for Vue components within the src/views/aiChat directory, focusing on TIM SDK integration, state management, and API interaction patterns observed in this module.

### Coding Environment
Technologies used:
- Vue 3 Composition API + <script setup>
- Element Plus 2.x components
- TypeScript 5.x with strict mode
- Pinia 2.x state management (via useChatStore)
- Tencent TIM Chat SDK (TUIChatEngine)
- Vite 5.x build system

### Code Implementation Guidelines
1. Component Structure:
   - Mandatory <script setup lang="ts"> syntax.
   - PascalCase component names.
   - Auto-import Element Plus components.
   - Use '@/...' aliases for imports (components, stores, api, types).
   - Define TypeScript interfaces in /types or locally if specific.

2. Element Plus Rules:
   - Icons must use @element-plus/icons-vue shorthand (e.g. <el-icon><MagicStick /></el-icon>).
   - Follow general accessibility rules (aria-labels, keyboard nav).
   - Confirm destructive actions with ElMessageBox.

3. TypeScript Standards:
   - Strict type definitions for all variables, props, and functions.
   - Prohibit 'any' type. Use 'unknown' with type guards or @ts-expect-error for unavoidable cases (especially with third-party libs like TIM SDK if types are missing).
   - Explicitly type data from TUIStore.getData().
   - Define props using defineProps<T>().

4. State Management (Pinia - useChatStore):
   - Access store via useChatStore().
   - Read state via store properties (e.g., chatStore.groupId).
   - NEVER mutate state directly. Use actions (e.g., chatStore.setGroupId(...)).
   - Store actions must return Promise<void>.

5. API Calls (@/api/aiChat, @/api/common):
   - Encapsulate API logic in @/api modules.
   - Use async/await with try...catch for robust error handling (as seen in getAiChatSign).
   - Consider Zod for API response validation.

### Cloud Integrations (Tencent TIM Chat SDK)
   - Initialization/Login:
     - Use environment variable VITE_APP_TENCENT_IM_SDK_APP_ID.
     - Call TUIChatEngine.login() with SDKAppID, userID, userSig.
     - Ensure useUploadPlugin: true is set during login.
     - Handle login success and errors robustly.
   - Readiness Check:
     - ALWAYS check TUIChatEngine.isReady() before calling SDK methods after initialization.
     - Use polling (setTimeout/setInterval) if needed to wait for readiness.
   - Service Usage:
     - Utilize TUIChatService (e.g., clearHistoryMessage).
     - Utilize TUIConversationService (e.g., switchConversation).
   - Store Interaction:
     - Access TIM state via TUIStore.getData(StoreName.CONV | StoreName.CHAT, ...).
     - Update TIM state via TUIStore.update(...).
     - **Crucially**: Add proper type definitions for data retrieved from TUIStore.
   - Custom Messages:
     - Use the sendCustomMessage utility for sending custom business logic messages.
   - Connection Handling: Implement handlers for TIM connection state changes (e.g., KICKED_OUT, LOGIN_SUCCESS, LOGIN_FAILED).

### Security Protocols
   - No hardcoded credentials (API Keys, TIM SDKAppID should be from env).
   - LocalStorage:
     - The keys PROGRESS_STORAGE_KEY, T_STORAGE_KEY, COMPLETED_VIDEOS_KEY are used in index.vue.
     - **Evaluate**: Determine if the data stored under these keys is sensitive.
     - **Mandatory**: If sensitive, encrypt data using CryptoJS BEFORE storing in localStorage and decrypt AFTER retrieval.
     - Avoid raw localStorage for tokens or user credentials.
   - Input Sanitization: If handling user-generated HTML content in the future, use DOMPurify.

### Accessibility Requirements
   - Apply general project accessibility standards to any interactive elements.
   - Ensure adequate color contrast.

### Code Quality Checklist
   - Verify dynamic imports for heavy components if applicable.
   - Ensure console.log/debug are removed before production builds.
   - Use handleX naming for event handlers.
   - Ensure proper .d.ts declarations if creating reusable modules/types.
   - Keep SCSS scoped unless intentionally global.

### Automatic Code Patterns
   - Suggest accessibility props for <el-*> elements.
   - Wrap API calls in try/catch blocks.
   - Suggest typing for TUIStore.getData().
   - Suggest encryption wrappers for localStorage access if keys match known sensitive patterns.

### Prohibited Practices
   - NEVER allow hardcoded API keys/credentials.
   - NEVER use v-for without :key.
   - NEVER use raw localStorage for sensitive data (see Security Protocols).
   - NEVER allow uncontrolled file inputs (implement proper validation/OSS upload).
   - NEVER allow direct store state mutation (use actions).
   - AVOID using implicit 'any'.

### Exception Protocol
Immediately stop and ask if:
- Missing TIM connection state handlers are detected.
- Unencrypted sensitive data is detected in localStorage (PROGRESS_STORAGE_KEY, T_STORAGE_KEY, COMPLETED_VIDEOS_KEY need verification).
- TypeScript type coverage appears low, especially around TIM SDK interactions.
- Found non-Aliyun OSS/unvalidated file uploads (if applicable in the future).
- Missing i18n structure placeholders (if i18n is used).
