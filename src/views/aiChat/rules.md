# src/views/aiChat 文件夹规则

本文档总结了 `src/views/aiChat` 文件夹下 Vue 组件开发应遵循的核心规则和最佳实践，结合了项目通用规范 (`miaobi-admin-magin-touch-rule`) 和当前目录下组件 (`index.vue`) 的具体实现。

## 1. Vue 3 & TypeScript

- **强制使用 `<script setup lang="ts">`**: 所有组件必须使用此语法。
- **Composition API**: 优先使用 `ref`, `reactive`, `computed`, `watch`, `onMounted` 等 Composition API 函数。
- **类型安全**:
  - 所有变量、函数参数和返回值都应有明确的 TypeScript 类型定义。
  - **禁止使用 `any` 类型**。如遇第三方库类型缺失或复杂类型难以定义，优先使用 `unknown` 并配合 `@ts-expect-error` 或类型断言，并在注释中说明原因。
  - `index.vue` 中 `TUIStore.getData` 返回值需要明确类型定义，避免使用隐式 `any`。
  - API 响应数据应使用 `/types` 目录下的接口或 Zod Schema 进行定义和验证。
- **组件导入**: 使用 `@/` 别名进行组件、工具函数和类型导入。
  - 示例: `import Chat from '@/components/Chat/index.vue'`
  - 示例: `import useChatStore from '@/store/modules/chat'`
  - 示例: `import { getAiChatSign } from '@/api/aiChat'`

## 2. Element Plus

- **自动导入**: 利用 `unplugin-auto-import` 自动导入 Element Plus 组件和图标。
- **图标使用**: 必须使用 `@element-plus/icons-vue` 提供的图标组件。
  - 示例: `<el-icon><MagicStick /></el-icon>`
- **无障碍性 (Accessibility)**:
  - 未来添加表单、表格、按钮等交互元素时，必须遵循项目规范，添加必要的 `aria-*` 属性和键盘导航支持。
  - 使用 `ElMessageBox` 确认潜在的破坏性操作。

## 3. 状态管理 (Pinia)

- **Store 交互**: 通过 `useChatStore()` Hook 获取 Store 实例。
- **状态访问**: 通过 `store.state` 或 `store.getters` 访问状态。
- **状态修改**: **严禁直接修改 Store State**。必须通过调用 Store Actions (`store.actionName()`) 来进行异步或同步的状态变更。
  - 示例: `chatStore.setGroupId(res?.data?.groupId)`
- **Action 返回值**: Store Actions 应返回 `Promise<void>` 以便进行链式调用或错误处理。

## 4. API 调用

- **封装 API 请求**: API 请求应封装在 `@/api` 目录下对应的模块中。
- **错误处理**: 所有 API 调用必须包含健壮的错误处理逻辑，通常使用 `try...catch` 或 Promise 的 `.catch()` 方法。
  - `index.vue` 中的 `getAiChatSign` 使用了 `try...catch`。
  - `clearAllMessage` 在 Promise 链中处理错误。建议统一使用 `async/await` 和 `try...catch` 以提高可读性。
- **响应验证**: 推荐使用 Zod 对 API 响应进行结构和类型验证，确保数据一致性。

## 5. 腾讯 TIM 集成

- **初始化与登录**:
  - 使用环境变量配置 `SDKAppID`: `import.meta.env.VITE_APP_TENCENT_IM_SDK_APP_ID`。
  - 调用 `TUIChatEngine.login` 进行登录，并处理登录成功和失败的情况。
  - 确保 `useUploadPlugin: true` 已设置，以启用文件上传插件。
- **状态检查**: 在执行依赖 TIM SDK 的操作前，检查 `TUIChatEngine.isReady()` 状态。
- **服务使用**: 使用 `TUITranslateService`, `TUIChatService`, `TUIConversationService` 提供的服务进行相关操作（如清空消息、切换会话）。
- **Store 交互**: 通过 `TUIStore` 访问和更新 TIM 内部状态 (`StoreName.CONV`, `StoreName.CHAT`)。注意处理 `TUIStore.getData` 返回值的类型。
- **自定义消息**: 使用封装的 `sendCustomMessage` 工具函数发送自定义消息。

## 6. 安全性

- **禁止硬编码密钥**: 严禁在代码中硬编码任何 API Key、Secret 或其他敏感凭证。应使用环境变量或安全的配置管理方式。
- **本地存储**:
  - **禁止直接使用 `localStorage` 或 `sessionStorage` 存储敏感信息** (如 Token、用户私密数据等)。
  - 如确需存储敏感信息，必须先使用 `CryptoJS` 等库进行加密。
  - `index.vue` 中使用了 `localStorage.getItem/setItem/removeItem`。需要评估 `PROGRESS_STORAGE_KEY`, `T_STORAGE_KEY`, `COMPLETED_VIDEOS_KEY` 存储的数据是否敏感，如果是，则需要加密。
- **输入清理**: 对于用户输入或富文本内容，未来若有相关功能，需使用 `DOMPurify` 进行清理，防止 XSS 攻击。

## 7. 代码质量与风格

- **命名规范**:
  - 组件名: PascalCase (`AiChatIndex.vue`)
  - 变量、函数名: camelCase (`getQueryValue`, `imUserId`)
  - 常量: 大写下划线 (`PROGRESS_STORAGE_KEY`)
  - 事件处理函数: `handleX` 格式 (如 `handleClick`)
- **注释**: 对复杂逻辑、特殊处理或意图不明显的代码添加必要的注释。
- **调试信息**: **生产构建前必须移除所有 `console.log`, `console.debug` 等调试语句**。可以使用 `console.error` 或 `console.warn` 记录关键错误或警告。
- **SCSS**: 使用 SCSS 编写样式，利用其嵌套、变量等特性。使用 `scoped` 限制样式作用域。

## 8. 禁止的做法

- **禁止 `v-for` 不带 `:key`**。
- **禁止直接操作 DOM** (除非必要，如集成第三方库)。优先使用 Vue 的响应式系统和模板引用。
- **禁止包含未测试或未完成的功能代码**。
- **禁止引入未经验证或来源不明的第三方依赖**。

请在开发 `src/views/aiChat` 目录下的新组件或修改现有组件时，严格遵守以上规则。
