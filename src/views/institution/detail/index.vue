<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础信息</span>
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdateBasicInfo"
            v-hasPermi="['system:organization:edit']"
          >编辑</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">机构ID：</span>
            <span class="info-content">{{ institutionInfo.id }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">机构名称：</span>
            <span class="info-content">{{ institutionInfo.orgName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">机构邮箱：</span>
            <span class="info-content">{{ institutionInfo.orgEmail }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">机构类型：</span>
            <span class="info-content">
              <dict-tag :options="organizationt_ype" :value="institutionInfo.orgTypeId" />
            </span>
          </div>
        </el-col>
      </el-row>
      <el-divider content-position="left">机构负责人</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">姓名：</span>
            <span class="info-content">{{ institutionInfo.leader }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">职位：</span>
            <span class="info-content">{{ institutionInfo.leaderPosition }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">电话：</span>
            <span class="info-content">{{ institutionInfo.leaderPhone }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">邮箱：</span>
            <span class="info-content">{{ institutionInfo.leaderEmail }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">微信：</span>
            <span class="info-content">{{ institutionInfo.leaderWx }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="box-card mt20">
      <template #header>
        <div class="card-header">
          <span>账号管理</span>
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdateAccount"
            v-hasPermi="['system:organization:edit']"
          >修改密码</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">账号：</span>
            <span class="info-content">{{ institutionInfo.account }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <!-- <span class="info-label">密码：</span> -->
            <span class="info-content">
              <password :password="institutionInfo.passWord" :id="institutionInfo.id" :account="institutionInfo.account"/>
            </span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">角色：</span>
            <span class="info-content">机构管理员</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">账号状态：</span>
            <span class="info-content">
              <dict-tag :options="account_status" :value="institutionInfo.status" />
            </span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="box-card mt20">
      <template #header>
        <div class="card-header">
          <span>机构课时</span>
          <el-button
            type="primary"
            link
            icon="Plus"
            @click="handleAddCredits"
            v-hasPermi="['org:hour:add']"
          >添加课时</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="info-item">
            <span class="info-label">累计剩余课时：</span>
            <span class="info-content">{{ `${institutionInfo?.classHourInfo?.remain}课时     ${institutionInfo?.classHourInfo?.remainTime}` }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="info-item">
            <span class="info-label">累计消耗课时： </span>
            <span class="info-content">{{ `${institutionInfo?.classHourInfo?.consume}课时     ${institutionInfo?.classHourInfo?.consumeTime}` }}</span>
          </div>
        </el-col>
      </el-row>
      <!-- <el-table v-loading="creditsLoading" :data="creditsList" stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="creditsCount" label="课时数量" min-width="120" align="center" />
        <el-table-column prop="creditsType" label="课时类型" min-width="120" align="center">
          <template #default="scope">
            <dict-tag :options="credits_type" :value="scope.row.creditsType" />
          </template>
        </el-table-column>
        <el-table-column prop="validityPeriod" label="有效期" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.validityStartTime) }} 至 {{ formatDate(scope.row.validityEndTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" min-width="120" align="center" />
        <el-table-column prop="remark" label="备注" min-width="200" :show-overflow-tooltip="true" />
      </el-table>
      <pagination
        v-show="creditsTotal > 0"
        :total="creditsTotal"
        v-model:page="creditsQueryParams.pageNum"
        v-model:limit="creditsQueryParams.pageSize"
        @pagination="getCredits"
      /> -->
    </el-card>

    <!-- 编辑基础信息对话框 -->
    <el-dialog
      :title="basicInfoDialog.title"
      v-model="basicInfoDialog.open"
      width="650px"
      append-to-body
    >
      <el-form
        ref="basicInfoFormRef"
        :model="basicInfoForm"
        :rules="basicInfoRules"
        label-width="150px"
      >
        <el-row>
          <el-col :span="22" :push="2">
            <p>基础信息</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构名称" prop="orgName">
              <el-input
                v-model="basicInfoForm.orgName"
                placeholder="请输入机构名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构邮箱" prop="orgEmail">
              <el-input
                v-model="basicInfoForm.orgEmail"
                placeholder="请输入机构邮箱"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构类型" prop="orgTypeId">
              <el-select
                v-model="basicInfoForm.orgTypeId"
                placeholder="选择机构类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in organizationt_ype"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="22" :push="2">
            <p>机构负责人</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="姓名" prop="leader">
              <el-input
                v-model="basicInfoForm.leader"
                placeholder="请输入负责人姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="职位" prop="leaderPosition">
              <el-input
                v-model="basicInfoForm.leaderPosition"
                placeholder="请输入负责人职位"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="电话" prop="leaderPhone">
              <el-input
                v-model="basicInfoForm.leaderPhone"
                placeholder="请输入负责人电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="邮箱" prop="leaderEmail">
              <el-input
                v-model="basicInfoForm.leaderEmail"
                placeholder="请输入负责人邮箱"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="微信" prop="leaderWx">
              <el-input
                v-model="basicInfoForm.leaderWx"
                placeholder="请输入负责人微信"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBasicInfoForm">确 定</el-button>
          <el-button @click="cancelBasicInfo">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑账号信息对话框 -->
    <el-dialog
      :title="accountDialog.title"
      v-model="accountDialog.open"
      width="650px"
      append-to-body
    >
      <el-form
        ref="accountFormRef"
        :model="accountForm"
        :rules="accountRules"
        label-width="150px"
      >
        <el-row>
          <el-col :span="20">
            <el-form-item label="原密码" prop="oldPwd">
              <el-input
                v-model="accountForm.oldPwd"
                placeholder="请输入原密码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="新密码" prop="newPwd">
              <el-input
                v-model="accountForm.newPwd"
                placeholder="请输入新密码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAccountForm">确 定</el-button>
          <el-button @click="cancelAccount">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加课时对话框 -->
    <el-dialog
      title="添加课时"
      v-model="creditsDialog.open"
      width="650px"
      append-to-body
    >
      <el-form
        ref="creditsFormRef"
        :model="creditsForm"
        :rules="creditsRules"
        label-width="150px"
      >
        <el-row>
          <el-col :span="20">
            <el-form-item label="新增课时数" prop="classHour">
              <el-input-number
                v-model="creditsForm.classHour"
                :min="1"
                :max="99999"
                placeholder="请输入课时数量"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="课时类型" prop="creditsType">
              <el-select
                v-model="creditsForm.creditsType"
                placeholder="选择课时类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in credits_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="20">
            <el-form-item label="有效期" prop="validityPeriod">
              <el-date-picker
                v-model="creditsForm.validityPeriod"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col :span="20">
            <el-form-item label="新增课时说明" prop="remark">
              <el-input
                v-model="creditsForm.remark"
                type="textarea"
                placeholder="请输入新增课时说明"
                :rows="3"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCreditsForm">确 定</el-button>
          <el-button @click="cancelCredits">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="institution-detail">
import { useRoute } from "vue-router";
import { getOrganization, updateOrganization, addClassHour, updatePwd } from "@/api/organization";
import { formatDate } from "@/utils/index";
import password from '../list/password.vue';

const route = useRoute();
const { proxy } = getCurrentInstance();
const { org_type: organizationt_ype, account_status, credits_type } = proxy.useDict("org_type", "account_status", "credits_type");

// 机构基本信息
const institutionInfo = ref({});
const loading = ref(true);

// 基础信息对话框相关变量
const basicInfoDialog = ref({
  open: false,
  title: "编辑基础信息"
});
const basicInfoFormRef = ref();
const basicInfoForm = ref({});
const basicInfoRules = {
  orgName: [
    { required: true, message: "机构名称不能为空", trigger: "blur" }
  ],
  orgEmail: [
    { required: true, message: "机构邮箱不能为空", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }
  ],
  orgTypeId: [
    { required: true, message: "机构类型不能为空", trigger: "change" }
  ],
  leader: [
    { required: true, message: "负责人姓名不能为空", trigger: "blur" }
  ],
  leaderPhone: [
    { required: true, message: "负责人电话不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  // leaderEmail: [
  //   { required: true, message: "负责人邮箱不能为空", trigger: "blur" },
  //   { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }
  // ]
};

// 账号管理对话框相关变量
const accountDialog = ref({
  open: false,
  title: "修改密码"
});
const accountFormRef = ref();
const accountForm = ref({});
const accountRules = {
  oldPwd: [
    { required: true, message: "密码不能为空", trigger: "blur" }
  ],
  newPwd: [
    { required: true, message: "密码不能为空", trigger: "change" }
  ]
};

// 课时管理相关变量
const creditsList = ref([]);
const creditsTotal = ref(0);
const creditsLoading = ref(false);
const creditsQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  organizationId: undefined
});

// 添加课时对话框相关变量
const creditsDialog = ref({
  open: false
});
const creditsFormRef = ref();
const creditsForm = ref({});
const creditsRules = {
  classHour: [
    { required: true, message: "课时数量不能为空", trigger: "blur" }
  ],
  remark: [
    { required: true, message: "课时说明不能为空", trigger: "change" }
  ],
};

/** 查询机构详细信息 */
function getInstitutionInfo() {
  loading.value = true;
  const organizationId = route.query.id;
  if (!organizationId) {
    proxy.$modal.msgError("未找到机构ID");
    return;
  }
  
  getOrganization(organizationId).then(response => {
    institutionInfo.value = response.data;
    loading.value = false;
    
    // 设置课时查询参数中的机构ID
    // creditsQueryParams.value.organizationId = institutionInfo.value.id;
    // 查询课时列表
    // getCredits();
  });
}

/** 查询机构课时列表 */
function getCredits() {
  creditsLoading.value = true;
  // 这里模拟调用课时列表接口，实际项目中需要替换为真实API
  // 模拟数据，实际中应该调用对应的API
  setTimeout(() => {
    creditsList.value = [
      {
        id: 1,
        creditsCount: 100,
        creditsType: '1',
        validityStartTime: Date.now() - 86400000 * 30, // 30天前
        validityEndTime: Date.now() + 86400000 * 365, // 365天后
        createTime: Date.now() - 86400000 * 30,
        createBy: '管理员',
        remark: '初始赠送课时'
      },
      {
        id: 2,
        creditsCount: 50,
        creditsType: '2',
        validityStartTime: Date.now() - 86400000 * 15, // 15天前
        validityEndTime: Date.now() + 86400000 * 180, // 180天后
        createTime: Date.now() - 86400000 * 15,
        createBy: '管理员',
        remark: '活动赠送课时'
      }
    ];
    creditsTotal.value = 2;
    creditsLoading.value = false;
  }, 500);
}

/** 打开编辑基础信息对话框 */
function handleUpdateBasicInfo() {
  basicInfoDialog.value.open = true;
  basicInfoForm.value = {
    id: institutionInfo.value.id,
    orgName: institutionInfo.value.orgName,
    orgEmail: institutionInfo.value.orgEmail,
    orgTypeId: institutionInfo.value.orgTypeId + '',
    leader: institutionInfo.value.leader,
    leaderPosition: institutionInfo.value.leaderPosition,
    leaderPhone: institutionInfo.value.leaderPhone,
    leaderEmail: institutionInfo.value.leaderEmail,
    leaderWx: institutionInfo.value.leaderWx
  };
}

/** 提交基础信息表单 */
function submitBasicInfoForm() {
  basicInfoFormRef.value.validate(valid => {
    if (valid) {
      // 保留原有的账号相关信息
      const formData = {
        ...basicInfoForm.value,
        account: institutionInfo.value.account,
        passWord: institutionInfo.value.passWord,
        roleId: institutionInfo.value.roleId,
        status: institutionInfo.value.status
      };
      
      updateOrganization(formData).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        basicInfoDialog.value.open = false;
        getInstitutionInfo();
      });
    }
  });
}

/** 取消编辑基础信息 */
function cancelBasicInfo() {
  basicInfoDialog.value.open = false;
}

/** 打开编辑账号信息对话框 */
function handleUpdateAccount() {
  accountDialog.value.open = true;
  // accountForm.value = {
  //   id: institutionInfo.value.id,
  //   account: institutionInfo.value.account,
  //   passWord: institutionInfo.value.passWord,
  //   roleId: institutionInfo.value.roleId || 101,
  //   status: institutionInfo.value.status
  // };
}

/** 提交账号信息表单 */
function submitAccountForm() {
  accountFormRef.value.validate(valid => {
    if (valid) {
      // 保留原有的基础信息
      // const formData = {
      //   ...accountForm.value,
      //   orgName: institutionInfo.value.orgName,
      //   orgEmail: institutionInfo.value.orgEmail,
      //   orgTypeId: institutionInfo.value.orgTypeId,
      //   leader: institutionInfo.value.leader,
      //   leaderPosition: institutionInfo.value.leaderPosition,
      //   leaderPhone: institutionInfo.value.leaderPhone,
      //   leaderEmail: institutionInfo.value.leaderEmail,
      //   leaderWx: institutionInfo.value.leaderWx
      // };
      
      updatePwd({
        ...accountForm.value,
        id: institutionInfo.value.id
      }).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        accountDialog.value.open = false;
        getInstitutionInfo();
      });
    }
  });
}

/** 取消编辑账号信息 */
function cancelAccount() {
  accountDialog.value.open = false;
}

/** 打开添加课时对话框 */
function handleAddCredits() {
  creditsDialog.value.open = true;
  creditsForm.value = {
    orgId: institutionInfo.value.id,
    classHour: 1,
    remark: ''
  };
}

/** 提交添加课时表单 */
function submitCreditsForm() {
  creditsFormRef.value.validate(valid => {
    if (valid) {
      // 这里需要格式化日期并构造提交的数据
      const formData = {
        ...creditsForm.value,
      };
      addClassHour(formData).then(response => {
        proxy.$modal.msgSuccess("添加课时成功");
        creditsDialog.value.open = false;
        getInstitutionInfo();
        // getCredits();
      })
    }
  });
}

/** 取消添加课时 */
function cancelCredits() {
  creditsDialog.value.open = false;
}

// 在页面初始化时获取机构信息
getInstitutionInfo();
</script>

<style scoped>
.mt20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.info-label {
  min-width: 80px;
  font-weight: bold;
  color: #606266;
}

.info-content {
  flex: 1;
  word-break: break-all;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 