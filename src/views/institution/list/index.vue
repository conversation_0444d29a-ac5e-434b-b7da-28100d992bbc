<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="机构ID" prop="id">
        <el-input v-model="queryParams.id" placeholder="请输入机构ID" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="机构名称" prop="orgName">
        <el-input v-model="queryParams.orgName" placeholder="请输入机构名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="机构类型" prop="orgTypeId">
        <el-select v-model="queryParams.orgTypeId" placeholder="机构类型" clearable style="width: 200px">
          <el-option v-for="dict in organizationt_ype" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="账号状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="账号状态" clearable style="width: 200px">
          <el-option v-for="dict in account_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="createDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="编辑时间" style="width: 308px">
        <el-date-picker
          v-model="editDate"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:organization:add']">添加机构</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
            >展开/折叠</el-button
          >
        </el-col> -->
      <!-- <right-toolbar
        :showSearch="showSearch"
        :search="false"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="applicationList" row-key="id">
      <el-table-column prop="id" label="机构ID" width="80" :fixed="true"></el-table-column>
      <el-table-column prop="orgName" label="机构名称" width="160" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="applicationType" label="机构负责人" width="180">
        <template #default="scope">
          <div>
            <el-space>
              <div>{{ scope.row.leader }}</div>
              <div>&#x2022;</div>
              <div>{{ scope.row.leaderPosition }}</div>
            </el-space>
            <div>{{ scope.row.leaderPhone }}</div>
            <div>{{ scope.row.leaderEmail }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="机构类型" width="160">
        <template #default="scope">
          <dict-tag :options="organizationt_ype" :value="scope.row.orgTypeId" />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="账号信息" width="160">
        <template #default="scope">
          <div>{{ `账号：${scope.row.account}` }}</div>
          <password :password="scope.row.passWord" :id="scope.row.id" :account="scope.row.account" />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="剩余课时/消耗课时" width="160">
        <template #default="scope">
          <div>{{ `${scope.row.remainClassHour}/${scope.row.consumeClassHour}` }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="账号状态" width="100" align="center">
        <template #default="scope">
          <dict-tag :options="account_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="编辑时间" align="center" prop="updateTime" min-width="200">
        <template #default="scope">
          <span>{{ formatDate(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="200">
        <template #default="scope">
          <span>{{ formatDate(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="210" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row, 'edit')" v-hasPermi="['system:organization:edit']">编辑</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row, 'check')" v-hasPermi="['system:organization:detail']">查看详情</el-button>
          <el-button link type="primary" icon="Sort" @click="handleChangeStatus(scope.row)" v-hasPermi="['system:organization:edit']">{{ scope?.row.status !== '0' ? '启用' : '禁用' }}</el-button>
          <!-- <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form ref="applicationRef" :model="form" :rules="rules" label-width="150px" :disabled="title === '查看机构'">
        <el-row>
          <el-col :span="22" :push="2">
            <p>基础信息</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构名称" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请输入机构名称" />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构邮箱" prop="orgEmail">
              <el-input v-model="form.orgEmail" placeholder="请输入机构邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="机构类型" prop="orgTypeId">
              <el-select v-model="form.orgTypeId" placeholder="选择机构类型" clearable>
                <el-option v-for="dict in organizationt_ype" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="22" :push="2">
            <p>机构负责人</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="姓名" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="职位" prop="leaderPosition">
              <el-input v-model="form.leaderPosition" placeholder="请输入负责人职位"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="电话" prop="leaderPhone">
              <el-input v-model="form.leaderPhone" placeholder="请输入负责人电话"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="邮箱" prop="leaderEmail">
              <el-input v-model="form.leaderEmail" placeholder="请输入负责人邮箱"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="微信" prop="leaderWx">
              <el-input v-model="form.leaderWx" placeholder="请输入负责人微信"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="22" :push="2">
            <p>机构账号</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="账号" prop="account">
              <el-input v-model="form.account" placeholder="默认为机构邮箱"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="密码" prop="passWord">
              <el-input
                v-model="form.passWord"
                placeholder="请输入密码"
                :disabled="title !== '添加机构'"
              ></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="20">
            <el-form-item label="角色" prop="roleId">
              <el-select v-model="form.roleId" placeholder="选择角色" disabled clearable>
                <el-option
                  v-for="dict in [
                    {
                      label: '机构管理员',
                      value: 101,
                    },
                  ]"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="账号状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer v-if="title !== '查看机构'">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="institution-list">
import { useRouter } from 'vue-router'
import { listOrganization, addOrganization, updateOrganization } from '@/api/organization'

import { formatDate } from '@/utils/index'
import password from './password.vue'
import { date2timeStamp } from '../../../utils'
import { dayjs } from 'element-plus'
const { proxy } = getCurrentInstance()
const router = useRouter()
const applicationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const refreshTable = ref(true)
const createDate = ref([])
const editDate = ref([])
const total = ref(0)
const data = reactive({
  form: {
    roleId: 101,
  },
  queryParams: {
    visible: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
    orgEmail: [{ required: true, message: '请输入机构邮箱', trigger: 'blur' }],
    orgTypeId: [{ required: true, message: '请选择机构类型', trigger: 'blur' }],
    leader: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    leaderPhone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    roleId: [{ required: true, message: '请选择账号角色', trigger: 'blur' }],
    status: [{ required: true, message: '请选择账号状态', trigger: 'blur' }],
  },
})

const { org_type: organizationt_ype, account_status } = proxy.useDict('org_type', 'account_status')
const { queryParams, rules, statusMaps, form } = toRefs(data)

watch(
  () => form.value.orgEmail,
  (newVal, oldVal) => {
    if (title.value === '编辑机构') {
      return
    }
    if (newVal && !oldVal && form.value.account && form.value.account !== newVal) {
      return
    }
    form.value.account = newVal
  }
)

/** 查询菜单列表 */
function getList() {
  loading.value = true
  console.log('createDate?.value', createDate?.value)
  listOrganization({
    ...queryParams.value,
    createStartTime: date2timeStamp(createDate?.value[0]),
    // createEndTime: date2timeStamp(createDate?.value[1] ? dayjs(createDate?.value[1]).endOf("day") : undefined),
    createEndTime: date2timeStamp(createDate?.value[1]),
    updateStartTime: date2timeStamp(editDate?.value[0]),
    // updateEndTime: date2timeStamp(editDate?.value[1] ? dayjs(editDate?.value[1]).endOf("day") : undefined),
    updateEndTime: date2timeStamp(editDate?.value[1]),
  }).then(response => {
    applicationList.value = response?.rows
    loading.value = false
    total.value = response.total
  })
}

/** 渲染状态 */
function getStatusLabel(scope) {
  return statusMaps?.value?.find(item => item?.value === scope?.row?.status)?.label
}
/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    roleId: 101,
  }
  proxy.resetForm('applicationRef')
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  createDate.value = []
  editDate.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset()
  open.value = true
  title.value = '添加机构'
}

/** 修改按钮操作 */
async function handleUpdate(row, type) {
  if (type === 'check') {
    router.push(`/institution/detail?id=${row.id}`)
    return
  }
  // if (type === 'edit') {
  open.value = true
  title.value = type === 'edit' ? '编辑机构' : '查看机构'
  form.value = {
    ...row,
    orgTypeId: row.orgTypeId + '',
  }
  // return
  // }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['applicationRef'].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateOrganization(form.value).then(response => {
          proxy.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        })
      } else {
        addOrganization({
          ...form.value,
          roleName: '机构管理员',
        }).then(response => {
          proxy.$modal.msgSuccess('新增成功')
          open.value = false
          getList()
          // router.push("/application/bot/" + response?.data);
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.applicationName + '"的数据项?')
    .then(function () {
      return updateApplicationContent({ id: row?.id, del: 1 })
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
  proxy.$modal
    .confirm(`是否确认${row?.status !== '0' ? '启用' : '禁用'}名称为"${row.orgName}"的数据项?`)
    .then(function () {
      return updateOrganization({
        ...row,
        status: row?.status !== '0' ? 0 : 1,
      })
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess(`${row?.status !== '0' ? '启用' : '禁用'}成功`)
    })
    .catch(() => {})
}
getList()
</script>
<style scoped>
.info-box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-start;
  width: 100%;
}
.info-box div {
  width: 100%;
  margin-bottom: 5px;
}
</style>
