<template>
  <el-space>
    <div>{{ `密码：${showPassword ? realPassword : password}` }}</div>
    <el-icon style="cursor: pointer" @click="togglePassword">
      <View v-if="showPassword" />
      <Hide v-else />
    </el-icon>
  </el-space>
  <el-space>
    <div>一键复制</div>
    <el-icon style="cursor: pointer" @click="copyPassword">
      <CopyDocument />
    </el-icon>
  </el-space>
</template>

<script setup>
import { ref } from "vue";
import { showPasswordFn } from "@/api/organization";
import { ElMessage } from "element-plus";

const props = defineProps({
  password: {
    type: String,
    required: true,
  },
  account: {
    type: String,
    required: true,
  },
  id: {
    type: [String, Number],
    required: true,
  },
});

const showPassword = ref(false);
const realPassword = ref("");

const togglePassword = () => {
  showPassword.value = !showPassword.value;
  if (!realPassword.value) {
    showPasswordFn(String(props.id)).then((res) => {
      realPassword.value = res.data.password;
    });
  }
};
const copyPassword = async () => {
  if (!realPassword.value) {
    ElMessage.error("请先查看密码");
    return;
  }
  try {
    await navigator.clipboard.writeText(`
    账号：${props.account}
    密码：${realPassword.value}`);
    ElMessage.success("文本复制成功");
  } catch (err) {
    ElMessage.error("文本复制失败");
  }
};
</script>

<style scoped>
</style>