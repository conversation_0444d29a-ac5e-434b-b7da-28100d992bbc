<template>
  <div class="schedule-container">
    <!-- 顶部导航 -->
    <div class="schedule-header">
      <div class="nav-buttons">
        <div class="nav-btn" @click="navigateWeek('prev')">上一周</div>
        <div class="nav-btn active">当周</div>
        <div class="nav-btn" @click="navigateWeek('next')">下一周</div>
      </div>
      <div class="date-range">{{ dateRange }}</div>
      <div class="view-selector" style="visibility: hidden">
        <select v-model="viewType">
          <option value="week">周</option>
          <option value="day">日</option>
        </select>
      </div>
    </div>

    <!-- 星期栏 -->
    <div class="week-header">
      <div class="time-area"></div>
      <div v-for="(day, index) in weekDays" :key="index" class="day-column">
        <div class="day-name">{{ day.name }}</div>
      </div>
    </div>
    <div class="date-header">
      <div class="time-area">GMT + 08</div>
      <div v-for="(day, index) in weekDays" :key="index" class="date-column">
        <div class="date-number" :class="{ active: day.isActive }">
          <div class="solar-date">{{ day.number }}&nbsp;&nbsp;{{ day.lunar }}</div>
          <div class="lunar-date"></div>
          <div v-if="day.festival" class="festival">{{ day.festival }}</div>
        </div>
      </div>
    </div>
    <!-- 时间表格 -->
    <div class="schedule-grid">
      <div class="time-axis">
        <div v-for="hour in 14" :key="hour" class="time-slot">{{ (hour + 7).toString().padStart(2, '0') }}:00</div>
      </div>

      <div class="schedule-content">
        <div v-for="(day, dayIndex) in 7" :key="'day-' + dayIndex" class="day-column">
          <div v-for="hour in 14" :key="'hour-' + hour" class="hour-cell"></div>
          <div
            v-if="dayIndex === 5 && mockData.saturday.length > 0"
            v-for="(course, idx) in mockData.saturday"
            :key="'saturday-' + idx"
            class="course-item"
            :style="{
              top: calculateTop(course.startTime),
              height: calculateHeight(course.startTime, course.endTime),
              backgroundColor: course.color || '#64B5F6',
            }"
          >
            <div class="course-title">{{ course.title }}</div>
            <div class="course-room">{{ course.room }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <!-- <div class="course-detail" v-if="selectedCourse" @click="closeCourseDetail">
      <div class="course-detail-content" @click.stop>
        <h3>{{ selectedCourse.title }}</h3>
        <p v-if="selectedCourse.time"><i class="icon-time"></i> {{ selectedCourse.time }}</p>
        <p v-if="selectedCourse.room"><i class="icon-room"></i> {{ selectedCourse.room }}</p>
        <p v-if="selectedCourse.teacher"><i class="icon-teacher"></i> {{ selectedCourse.teacher }}</p>
        <p v-if="selectedCourse.class"><i class="icon-class"></i> {{ selectedCourse.class }}</p>
        <p v-if="selectedCourse.desc"><i class="icon-desc"></i> {{ selectedCourse.desc }}</p>
        <div class="detail-btn" @click="closeCourseDetail">查看</div>
      </div>
    </div> -->
  </div>
</template>

<script lang="ts">
import { Lunar, Solar, HolidayUtil } from 'lunar-javascript'
export default {
  name: 'ScheduleView',
  data() {
    return {
      viewType: 'week',
      currentDate: new Date(),
      selectedCourse: null,
      // 添加基准字体大小变量
      baseFontSize: 16,
      // 模拟数据
      mockData: {
        monday: [
          {
            title: '一课 AIGC',
            desc: '课程',
            startTime: '08:00',
            endTime: '09:30',
            color: '#8BC34A',
          },
        ],
        tuesday: [
          {
            title: 'AIGC课程',
            time: '2024-01-10 周三 08:00-09:30',
            room: '206教室',
            teacher: '王非老师',
            class: '2024年小班4班',
            desc: '美育设计-花艺美学设计',
            startTime: '17:00',
            endTime: '18:30',
            color: '#64B5F6',
          },
        ],
        wednesday: [
          {
            title: 'AIGC课程',
            startTime: '15:00',
            endTime: '16:30',
            color: '#64B5F6',
          },
        ],
        saturday: [
          {
            title: 'AIGC课程',
            room: '02-4班',
            startTime: '12:30',
            endTime: '14:00',
            color: '#64B5F6',
          },
        ],
      },
    }
  },
  computed: {
    dateRange() {
      const startDate = new Date(this.currentDate)
      startDate.setDate(this.currentDate.getDate() - this.currentDate.getDay() + 1)

      const endDate = new Date(startDate)
      endDate.setDate(startDate.getDate() + 6)

      return `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}至${endDate.getFullYear()}-${String(
        endDate.getMonth() + 1
      ).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`
    },
    weekDays() {
      const days = []
      const startDate = new Date(this.currentDate)
      startDate.setDate(this.currentDate.getDate() - this.currentDate.getDay() + 1)

      const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      const today = new Date()

      // 引入农历库

      for (let i = 0; i < 7; i++) {
        const currentDay = new Date(startDate)
        currentDay.setDate(startDate.getDate() + i)

        // 获取农历信息
        const solar = Solar.fromDate(currentDay)
        const lunar = solar.getLunar()

        // 获取节日信息
        const festivals = []
        // 公历节日
        try {
          if (HolidayUtil && typeof HolidayUtil.getSolarFestival === 'function') {
            const solarFestival = HolidayUtil.getSolarFestival(solar.getMonth(), solar.getDay())
            if (solarFestival) festivals.push(solarFestival)
          }
        } catch (error) {
          console.error('Error getting solar festival:', error)
        }

        // 农历节日
        try {
          if (HolidayUtil && typeof HolidayUtil.getLunarFestival === 'function') {
            const lunarFestival = HolidayUtil.getLunarFestival(lunar.getMonth(), lunar.getDay())
            if (lunarFestival) festivals.push(lunarFestival)
          }
        } catch (error) {
          console.error('Error getting lunar festival:', error)
        }

        days.push({
          number: String(currentDay.getDate()).padStart(2, '0'),
          name: dayNames[i],
          isActive: currentDay.getDate() === today.getDate() && currentDay.getMonth() === today.getMonth() && currentDay.getFullYear() === today.getFullYear(),
          // 添加农历信息
          lunar: lunar.getDayInChinese(),
          lunarMonth: lunar.getMonthInChinese(),
          festival: festivals.length > 0 ? festivals[0] : null,
        })
      }

      return days
    },
  },
  methods: {
    navigateWeek(direction) {
      if (direction === 'prev') {
        this.currentDate.setDate(this.currentDate.getDate() - 7)
      } else if (direction === 'next') {
        this.currentDate.setDate(this.currentDate.getDate() + 7)
      }
      this.currentDate = new Date(this.currentDate)
    },
    // 添加计算基准字体大小的方法
    calculateBaseFontSize() {
      const viewportWidth = window.innerWidth
      // 基准宽度为1920px，基准字体大小为16px
      const baseWidth = 768
      const minFontSize = 11
      const maxFontSize = 16

      // 计算比例
      let fontSize = (viewportWidth / baseWidth) * maxFontSize

      // 限制最小和最大值
      fontSize = Math.max(minFontSize, Math.min(maxFontSize, fontSize))

      // 更新基准字体大小
      this.baseFontSize = fontSize

      // 更新根元素字体大小
      document.documentElement.style.fontSize = `${fontSize}px`

      // 重新计算所有使用rem的元素
      this.updateRemElements()
    },

    // 更新使用rem的元素
    updateRemElements() {
      // 这里可以添加需要更新的特定元素
    },

    // 修改计算方法使用动态基准字体大小
    calculateTop(time) {
      const [hours, minutes] = time.split(':').map(Number)
      const startHour = 8 // 8:00 AM
      const hourHeight = 3 * this.baseFontSize // 每小时高度为3rem

      const hourDiff = hours - startHour
      const minutePercentage = minutes / 60

      return `${(hourDiff + minutePercentage) * hourHeight}px`
    },

    calculateHeight(startTime, endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number)
      const [endHours, endMinutes] = endTime.split(':').map(Number)

      const hourDiff = endHours - startHours
      const minuteDiff = (endMinutes - startMinutes) / 60
      const totalHourDiff = hourDiff + minuteDiff

      const hourHeight = 3 * this.baseFontSize // 每小时高度为3rem

      return `${totalHourDiff * hourHeight}px`
    },
    showCourseDetail(course) {
      this.selectedCourse = course
    },
    closeCourseDetail() {
      this.selectedCourse = null
    },

    // 添加防抖函数
    debounce(func, wait) {
      let timeout
      return function () {
        const context = this
        const args = arguments
        clearTimeout(timeout)
        timeout = setTimeout(() => {
          func.apply(context, args)
        }, wait)
      }
    },

    // 添加窗口大小变化监听（使用防抖）
    handleResize() {
      this.debouncedCalculate()
    },
  },

  // 生命周期钩子
  created() {
    // 创建防抖版本的计算函数
    this.debouncedCalculate = this.debounce(this.calculateBaseFontSize, 200)
  },

  mounted() {
    // 初始化基准字体大小
    this.calculateBaseFontSize()

    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
  },
}
</script>

<style lang="scss" scoped>
/* 移除原有的媒体查询 */
/* html {
  font-size: 16px;
}

@media screen and (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  html {
    font-size: 12px;
  }
} */

/* 容器样式 */
.schedule-container {
  width: 100%;
  height: 100%;
  background-color: #f2f5fc;
  position: relative;
  /* 使用计算后的字体大小 */
  font-size: calc(0.875 * var(--base-font-size, 16px));
}

/* 其他样式保持不变，但可以使用CSS变量来引用基准字体大小 */
.schedule-header {
  box-sizing: border-box;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  height: 79px;
  padding: 30px 25px 23px 25px;
}

.nav-buttons {
  display: grid;
  grid-template-columns: repeat(3, auto);
  gap: 2px;
  margin-right: 13px;
}
.nav-btn {
  width: 96px;
  height: 28px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dbe2f0;
  text-align: center;
  line-height: 28px;
  color: #000000;
  font-size: 14px;

  font-weight: normal;
}

.nav-btn.active {
  background-color: #1890ff;
  color: white;
  width: 126px;
  height: 28px;
  background: #2266ff;
  border-radius: 4px;
  border: 1px solid #dbe2f0;
  text-align: center;
  line-height: 28px;
  color: #ffffff;
  font-size: 14px;

  font-weight: normal;
}

.date-range {
  font-size: 12px;
  color: #666;
}

.view-selector select {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

/* 星期栏 */
.week-header {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  height: 28px;
  margin: 0 25px 0 42px;
  .day-column {
    text-align: center;
    // background-color: #d00;
    .day-number {
      font-size: 14px;
      font-weight: bold;
    }
    .day-name {
      font-size: 12px;
      //   margin-top: 4px;
    }
  }
}

.date-header {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(13, 1fr);
  height: 28px;
  margin: 0 25px 0 42px;
  .time-area {
    font-weight: normal;
    font-size: 14px;
    color: #000000;
    height: 28px;
    line-height: 28px;
    text-align: center;
  }
  .date-number {
    font-weight: normal;
    font-size: 13px;
    color: #000000;
    height: 48px; // 固定高度
    width: 100%; // 宽度占满父容器
    background: #ffffff;
    border: 1px solid #dbe2f0;
    text-align: center;
    padding: 4px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box; // 确保padding不会增加元素实际尺寸

    .solar-date {
      font-size: 14px;
      font-weight: bold;
    }

    .lunar-date {
      font-size: 10px;
      color: #666;
      margin-top: 2px;
    }

    .festival {
      font-size: 10px;
      color: #f5222d;
      margin-top: 2px;
    }

    &.active {
      background: #2266ff;
      border: 1px solid #dbe2f0;

      font-weight: normal;

      .solar-date {
        color: #ffffff;
      }

      .lunar-date {
        color: rgba(255, 255, 255, 0.8);
      }

      .festival {
        color: #ffccc7;
      }
    }
  }
}

/* 时间表格 */
.schedule-grid {
  display: grid;
  margin: 0 25px 0 42px;
  grid-template-columns: 1fr 7fr; /* 修改这里：按1:7的比例分配空间 */
  overflow-y: auto;
  border-collapse: collapse;
  margin-top: 14px;
}

.time-axis {
  box-sizing: border-box;
}

.time-slot {
  height: 48px;
  display: grid;
  //   place-items: center;
  justify-items: center;
  align-items: center;
  font-size: 12px;
  color: #666;
  //   border-bottom: 1px solid #e0e0e0;
  box-sizing: border-box;
  position: relative;
  //   margin-top: -1px;
}

.schedule-content {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  position: relative;
  box-sizing: border-box;
}

.schedule-content .day-column {
  position: relative;
  box-sizing: border-box;
}
.hour-cell {
  height: 48px;
  border: 1px solid #dbe2f0;
  box-sizing: border-box;
  background-color: #ffffff;
  //   background: #d00;
  border-collapse: collapse;
}

/* 课程项目 */
.course-item {
  position: absolute;
  left: 4px;
  right: 4px;
  border-radius: 4px;
  padding: 8px;
  color: white;
  font-size: 12px;
  overflow: hidden;
  cursor: pointer;
}

.course-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.course-desc,
.course-time,
.course-room {
  font-size: 11px;
  opacity: 0.9;
}

/* 课程详情弹窗 */
.course-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: grid;
  place-items: center;
  z-index: 1000;
}

.course-detail-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 80%;
  max-width: 320px;
}

.course-detail-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
}

.course-detail-content p {
  margin: 8px 0;
  font-size: 14px;
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
}

.course-detail-content i {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  display: inline-block;
}

.detail-btn {
  margin-top: 24px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}

/* 截图按钮 */
.screenshot-btn {
  position: fixed;
  bottom: 32px;
  right: 32px;
  background-color: #1890ff;
  color: white;
  padding: 12px 16px;
  border-radius: 32px;
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.screenshot-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M3 4V1h2v3h3v2H5v3H3V6H0V4h3zm3 6V7h3V4h7l1.83 2H21c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V10h3zm7 9c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-3.2-5c0 1.77 1.43 3.2 3.2 3.2s3.2-1.43 3.2-3.2-1.43-3.2-3.2-3.2-3.2 1.43-3.2 3.2z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
</style>