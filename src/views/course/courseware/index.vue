<template>
	<div class="app-container">
		<el-form
			:model="queryParams"
			ref="queryRef"
			:inline="true"
			v-show="showSearch"
		>
			<el-form-item label="课件名称" prop="name">
				<el-input
					v-model="queryParams.name"
					placeholder="课件名称"
					clearable
					style="width: 200px"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="课件状态" prop="status">
				<el-select
					v-model="queryParams.status"
					placeholder="课件状态"
					clearable
					style="width: 200px"
				>
					<el-option
						v-for="dict in course_status"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="创建时间" style="width: 308px">
				<el-date-picker
					v-model="createDate"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					:default-time="[
						new Date(2000, 1, 1, 0, 0, 0),
						new Date(2000, 2, 1, 23, 59, 59),
					]"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="编辑时间" style="width: 308px">
				<el-date-picker
					v-model="editDate"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					:default-time="[
						new Date(2000, 1, 1, 0, 0, 0),
						new Date(2000, 2, 1, 23, 59, 59),
					]"
					end-placeholder="结束日期"
				></el-date-picker>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="Search" @click="handleQuery"
					>搜索</el-button
				>
				<el-button icon="Refresh" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="Plus" @click="handleAdd()"
					>创建课件</el-button
				>
			</el-col>
			<!-- <right-toolbar
        :showSearch="showSearch"
        :search="false"
        @queryTable="getList"
      ></right-toolbar> -->
		</el-row>

		<el-table
			v-if="refreshTable"
			v-loading="loading"
			:data="applicationList"
			row-key="id"
		>
			<el-table-column
				prop="courseCode"
				label="id"
				width="100"
				align="center"
				:fixed="true"
			></el-table-column>
			<el-table-column
				prop="title"
				label="课件名称"
				width="160"
				:show-overflow-tooltip="true"
			>
				<template #default="scope">
					<div>{{ scope.row.title }}</div>
					<el-tag
						type="danger"
						v-if="scope.row.appDel == '1' || scope.row.appStatus == '1'"
						>课件异常</el-tag
					>
				</template>
			</el-table-column>
			<el-table-column
				prop="describeInfo"
				label="课件介绍"
				width="180"
				:show-overflow-tooltip="true"
			>
			</el-table-column>
			<el-table-column prop="coverImage" label="课件封面图" width="160">
				<template #default="scope">
					<el-image
						style="width: 80px; height: 80px"
						:src="scope.row.coverImage"
						fit="cover"
					></el-image>
				</template>
			</el-table-column>
			<el-table-column
				prop="applicationInfo"
				label="关联应用名称·状态"
				min-width="180"
			>
				<template #default="scope">
					<el-space>
						<div>{{ scope.row.appName }} ·</div>
						<dict-tag
							:options="application_status"
							:value="scope.row.appStatus"
						/>
					</el-space>
				</template>
			</el-table-column>
			<el-table-column prop="status" label="课件状态" width="160">
				<template #default="scope">
					<dict-tag :options="course_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column
				label="创建时间"
				align="center"
				min-width="180"
				prop="createTime"
			>
				<template #default="scope">
					<span>{{ formatDate(scope.row.ctime) }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="编辑时间"
				align="center"
				min-width="180"
				prop="updateTime"
			>
				<template #default="scope">
					<span>{{ formatDate(scope.row.utime) }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="updateUserName"
				label="操作人"
				min-width="80"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column
				label="操作"
				align="center"
				width="210"
				class-name="small-padding fixed-width"
			>
				<template #default="scope">
					<el-button
						link
						type="primary"
						icon="Edit"
						@click="handleUpdate(scope.row, 'edit')"
						>编辑</el-button
					>
					<el-button
						link
						type="primary"
						icon="Sort"
						@click="handleChangeStatus(scope.row)"
						>{{ scope?.row.status != '0' ? '启用' : '禁用' }}</el-button
					>
					<!-- <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row, 'check')"
            >查看详情</el-button
          > -->
					<el-button
						link
						type="primary"
						icon="Delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column>
		</el-table>
		<pagination
			v-show="total > 0"
			:total="total"
			v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize"
			@pagination="getList"
		/>
		<el-drawer :title="title" v-model="open" append-to-body size="40%">
			<el-form
				ref="roomRef"
				:model="roomForm"
				:rules="rules"
				label-width="150px"
				:disabled="title === '查看课件'"
				label-position="top"
			>
				<el-row>
					<el-col :span="24">
						<el-form-item label="课件名称" prop="title">
							<el-input
								v-model="roomForm.title"
								show-word-limit
								:maxlength="20"
								placeholder="请输入课件名称"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课件介绍" prop="describeInfo">
							<el-input
								v-model="roomForm.describeInfo"
								:maxlength="300"
								show-word-limit
								type="textarea"
								placeholder="请输入课件介绍"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课件封面" prop="coverImage">
							<image-upload-old
								:isShowTip="false"
								:multiple="false"
								v-model="roomForm.coverImage"
								list-type="picture-card"
								:limit="1"
							></image-upload-old>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item prop="appId" label="课件应用">
							<el-space style="width: 100%; justify-content: space-between">
								<el-space v-if="roomForm.appId">
									<el-avatar
										:size="60"
										:src="applicationInfo.appCoverImage"
										class="avatar"
									/>
									<div>
										<div>{{ applicationInfo.appName }}</div>
										<div>{{ applicationInfo.appInfo }}</div>
									</div>
								</el-space>
								<el-button
									link
									type="primary"
									icon="plus"
									:disabled="title === '复制并创建课件'"
									@click="handleDevice('', 'add')"
									>关联应用</el-button
								>
							</el-space>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="附件内容" prop="fileUrl">
							<image-upload-old
								:isShowTip="false"
								:multiple="true"
								v-model="roomForm.fileUrl"
								:fileType="['png', 'jpg', 'jpeg', 'mp4', 'mov', 'avi']"
								list-type="picture-card"
								:limit="50"
							></image-upload-old>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课件分类" prop="cateType">
							<el-select
								v-model="roomForm.cateType"
								placeholder="选择课件分类"
								clearable
								style="width: 200px"
							>
								<el-option
									v-for="dict in courseware_type"
									:key="dict.value"
									:label="dict.label"
									:value="dict.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课件状态" prop="status">
							<el-radio-group v-model="roomForm.status">
								<el-radio :label="0">启用</el-radio>
								<el-radio :label="1">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<!-- <el-divider /> -->
				</el-row>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="cancel">取 消</el-button>
				</div>
			</template>
		</el-drawer>
		<!-- 添加或修改菜单对话框 -->
		<el-dialog
			:title="dialogTitle"
			v-model="dialogOpen"
			width="650px"
			append-to-body
		>
			<p v-if="roomForm.appId">
				{{ `已关联应用：${applicationInfo.appName}` }}
			</p>
			<div class="info-card-container">
				<el-tabs tab-position="left" v-model="applicationType">
					<el-tab-pane
						v-for="dict in [
							{ label: '全部', value: 'all' },
							...application_classification,
						]"
						:key="dict.value"
						:label="dict.label"
						:name="dict.value"
					/>
				</el-tabs>
				<div class="info-card" v-if="applicationTypeList?.length">
					<el-card
						shadow="always"
						v-for="item in applicationTypeList"
						class="custom-card"
						:key="item.id"
					>
						<div class="card-content">
							<!-- 左侧头像 -->
							<el-avatar
								:size="60"
								:src="item.applicationAvatar"
								class="avatar"
							/>

							<!-- 中间名称和描述 -->
							<div class="info">
								<div>{{ `应用名称：${item.applicationName}` }}</div>
								<div>{{ `应用描述：${item.applicationDesc}` }}</div>
							</div>

							<!-- 右侧操作按钮 -->
							<el-button
								link
								:type="roomForm?.appId === item.id ? 'danger' : 'primary'"
								@click="submitFormDialog(item, 'confirm')"
								>{{
									roomForm?.appId === item.id ? '已关联' : '关联'
								}}</el-button
							>
						</div>
					</el-card>
				</div>
				<el-empty v-else description="暂无应用" style="margin: 0 auto" />
			</div>
			<!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFormDialog">确 定</el-button>
          <el-button @click="cancelDialog">取 消</el-button>
        </div>
      </template> -->
		</el-dialog>
		<el-dialog
			width="750px"
			title="创建课件"
			v-model="dialogToOrgOpen"
			append-to-body
		>
			<div class="info-card-container">
				<div>
					<el-button
						type="primary"
						style="margin: 20px 0"
						@click="handleAdd('add')"
						>创建课件</el-button
					>
					<el-tabs tab-position="left" v-model="dialogToOrgOpen">
						<el-tab-pane label="官方提供" name="office" />
					</el-tabs>
				</div>
				<div style="width: 100%">
					<el-select
						v-model="courseTypeTOrg"
						placeholder="选择课件分类"
						clearable
						style="width: 200px; margin-bottom: 20px"
					>
						<el-option
							v-for="dict in courseware_type"
							:key="dict.value"
							:label="dict.label"
							:value="dict.value"
						/>
					</el-select>
					<div class="info-card" v-if="courseListToOrg?.length">
						<el-card
							shadow="always"
							v-for="item in courseListToOrg"
							class="custom-card"
							:key="item.id"
						>
							<div class="card-content">
								<!-- 左侧头像 -->
								<el-avatar :size="60" :src="item.coverImage" class="avatar" />

								<!-- 中间名称和描述 -->
								<div class="info">
									<div>{{ `应用名称：${item.title}` }}</div>
									<div>{{ `应用描述：${item.describeInfo}` }}</div>
								</div>

								<!-- 右侧操作按钮 -->
								<el-button
									link
									type="primary"
									@click="handleUpdate(item, 'copy')"
									>复制并创建</el-button
								>
							</div>
						</el-card>
					</div>
					<el-empty v-else description="暂无课件" style="margin: 0 auto" />
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup name="institution-list">
import {
	listCourse,
	listCourseCopy,
	addCourse,
	checkCourseDetail,
	editCourse,
	changeCourseDisable,
	removeCourse,
} from '@/api/course'
import useUserStore from '@/store/modules/user'
import { getApplicationList } from '@/api/application/list'
import { date2timeStamp } from '../../../utils'

import { formatDate } from '@/utils/index'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const applicationList = ref([])
const courseListToOrg = ref([])
const courseTypeTOrg = ref('')
const open = ref(false)
const dialogOpen = ref(false)
const dialogToOrgOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const applicationType = ref('all')
const dialogTitle = ref('')
const refreshTable = ref(true)
const createDate = ref([])
const applicationTypeList = ref([])
const editDate = ref([])
const total = ref(0)
const deviceList = ref([])
const data = reactive({
	roomForm: {},
	form: {},
	queryParams: {
		visible: undefined,
		pageNum: 1,
		pageSize: 10,
	},
	applicationInfo: {},
	dialogRules: {
		deviceNo: [
			{ required: true, message: '请输入设备序列号', trigger: 'blur' },
		],
		seatNo: [{ required: true, message: '请输入座位号', trigger: 'blur' }],
	},
	rules: {
		title: [{ required: true, message: '请输入课件名称 ', trigger: 'blur' }],
		describeInfo: [
			{ required: true, message: '请输入课件介绍', trigger: 'blur' },
		],
		coverImage: [
			{ required: true, message: '请上传课件封面', trigger: 'blur' },
		],
		appId: [{ required: true, message: '请关联应用', trigger: 'blur' }],
		cateType: [{ required: true, message: '请选择课件分类', trigger: 'blur' }],
		status: [{ required: true, message: '请选择课件状态', trigger: 'blur' }],
	},
})

const {
	course_status,
	application_classification,
	courseware_type,
	application_status,
} = proxy.useDict(
	'course_status',
	'application_classification',
	'courseware_type',
	'application_status'
)
const { queryParams, rules, statusMaps, form, roomForm, applicationInfo } =
	toRefs(data)

watch(
	[applicationType, dialogOpen],
	([newVal, newVal2]) => {
		if (newVal2 && newVal) {
			getApplicationList({
				pageNum: 1,
				pageSize: 100,
				status: '0',
				applicationType: newVal === 'all' ? undefined : applicationType.value,
			}).then(response => {
				applicationTypeList.value = response?.rows
			})
		} else {
			applicationType.value = 'all'
			applicationTypeList.value = []
		}
	},
	{
		immediate: true,
	}
)
watch(
	[courseTypeTOrg, dialogToOrgOpen],
	([newVal, newVal2]) => {
		if (newVal2 && (newVal || newVal === '')) {
			listCourseCopy({
				orgId: 0,
				pageNum: 1,
				pageSize: 100,
				status: '0',
				cateType: newVal,
			}).then(response => {
				courseListToOrg.value = response?.rows
			})
		} else {
			courseTypeTOrg.value = ''
			courseListToOrg.value = []
		}
	},
	{
		immediate: true,
	}
)

/** 查询菜单列表 */
function getList() {
	loading.value = true
	listCourse({
		...queryParams.value,
		ctimeStart: date2timeStamp(createDate?.value[0]),
		ctimeEnd: date2timeStamp(createDate?.value[1]),
		utimeStart: date2timeStamp(editDate?.value[0]),
		utimeEnd: date2timeStamp(editDate?.value[1]),
	}).then(response => {
		applicationList.value = response?.rows
		loading.value = false
		total.value = response.total
	})
}

/** 渲染状态 */
function getStatusLabel(scope) {
	return statusMaps?.value?.find(item => item?.value === scope?.row?.status)
		?.label
}
/** 取消按钮 */
function cancel() {
	open.value = false
	resetDrawer()
	applicationInfo.value = {}
}

function handleDevice(row, type) {
	// dialogTitle.value = type === 'add' ? "添加课件" : "编辑课件";
	dialogTitle.value = '关联应用'
	// if (type === 'edit') {
	//   // dialogTitle.value = "编辑课件";
	//   dialogOpen.value = true;
	//   form.value = {
	//     ...row,
	//   };
	//   return
	// }
	// if (type === 'delete') {
	//   const index = deviceList.value.findIndex(item => item.id === row.id);
	//   deviceList.value.splice(index, 1);
	//   return
	// }
	dialogOpen.value = true
	// dialogTitle.value =  "添加课件"
}

/** 表单重置 */
function reset() {
	form.value = {}
	proxy.resetForm('applicationRef')
}
/** 表单重置 */
function resetDrawer() {
	roomForm.value = {}
	proxy.resetForm('roomForm')
	deviceList.value = []
}

/** 搜索按钮操作 */
function handleQuery() {
	queryParams.value.pageNum = 1
	getList()
}

/** 重置按钮操作 */
function resetQuery() {
	createDate.value = []
	editDate.value = []
	proxy.resetForm('queryRef')
	handleQuery()
}

/** 新增按钮操作 */
function handleAdd(type) {
	if (userStore.orgId !== 0 && !type) {
		dialogToOrgOpen.value = 'office'
		return
	}
	resetDrawer()
	dialogToOrgOpen.value = false
	open.value = true
	title.value = '添加课件'
}

/** 修改按钮操作 */
async function handleUpdate(data, type) {
	const row = JSON.parse(JSON.stringify(data))
	title.value = type === 'edit' ? '编辑课件' : '复制并创建课件'
	if (type === 'copy') {
		dialogToOrgOpen.value = false
	}
	const result = await checkCourseDetail(row.id)
	if (result?.code === 200) {
		open.value = true
		roomForm.value = {
			...result?.data,
			cateType: result?.data?.cateType?.toString(),
			id: type === 'edit' ? row.id : undefined,
		}
		applicationInfo.value = {
			appName: result?.data?.appName,
			appCoverImage: result?.data?.appCoverImage,
			appInfo: result?.data?.appInfo,
		}
	}
	console.log('result', result)
	// roomForm.value = {
	//   roomName: row.roomName,
	//   id: row.id,
	//   deviceNum: row.deviceNum
	// };
}
function submitFormDialog(item, type) {
	if (item.id === roomForm.value.appId) {
		roomForm.value.appId = undefined
		applicationInfo.value = {}
		ElMessage.success('取消关联成功')
		return
	}
	if (type === 'confirm') {
		roomForm.value.appId = item.id
		applicationInfo.value = {
			appName: item.applicationName,
			appCoverImage: item.applicationAvatar,
			appInfo: item.applicationDesc,
		}
		ElMessage.success('关联成功')
	}
	dialogOpen.value = false
}
/** 提交按钮 */
function submitForm() {
	proxy.$refs['roomRef'].validate(valid => {
		if (valid) {
			const parmas = {
				...roomForm.value,
				type: userStore.orgId === 0 ? 0 : 1,
				orgId: userStore.orgId,
			}
			if (roomForm.value.id != undefined) {
				editCourse(parmas).then(response => {
					proxy.$modal.msgSuccess('修改成功')
					open.value = false
					getList()
				})
			} else {
				addCourse(parmas).then(response => {
					proxy.$modal.msgSuccess('新增成功')
					open.value = false
					getList()
				})
			}
		}
	})
}

/** 删除按钮操作 */
function handleDelete(row) {
	proxy.$modal
		.confirm('是否确认删除名称为"' + row.title + '"的数据项?')
		.then(function () {
			return removeCourse({ id: row.id })
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess('删除成功')
		})
		.catch(() => {})
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
	let text = `是否确认${row?.status != '0' ? '启用' : '禁用'}名称为"${
		row.title
	}"的数据项?`
	if (row.status === 0 && row.appStatus === 0) {
		text = '该课件被禁用后，客户端的课包会受影响，是否继续？'
	}
	if (row.status === 1 && row.appStatus === 1) {
		text = '关联应用无法使用，课件暂不支持启用！'
		proxy.$modal.msgError(text)
		return
	}
	proxy.$modal
		.confirm(text)
		.then(function () {
			return changeCourseDisable({
				id: row?.id,
				status: row?.status != '0' ? 0 : 1,
			})
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess(`${row?.status != '0' ? '启用' : '禁用'}成功`)
		})
		.catch(() => {})
}
getList()
</script>
<style scoped lang="scss">
.info-card-container {
	display: flex;
	align-items: start;
}
.info-card {
	width: 100%;
	:deep(.el-card__body) {
		padding: 0 !important;
	}
}
.info-box {
	display: flex;
	justify-content: space-around;
	align-items: flex-start;
}
.info-box {
	width: 100%;
	margin-bottom: 5px;
}
.custom-card {
	margin: 0 0 15px 10px;
	max-width: 800px;

	.card-content {
		display: flex;
		align-items: center;
		padding: 10px;
		gap: 20px;
	}

	.avatar {
		flex-shrink: 0;
	}

	.info {
		flex-grow: 1;
		min-width: 0;
	}

	.name {
		margin: 0 0 8px 0;
		color: #303133;
	}

	.desc {
		margin: 0;
		color: #606266;
		font-size: 14px;
		line-height: 1.5;
	}

	.actions {
		flex-shrink: 0;
		display: flex;
		gap: 10px;
	}
}
</style>
