<template>
	<div class="app-container">
		<el-form
			:model="queryParams"
			ref="queryRef"
			:inline="true"
			v-show="showSearch"
		>
			<el-form-item label="课包名称" prop="name">
				<el-input
					v-model="queryParams.name"
					placeholder="课包名称"
					clearable
					style="width: 200px"
					@keyup.enter="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="课包状态" prop="status">
				<el-select
					v-model="queryParams.status"
					placeholder="课包状态"
					clearable
					style="width: 200px"
				>
					<el-option
						v-for="dict in course_status"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="创建时间" style="width: 308px">
				<el-date-picker
					v-model="createDate"
					:default-time="[
						new Date(2000, 1, 1, 0, 0, 0),
						new Date(2000, 2, 1, 23, 59, 59),
					]"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				></el-date-picker>
			</el-form-item>
			<el-form-item label="编辑时间" style="width: 308px">
				<el-date-picker
					v-model="editDate"
					:default-time="[
						new Date(2000, 1, 1, 0, 0, 0),
						new Date(2000, 2, 1, 23, 59, 59),
					]"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				></el-date-picker>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="Search" @click="handleQuery"
					>搜索</el-button
				>
				<el-button icon="Refresh" @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="Plus" @click="handleAdd"
					>创建课包</el-button
				>
			</el-col>
			<!-- <right-toolbar
        :showSearch="showSearch"
        :search="false"
        @queryTable="getList"
      ></right-toolbar> -->
		</el-row>

		<el-table
			v-if="refreshTable"
			v-loading="loading"
			:data="applicationList"
			row-key="id"
		>
			<el-table-column
				prop="packageCode"
				label="id"
				width="120"
				align="center"
				:fixed="true"
			></el-table-column>
			<el-table-column
				prop="title"
				label="课包名称"
				min-width="160"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column
				prop="describeInfo"
				label="课包介绍"
				min-width="180"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column prop="coverImage" label="课包封面图" width="120">
				<template #default="scope">
					<el-image
						style="width: 80px; height: 80px"
						:src="scope.row.coverImage"
						fit="cover"
					></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="cateType" label="课包分类" min-width="100">
				<template #default="scope">
					<dict-tag
						:options="course_package_type"
						:value="scope.row.cateType"
					/>
				</template>
			</el-table-column>
			<el-table-column prop="coursewareIdList" label="课包数量">
				<template #default="scope">
					<span>{{ scope.row.coursewareIdList?.length }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="status" label="课包状态" width="100">
				<template #default="scope">
					<dict-tag :options="course_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column
				label="创建时间"
				align="center"
				min-width="180"
				prop="createTime"
			>
				<template #default="scope">
					<span>{{ formatDate(scope.row.ctime) }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="编辑时间"
				align="center"
				min-width="180"
				prop="updateTime"
			>
				<template #default="scope">
					<span>{{ formatDate(scope.row.utime) }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="updateUserName"
				label="操作人"
				min-width="80"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column
				label="操作"
				align="center"
				width="210"
				class-name="small-padding fixed-width"
			>
				<template #default="scope">
					<el-button
						link
						type="primary"
						icon="Edit"
						@click="handleUpdate(scope.row, 'edit')"
						>编辑</el-button
					>
					<el-button
						link
						type="primary"
						icon="Sort"
						@click="handleChangeStatus(scope.row)"
						>{{ scope?.row.status != '0' ? '启用' : '禁用' }}</el-button
					>
					<!-- <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row, 'check')"
            >查看详情</el-button
          > -->
					<el-button
						link
						type="primary"
						icon="Delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column>
		</el-table>
		<pagination
			v-show="total > 0"
			:total="total"
			v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize"
			@pagination="getList"
		/>
		<el-drawer
			:title="title"
			v-model="open"
			append-to-body
			size="60%"
			destroy-on-close
		>
			<el-form
				ref="roomRef"
				:model="roomForm"
				:rules="rules"
				label-width="150px"
				:disabled="title === '查看课件'"
				label-position="top"
			>
				<el-row>
					<el-col :span="24">
						<el-form-item label="课包名称" prop="title">
							<el-input
								v-model="roomForm.title"
								show-word-limit
								:maxlength="20"
								placeholder="请输入课包名称"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课包介绍" prop="describeInfo">
							<el-input
								v-model="roomForm.describeInfo"
								:maxlength="300"
								show-word-limit
								type="textarea"
								placeholder="请输入课包介绍"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课包封面" prop="coverImage">
							<image-upload-old
								:isShowTip="false"
								:multiple="false"
								v-model="roomForm.coverImage"
								list-type="picture-card"
								:limit="1"
							></image-upload-old>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课包分类" prop="cateType">
							<el-select
								v-model="roomForm.cateType"
								placeholder="选择课包分类"
								clearable
								style="width: 200px"
							>
								<el-option
									v-for="dict in course_package_type"
									:key="dict.value"
									:label="dict.label"
									:value="dict.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="课包状态" prop="status">
							<el-radio-group v-model="roomForm.status">
								<el-radio :label="0">启用</el-radio>
								<el-radio :label="1">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-divider />
					<el-col :span="24">
						<el-form-item prop="coursewareDetailIds" label="课包内课件">
							<el-space style="width: 100%; justify-content: space-between">
								<div>{{ `课件数量：${coursewareDetailIds?.length}` }}</div>
								<el-button
									link
									type="primary"
									icon="plus"
									@click="handleDevice('', 'add')"
									>添加课件</el-button
								>
							</el-space>
							<div class="info-card" v-if="roomForm.coursewareDetailIds">
								<p style="text-align: right; color: #606266; font-size: 13px">
									课件列表可以拖拽排序
								</p>
								<draggable
									v-model="coursewareDetailList"
									item-key="id"
									class="draggable-list"
									@end="saveCoursewareDetailIds"
								>
									<template #item="{ element }">
										<el-card shadow="always" class="custom-card">
											<div class="card-content">
												<!-- 左侧头像 -->
												<el-avatar
													:size="60"
													:src="element.coverImage"
													class="avatar"
												/>

												<!-- 中间名称和描述 -->
												<div class="info">
													<el-space>
														<div>{{ `课件名称：${element.title}` }}</div>
														<el-tag
															type="danger"
															v-if="
																element.appDel == '1' ||
																element.appStatus == '1'
															"
															>课件异常</el-tag
														>
													</el-space>
													<div>
														<el-tooltip
															class="item"
															effect="dark"
															:content="element.describeInfo"
															placement="top"
														>
															<el-text truncated class="description">{{
																`课件描述：${element.describeInfo}`
															}}</el-text>
														</el-tooltip>
													</div>
													<!-- <div>{{ `课件描述：${element.describeInfo}` }}</div> -->
												</div>

												<!-- 右侧操作按钮 -->
												<el-button
													link
													type="danger"
													@click="submitFormDialog(element, 'deleteSpecial')"
													>删除</el-button
												>
											</div>
										</el-card>
									</template>
								</draggable>
							</div>
							<el-empty v-else description="暂无课件" style="margin: 0 auto" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitForm">确 定</el-button>
					<el-button @click="cancel">取 消</el-button>
				</div>
			</template>
		</el-drawer>
		<!-- 添加或修改菜单对话框 -->
		<el-dialog
			:title="dialogTitle"
			v-model="dialogOpen"
			width="650px"
			append-to-body
			:before-close="doneFn => submitFormDialog(doneFn, 'close')"
		>
			<!-- <p v-if="roomForm.appId">{{ `已关联课件：${applicationInfo.appName}` }}</p> -->
			<div class="info-card-container">
				<el-space>
					<el-radio-group
						v-model="courseTypeStatus"
						style="margin-bottom: 20px"
					>
						<el-radio-button :label="0">启用</el-radio-button>
						<el-radio-button :label="1">禁用</el-radio-button>
					</el-radio-group>
				</el-space>
				<div class="info-card" v-if="courseList?.length">
					<el-card
						shadow="always"
						v-for="item in courseList"
						class="custom-card"
						:key="item.id"
					>
						<div class="card-content">
							<!-- 左侧头像 -->
							<el-avatar :size="60" :src="item.coverImage" class="avatar" />

							<!-- 中间名称和描述 -->
							<div class="info">
								<div>{{ `课件名称：${item.title}` }}</div>
								<div>{{ `课件描述：${item.describeInfo}` }}</div>
							</div>

							<!-- 右侧操作按钮 -->
							<el-button
								link
								:type="
									coursewareDetailIds?.includes(item.id) ? 'danger' : 'primary'
								"
								:disabled="
									!coursewareDetailIds?.includes(item.id) && item.status == 1
								"
								@click="
									submitFormDialog(
										item,
										coursewareDetailIds?.includes(item.id) ? 'delete' : 'add'
									)
								"
								>{{
									coursewareDetailIds?.includes(item.id) ? '已添加' : '添加'
								}}</el-button
							>
						</div>
					</el-card>
				</div>
				<el-empty v-else description="暂无课件" style="margin: 0 auto" />
			</div>
			<!-- <template #footer> -->
			<!-- <div class="dialog-footer"> -->
			<!-- <el-button type="primary" @click="submitFormDialog('', 'confirm')">确 定</el-button> -->
			<!-- <el-button @click="cancelDialog">取 消</el-button> -->
			<!-- </div> -->
			<!-- </template> -->
		</el-dialog>
	</div>
</template>

<script setup name="institution-list">
import draggable from 'vuedraggable'
import {
	listPackage,
	listCourse,
	addPackage,
	checkPackageDetail,
	editPackage,
	changePackageDisable,
	removePackage,
} from '@/api/course'
import useUserStore from '@/store/modules/user'
import { date2timeStamp } from '../../../utils'

import { formatDate } from '@/utils/index'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const applicationList = ref([])
const courseList = ref([])
const courseTypeStatus = ref('0')
const open = ref(false)
const dialogOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const dialogTitle = ref('')
const refreshTable = ref(true)
const createDate = ref([])
const editDate = ref([])
const total = ref(0)
const coursewareDetailList = ref([])
const data = reactive({
	roomForm: {},
	form: {},
	queryParams: {
		visible: undefined,
		pageNum: 1,
		pageSize: 10,
	},
	coursewareDetailIds: [],
	dialogRules: {
		deviceNo: [
			{ required: true, message: '请输入设备序列号', trigger: 'blur' },
		],
		seatNo: [{ required: true, message: '请输入座位号', trigger: 'blur' }],
	},
	rules: {
		title: [{ required: true, message: '请输入课件名称 ', trigger: 'blur' }],
		describeInfo: [
			{ required: true, message: '请输入课件介绍', trigger: 'blur' },
		],
		coverImage: [
			{ required: true, message: '请上传课件封面', trigger: 'blur' },
		],
		coursewareDetailIds: [
			{ required: true, message: '请添加课件', trigger: 'blur' },
		],
		cateType: [{ required: true, message: '请选择课件分类', trigger: 'blur' }],
		status: [{ required: true, message: '请选择课件状态', trigger: 'blur' }],
	},
})

const {
	course_status,
	application_classification,
	courseware_type,
	application_status,
	course_package_type,
} = proxy.useDict(
	'course_status',
	'application_classification',
	'courseware_type',
	'application_status',
	'course_package_type'
)
const { queryParams, rules, statusMaps, form, roomForm, coursewareDetailIds } =
	toRefs(data)

watch(
	[courseTypeStatus, dialogOpen],
	([newVal, newVal2]) => {
		if (newVal2) {
			listCourse({
				pageNum: 1,
				pageSize: 100,
				status: newVal,
			}).then(response => {
				courseList.value = response?.rows
			})
		} else {
			courseList.value = []
		}
	},
	{
		immediate: true,
	}
)
function saveCoursewareDetailIds() {
	roomForm.value.coursewareDetailIds = coursewareDetailList.value
		.map(v => v.id)
		.join()
}
/** 查询菜单列表 */
function getList() {
	loading.value = true
	listPackage({
		...queryParams.value,
		ctimeStart: date2timeStamp(createDate?.value[0]),
		ctimeEnd: date2timeStamp(createDate?.value[1]),
		utimeStart: date2timeStamp(editDate?.value[0]),
		utimeEnd: date2timeStamp(editDate?.value[1]),
	}).then(response => {
		applicationList.value = response?.rows
		loading.value = false
		total.value = response.total
	})
}

/** 渲染状态 */
function getStatusLabel(scope) {
	return statusMaps?.value?.find(item => item?.value === scope?.row?.status)
		?.label
}
/** 取消按钮 */
function cancel() {
	open.value = false
	resetDrawer()
}

function handleDevice(row, type) {
	dialogTitle.value = '关联课件'
	dialogOpen.value = true
}

/** 表单重置 */
function reset() {
	form.value = {}
	proxy.resetForm('applicationRef')
}
/** 表单重置 */
function resetDrawer() {
	roomForm.value = {}
	proxy.resetForm('roomForm')
	coursewareDetailList.value = []
	coursewareDetailIds.value = []
}

/** 搜索按钮操作 */
function handleQuery() {
	queryParams.value.pageNum = 1
	getList()
}

/** 重置按钮操作 */
function resetQuery() {
	createDate.value = []
	editDate.value = []
	proxy.resetForm('queryRef')
	handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
	resetDrawer()
	open.value = true
	title.value = '添加课包'
}

/** 修改按钮操作 */
async function handleUpdate(data, type) {
	const row = JSON.parse(JSON.stringify(data))
	title.value = type === 'edit' ? '编辑课包' : '复制并创建课件'
	const result = await checkPackageDetail(row.id)
	if (result?.code === 200) {
		open.value = true
		roomForm.value = {
			...result?.data,
			cateType: result?.data?.cateType?.toString(),
			id: type === 'edit' ? row.id : undefined,
		}
		coursewareDetailList.value = result?.data?.coursewareDetailList || []
		coursewareDetailIds.value =
			result?.data?.coursewareDetailList?.map(v => v.id) || []
	}
}
function submitFormDialog(item, type) {
	if (type === 'add') {
		coursewareDetailIds.value.push(item.id)
		coursewareDetailList.value.push(item)
		ElMessage.success('添加成功')
		return
	}
	if (type === 'delete' || type === 'deleteSpecial') {
		coursewareDetailIds.value = coursewareDetailIds.value.filter(
			i => i !== item.id
		)
		coursewareDetailList.value = coursewareDetailList.value.filter(
			i => i.id !== item.id
		)
		ElMessage.success('删除成功')
		type === 'deleteSpecial' && saveCoursewareDetailIds()
		return
	}
	saveCoursewareDetailIds()
	item()
}
/** 提交按钮 */
function submitForm() {
	proxy.$refs['roomRef'].validate(valid => {
		if (valid) {
			const parmas = {
				...roomForm.value,
				type: userStore.orgId === 0 ? 0 : 1,
				orgId: userStore.orgId,
			}
			if (roomForm.value.id != undefined) {
				editPackage(parmas).then(response => {
					proxy.$modal.msgSuccess('修改成功')
					open.value = false
					getList()
				})
			} else {
				addPackage(parmas).then(response => {
					proxy.$modal.msgSuccess('新增成功')
					open.value = false
					getList()
				})
			}
		}
	})
}

/** 删除按钮操作 */
function handleDelete(row) {
	proxy.$modal
		.confirm('是否确认删除名称为"' + row.title + '"的数据项?')
		.then(function () {
			return removePackage({ id: row.id })
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess('删除成功')
		})
		.catch(() => {})
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
	proxy.$modal
		.confirm(
			`是否确认${row?.status != '0' ? '启用' : '禁用'}名称为"${
				row.title
			}"的数据项?`
		)
		.then(function () {
			return changePackageDisable({
				id: row?.id,
				status: row?.status != '0' ? 0 : 1,
			})
		})
		.then(() => {
			getList()
			proxy.$modal.msgSuccess(`${row?.status != '0' ? '启用' : '禁用'}成功`)
		})
		.catch(() => {})
}
getList()
</script>
<style scoped lang="scss">
// .info-card-container {
//   display: flex;
//   align-items: start;
// }
.info-card {
	width: 100%;
	:deep(.el-card__body) {
		padding: 0 !important;
	}
}
.info-box {
	display: flex;
	justify-content: space-around;
	align-items: flex-start;
}
.info-box {
	width: 100%;
	margin-bottom: 5px;
}
.custom-card {
	margin: 0 0 15px 10px;
	max-width: 800px;

	.card-content {
		display: flex;
		align-items: center;
		padding: 10px;
		gap: 20px;
	}

	.avatar {
		flex-shrink: 0;
	}

	.info {
		flex-grow: 1;
		min-width: 0;
	}

	.name {
		margin: 0 0 8px 0;
		color: #303133;
	}

	.desc {
		margin: 0;
		color: #606266;
		font-size: 14px;
		line-height: 1.5;
	}

	.actions {
		flex-shrink: 0;
		display: flex;
		gap: 10px;
	}
}
</style>
