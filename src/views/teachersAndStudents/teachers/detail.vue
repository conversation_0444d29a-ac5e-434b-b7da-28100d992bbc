<template>
  <div class="teacher-detail-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>老师基本信息</span>
          <el-button type="primary" @click="openEditDialog">编辑</el-button>
        </div>
      </template>
      <div class="teacher-info">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">老师姓名：</span>
              <span class="value">{{ teacherInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">ID：</span>
              <span class="value">{{ teacherInfo.id }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">手机号：</span>
              <span class="value">{{ teacherInfo.phone }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">微信账号：</span>
              <span class="value">{{ teacherInfo.wechatNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">老师邮箱：</span>
              <span class="value">{{ teacherInfo.account || '-' }}</span>
            </div>
          </el-col>
          <!-- <el-col :span="8">
            <div class="info-item">
              <span class="label">性别：</span>
              <span class="value">{{ teacherInfo.gender === 1 ? '男' : '女' }}</span>
            </div>
          </el-col> -->
          <!-- <el-col :span="8">
            <div class="info-item">
              <span class="label">年龄：</span>
              <span class="value">{{ teacherInfo.age || '未设置' }}</span>
            </div>
          </el-col> -->
        </el-row>
      </div>
    </el-card>

    <el-card class="box-card account-card">
      <template #header>
        <div class="card-header">
          <span>账号信息</span>
        </div>
      </template>
      
      <div class="account-info">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="section-title">客户端账号信息</div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">登录账号：</span>
              <span class="value">{{ teacherInfo.jobNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">密码：</span>
              <div class="value">
                <TeacherPassword 
                  :password="'******'" 
                  :account="teacherInfo.jobNo" 
                  :id="teacherInfo.id" 
                  type="client"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">账号状态：</span>
              <div class="value status-switch">
                <el-switch
                  v-model="accountStatus"
                  active-text="启用"
                  inactive-text="禁用"
                  @change="handleStatusChange('client')"
                />
              </div>
            </div>
          </el-col>
        </el-row>

        <el-divider />

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="section-title">后台账号信息</div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">登录账号：</span>
              <span class="value">{{ teacherInfo.account }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">密码：</span>
              <div class="value">
                <TeacherPassword 
                  :password="'******'" 
                  :account="teacherInfo.account" 
                  :id="teacherInfo.id" 
                  type="backend"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">账号状态：</span>
              <div class="value status-switch">
                <el-switch
                  v-model="backendAccountStatus"
                  active-text="启用"
                  inactive-text="禁用"
                  @change="handleStatusChange('backend')"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 编辑老师信息对话框 -->
    <el-dialog
      title="编辑老师信息"
      v-model="editDialogVisible"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="teacherForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="老师姓名" prop="name">
          <el-input v-model="teacherForm.name" placeholder="请输入老师姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="teacherForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="微信账号" prop="wechatNo">
          <el-input v-model="teacherForm.wechat" placeholder="请输入微信账号" />
        </el-form-item>
        <el-form-item label="老师邮箱" prop="account">
          <el-input v-model="teacherForm.account" placeholder="请输入邮箱" />
        </el-form-item>
        <!-- <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="teacherForm.gender">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input-number v-model="teacherForm.age" :min="18" :max="100" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateTeacher">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getTeacherDetail, updateTeacher, updateAccountStatus } from '@/api/teachersAndStudents';
import TeacherPassword from './password.vue';

const route = useRoute();
const teacherId = ref(route.query.id as string);

// 老师信息
const teacherInfo = ref({
  id: '',
  name: '',
  phone: '',
  wechatNo: '',
  gender: 1,
  age: 0,
  account: '',
  status: 1 // 1启用 0禁用
});

// 账号状态
const accountStatus = ref(true);
const backendAccountStatus = ref(true);

// 编辑对话框
const editDialogVisible = ref(false);
const formRef = ref();

// 表单数据和验证规则
const teacherForm = ref({
  name: '',
  phone: '',
  wechat: '',
  account: ''
});

const formRules = {
  name: [
    { required: true, message: '请输入老师姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  account: [
    { required: true, message: '请输入邮箱', trigger: 'blur' }
  ]
};

// 获取老师详情
const getTeacherInfo = async () => {
  try {
    const res = await getTeacherDetail( teacherId.value);
    if (res.code === 200 && res.data) {
      teacherInfo.value = res.data;
      accountStatus.value = teacherInfo.value.jobStatus === 1;
      backendAccountStatus.value = teacherInfo.value.status === 1;
    } else {
      ElMessage.error(res.msg || '获取老师信息失败');
    }
  } catch (error) {
    console.error('获取老师信息出错:', error);
    ElMessage.error('系统异常，请稍后重试');
  }
};

// 打开编辑对话框
const openEditDialog = () => {
  const {jobPwd, password, ...rest} = teacherInfo.value;
  teacherForm.value = {
    ...rest,
    wechat: teacherInfo.value.wechatNo
  };
  editDialogVisible.value = true;
};

// 更新老师信息
const handleUpdateTeacher = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const params = {
          id: teacherId.value,
          ...teacherForm.value
        };
        const res = await updateTeacher(params);
        if (res.code === 200) {
          ElMessage.success('老师信息更新成功');
          getTeacherInfo(); // 刷新数据
          editDialogVisible.value = false;
        } else {
          ElMessage.error(res.msg || '更新老师信息失败');
        }
      } catch (error) {
        console.error('更新老师信息出错:', error);
        ElMessage.error('系统异常，请稍后重试');
      }
    }
  });
};

// 处理账号状态变更
const handleStatusChange = (type: 'client' | 'backend') => {
  const statusValue = type === 'client' ? accountStatus.value : backendAccountStatus.value;
  const statusText = statusValue ? '启用' : '禁用';
  
  ElMessageBox.confirm(
    `确认${statusText}该老师的${type === 'client' ? '客户端' : '后台'}账号吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const params = {
        id: teacherId.value,
        optType: statusValue ? 1 : 0,
        type // 客户端或后台账号类型
      };
      const res = await updateAccountStatus(params);
      if (res.code === 200) {
        ElMessage.success(`账号${statusText}成功`);
        getTeacherInfo(); // 刷新数据
      } else {
        ElMessage.error(res.msg || `账号${statusText}失败`);
        // 恢复开关状态
        if (type === 'client') {
          accountStatus.value = !accountStatus.value;
        } else {
          backendAccountStatus.value = !backendAccountStatus.value;
        }
      }
    } catch (error) {
      console.error(`更新账号状态出错:`, error);
      ElMessage.error('系统异常，请稍后重试');
      // 恢复开关状态
      if (type === 'client') {
        accountStatus.value = !accountStatus.value;
      } else {
        backendAccountStatus.value = !backendAccountStatus.value;
      }
    }
  }).catch(() => {
    // 用户取消操作，恢复开关状态
    if (type === 'client') {
      accountStatus.value = !accountStatus.value;
    } else {
      backendAccountStatus.value = !backendAccountStatus.value;
    }
  });
};

// 页面加载时获取老师信息
onMounted(() => {
  getTeacherInfo();
});
</script>

<style scoped>
.teacher-detail-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.value {
  color: #606266;
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 15px;
  color: #409EFF;
}

.status-switch {
  display: flex;
  align-items: center;
}

.account-card {
  margin-top: 20px;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 