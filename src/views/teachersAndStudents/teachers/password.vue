<template>
  <div style="display: flex; align-items: center;">
    <el-space>
      <div>{{ `${showPassword ? realPassword : password }` }}</div>
      <el-icon style="cursor: pointer;" @click="togglePassword">
        <View v-if="showPassword"/>
        <Hide v-else/>
      </el-icon>
    </el-space>
    <div>
      <el-space>
        <div>一键复制</div>
        <el-icon style="cursor: pointer;" @click="copyPassword">
          <CopyDocument />
        </el-icon>  
      </el-space>
    </div>
    <el-button type="primary" link @click="changePassword" style="margin-left: 10px;">修改密码</el-button>
  </div>

  <!-- 修改密码对话框 -->
  <el-dialog
    title="修改密码"
    v-model="passwordDialog"
    width="500px"
    append-to-body
  >
    <el-form
      ref="passwordFormRef"
      :model="passwordForm"
      :rules="passwordRules"
      label-width="100px"
    >
      <el-form-item label="旧密码" prop="oldPwd">
        <el-input
          v-model="passwordForm.oldPwd"
          placeholder="请输入旧密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input
          v-model="passwordForm.newPwd"
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPwd">
        <el-input
          v-model="passwordForm.confirmPwd"
          placeholder="请再次输入新密码"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitPasswordForm">确 定</el-button>
        <el-button @click="passwordDialog = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { View, Hide, CopyDocument } from '@element-plus/icons-vue';
import { showJobPwd, showPasswordFn, updateTeacherPassword } from "@/api/teachersAndStudents";

// 定义类型
interface PasswordFormData {
  oldPwd: string;
  newPwd: string;
  confirmPwd: string;
}

interface PasswordProps {
  password: string;
  account: string;
  id: string;
  type: 'client' | 'backend'; // 添加类型区分客户端和后台密码
}

const props = defineProps<PasswordProps>();

const showPassword = ref(false);
const realPassword = ref('');
const passwordDialog = ref(false);

const passwordForm = ref<PasswordFormData>({
  oldPwd: '',
  newPwd: '',
  confirmPwd: ''
});

const passwordFormRef = ref();
const passwordRules = {
  oldPwd: [
    { required: true, message: '请输入旧密码', trigger: 'blur' }
  ],
  newPwd: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ],
  confirmPwd: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: (error?: Error) => void) => {
        if (value !== passwordForm.value.newPwd) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 切换密码显示状态
const togglePassword = async () => {
  showPassword.value = !showPassword.value;
  if (showPassword.value && !realPassword.value) {
    try {
      // 根据类型调用不同的查看密码接口
      const apiFunc = props.type === 'client' ? 
        () => showJobPwd({ id: props.id }) : 
        () => showPasswordFn({ id: props.id });
      
      const res = await apiFunc();
      if (res.code === 200) {
        realPassword.value = res.data;
      } else {
        ElMessage.error(res.msg || '获取密码失败');
        showPassword.value = false;
      }
    } catch (error) {
      console.error('获取密码出错:', error);
      ElMessage.error('系统异常，请稍后重试');
      showPassword.value = false;
    }
  }
};

// 复制密码
const copyPassword = async () => {
  if (!realPassword.value) {
    ElMessage.error('请先查看密码');
    return;
  }
  try {
    await navigator.clipboard.writeText(`
    账号：${props.account}
    密码：${realPassword.value}`);
    ElMessage.success('文本复制成功');
  } catch (err) {
    ElMessage.error('文本复制失败');
  }
};

// 打开修改密码对话框
const changePassword = () => {
  passwordDialog.value = true;
  passwordForm.value = {
    oldPwd: '',
    newPwd: '',
    confirmPwd: ''
  };
};

// 提交密码表单
const submitPasswordForm = () => {
  passwordFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const params = {
        id: props.id,
        oldPwd: passwordForm.value.oldPwd,
        newPwd: passwordForm.value.newPwd,
        type:  props.type === 'client' ? 2 : 1// 添加类型区分客户端和后台密码
      };
      
      updateTeacherPassword(params).then((res: any) => {
        if (res.code === 200) {
          ElMessage.success('密码修改成功');
          passwordDialog.value = false;
          // 清空已查看的密码,下次需要重新获取
          realPassword.value = '';
          showPassword.value = false;
        } else {
          ElMessage.error(res.msg || '密码修改失败');
        }
      }).catch((error: any) => {
        console.error('修改密码出错:', error);
        ElMessage.error('系统异常，请稍后重试');
      });
    }
  });
};

// 暴露方法
defineExpose({
  togglePassword,
  copyPassword,
  changePassword,
  submitPasswordForm
});
</script>

<style scoped>
.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>