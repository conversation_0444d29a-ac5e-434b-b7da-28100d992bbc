<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="老师姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入老师姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="老师手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入老师手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="老师工号" prop="jobNo">
        <el-input
          v-model="queryParams.jobNo"
          placeholder="请输入老师工号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账号状态" prop="jobStatus">
        <el-select
          v-model="queryParams.jobStatus"
          placeholder="账号状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in account_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="createDate"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="编辑时间" style="width: 308px">
        <el-date-picker
          v-model="editDate"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['organization:teacher:add']"
          >新增老师</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
            >展开/折叠</el-button
          >
        </el-col> -->
      <!-- <right-toolbar
        :showSearch="showSearch"
        :search="false"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="applicationList"
      row-key="id"
    >
      <el-table-column
        prop="code"
        label="id"
        width="120"
        :fixed="true"
      ></el-table-column>
      <el-table-column
        v-if="!userStore.orgId"
        prop="orgName"
        label="所属机构"
        width="160"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="老师姓名"
        width="160"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="老师手机号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="wechat"
        label="老师微信"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="jobNo"
        label="老师工号"
        width="160"
      ></el-table-column>
      <!-- <el-table-column prop="account" label="老师账号" width="200">
        <template #default="scope">
          <div>{{ `账号：${scope.row.account}` }}</div>
          <password :password="scope.row.password" :id="scope.row.id" :account="scope.row.account"/>
        </template>
      </el-table-column> -->
      <el-table-column prop="jobStatus" label="客户端账号状态" width="130" align="center">
        <template #default="scope">
          <dict-tag :options="account_status" :value="scope.row.jobStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="编辑时间"
        align="center"
        min-width="180"
        prop="utime"
      >
        <template #default="scope">
          <span>{{ formatDate(scope.row.utime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        min-width="180"
        prop="ctime"
      >
        <template #default="scope">
          <span>{{ formatDate(scope.row.ctime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="210"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row, 'edit')"
            v-hasPermi="['organization:teacher:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row, 'check')"
            >查看详情</el-button
          >
          <!-- <el-button
            link
            type="primary"
            icon="Sort"
            @click="handleChangeStatus(scope.row)"
            >{{ scope?.row.status != '1' ? "启用" : "禁用" }}</el-button
          > -->
          <!-- <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form
        ref="applicationRef"
        :model="form"
        :rules="rules"
        label-width="150px"
        :disabled="title === '查看机构'"
      >
        <el-row>
          <el-col :span="22" :push="2">
            <p>基础信息</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="老师姓名" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入老师姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="老师电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入老师电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="老师微信" prop="wechat">
              <el-input
                v-model="form.wechat"
                placeholder="请输入老师微信"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="老师邮箱" prop="account">
              <el-input
                v-model="form.account"
                placeholder="请输入老师邮箱"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="老师工号" prop="jobNo">
              <el-input
                v-model="form.jobNo"
                placeholder="请输入老师工号"
              />
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="22" :push="2">
            <p>后台账号信息</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="账号" prop="account">
              <el-input
                v-model="form.account"
                placeholder="请输入账号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入密码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="角色" prop="roleId">
              <el-select
                v-model="form.roleId"
                placeholder="选择角色"
                clearable
              >
                <el-option
                  v-for="dict in [{
                    label: '老师',
                    value: 2
                  }]"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="账号状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="22" :push="2">
            <p>客户端账号信息</p>
          </el-col>
          <el-col :span="20">
            <el-form-item label="账号" prop="jobNo">
              <el-input
                v-model="form.jobNo"
                placeholder="请输入账号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="密码" prop="jobPwd">
              <el-input
                v-model="form.jobPwd"
                placeholder="请输入密码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="账号状态" prop="jobStatus">
              <el-radio-group v-model="form.jobStatus">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer v-if="title !== '查看机构'">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="institution-list">
import { useRouter } from "vue-router";
import { listTeacher, addTeacher, updateTeacher, changeTeacherDisable } from "@/api/teachersAndStudents";
import useUserStore from '@/store/modules/user'
import { formatDate } from "@/utils/index";
import password from './password.vue';
import { date2timeStamp } from "../../../utils";
const { proxy } = getCurrentInstance();
const router = useRouter();
const applicationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const userStore = useUserStore()
const title = ref("");
const refreshTable = ref(true);
const createDate = ref([]);
const editDate = ref([]);
const total = ref(0);
const data = reactive({
  form: {
    roleId: 2
  },
  queryParams: {
    // orgId: 1,
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    name: [
      { required: true, message: "请输入老师姓名", trigger: "blur" },
    ],
    phone: [
      { required: true, message: "请输入老师手机号", trigger: "blur" },
    ],
    jobNo: [
      { required: true, message: "请输入老师工号", trigger: "blur" },
    ],
    account: [
      { required: true, message: "请输入账号", trigger: "blur" },
    ],
    password: [
      { required: true, message: "请输入密码", trigger: "blur" },
    ],
    jobPwd: [
      { required: true, message: "请输入密码", trigger: "blur" },
    ],
    roleId: [
      { required: true, message: "请选择账号角色", trigger: "blur" },
    ],
    status: [
      { required: true, message: "请选择账号状态", trigger: "blur" },
    ],
    jobStatus: [
      { required: true, message: "请选择账号状态", trigger: "blur" },
    ],
  },
});

const { teacher_status: account_status } = proxy.useDict(
  "teacher_status"
);
const { queryParams, rules, statusMaps, form } = toRefs(data);

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  listTeacher({
    ...queryParams.value,
    createTimeStart: date2timeStamp(createDate?.value[0]),
    createTimeEnd: date2timeStamp(createDate?.value[1]),
    updateTimeStart: date2timeStamp(editDate?.value[0]),
    updateTimeEnd: date2timeStamp(editDate?.value[1]),
  }).then((response) => {
    applicationList.value = response?.data?.list || response?.data?.records;
    loading.value = false;
    total.value = response.data?.total;
  }).finally(() => {
    loading.value = false;
  });
}

/** 渲染状态 */
function getStatusLabel(scope) {
  return statusMaps?.value?.find((item) => item?.value === scope?.row?.status)
    ?.label;
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {};
  proxy.resetForm("applicationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  createDate.value = [];
  editDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  open.value = true;
  title.value = "添加老师";
}

/** 修改按钮操作 */
async function handleUpdate(row, type) {
  if (type === 'check') {
    router.push(`/teachers-and-students/teachers/detail?id=${row.id}`);
    return;
  }
  // if (type === 'edit') {
  open.value = true;
  title.value = type === 'edit' ? "编辑老师" : "查看机构";
  console.log('row', row)
  form.value = {
    ...row,
    roleId: 2
  };
    // return
  // }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["applicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateTeacher(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTeacher({
          ...form.value,
        }).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          // router.push("/application/bot/" + response?.data);
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.applicationName + '"的数据项?')
    .then(function () {
      return updateApplicationContent({ id: row?.id, del: 1 });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
  proxy.$modal
    .confirm(
      `是否确认${row?.status != '1' ? "启用" : "禁用"}名称为"${
        row.name
      }"的数据项?`
    )
    .then(function () {
      return changeTeacherDisable({
        id: row?.id,
        optType: row?.status ? 0 : 1,
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess(`${row?.status != '1' ? "启用" : "禁用"}成功`);
    })
    .catch(() => {});
}
getList();
</script>
<style scoped>
.info-box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-start;
  width: 100%;
}
.info-box div {
  width: 100%;
  margin-bottom: 5px;
}
</style>
