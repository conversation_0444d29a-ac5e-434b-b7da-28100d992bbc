<template>
  <div class="app-container">
    <!-- 基础信息 -->
    <el-card class="box-card">
      <!-- <template #header>
        <div class="card-header">
          <span>基础信息</span>
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdateBasicInfo"
            v-hasPermi="['organization:user:edit']"
          >编辑</el-button>
        </div>
      </template> -->
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="info-item">
            <el-avatar :src="studentInfo.photo || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" :size="60">
            </el-avatar>
            <span class="info-label" style="margin-left: 20px;"  v-if="!userStore.orgId" >所属机构：</span>
            <span class="info-content"  v-if="!userStore.orgId">{{ studentInfo.orgName }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">学员ID：</span>
            <span class="info-content">{{ studentInfo.id }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">学员姓名：</span>
            <span class="info-content">{{ studentInfo.nickname }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">用户电话：</span>
            <span class="info-content">{{ studentInfo.phone }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">性别：</span>
            <span class="info-content">
              <dict-tag :options="user_sex" :value="studentInfo.sex" />
            </span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">年龄：</span>
            <span class="info-content">{{ studentInfo.age }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">学生号：</span>
            <span class="info-content">{{ studentInfo.no }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="info-label">微信：</span>
            <span class="info-content">{{ studentInfo.wechat }}</span>
          </div>
        </el-col>
        <el-col :span="24" style="text-align: right;">
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdateBasicInfo"
            v-hasPermi="['organization:user:edit']"
          >编辑</el-button>
        </el-col>
      </el-row>
     
    </el-card>

    <!-- 标签页 -->
    <el-card class="box-card mt20">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="作品" name="works">
          <div class="tab-container">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-table v-loading="worksLoading" :data="worksList" stripe style="width: 100%">
                  <el-table-column type="index" label="序号" width="60" align="center" />
                  <el-table-column prop="id" label="作品ID" min-width="120" align="center" />
                  <el-table-column label="封面图展示" min-width="150" align="center">
                    <template #default="scope">
                      <el-image 
                        style="width: 100px; height: 100px" 
                        :src="scope.row.workType === 2 ? scope.row.taskImage : scope.row.cover" 
                        fit="cover"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                    </template>
                  </el-table-column>
                  <el-table-column prop="workType" label="类型" min-width="120" align="center">
                    <template #default="scope">
                      {{ scope.row.workType === 2 ? '图片' : '视频' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="ctime" label="保存时间" min-width="160" align="center">
                    <template #default="scope">
                      <span>{{ formatDate(scope.row.ctime) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="200">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        @click="handlePreview(scope.row)"
                      >查看</el-button>
                      <!-- <el-button
                        link
                        type="primary"
                        @click="handleDownload(scope.row)"
                      >下载</el-button> -->
                    </template>
                  </el-table-column>
                </el-table>
                <pagination
                  v-show="worksTotal > 0"
                  :total="worksTotal"
                  v-model:page="worksQueryParams.pageNum"
                  v-model:limit="worksQueryParams.pageSize"
                  @pagination="getWorksList"
                />
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="账号" name="account">
          <div class="tab-container">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <span class="info-label">学号：</span>
                  <span class="info-content">{{ studentInfo.no }}</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="info-item">
                  <span class="info-label">密码：</span>
                  <span class="info-content">
                    <password :password="'********'" :id="studentInfo.id" :account="studentInfo.no"/>
                  </span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="info-item">
                  <span class="info-label">账号状态：</span>
                  <span class="info-content">
                    <dict-tag :options="account_status" :value="studentInfo.status" />
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 查看作品对话框 -->
    <el-dialog
      v-model="previewDialog.open"
      :title="previewDialog.title"
      width="800px"
      append-to-body
    >
      <div class="preview-container" v-if="previewDialog.currentWork">
        <template v-if="previewDialog.currentWork.workType === 1">
          <video 
            v-if="previewDialog.currentWork.taskImage" 
            :src="previewDialog.currentWork.taskImage" 
            controls 
            style="max-width: 100%; max-height: 600px;"
          ></video>
        </template>
        <template v-else>
          <el-image 
            v-if="previewDialog.currentWork.taskImage" 
            :src="previewDialog.currentWork.taskImage" 
            style="max-width: 100%; max-height: 600px;"
            fit="contain"
          ></el-image>
        </template>
      </div>
    </el-dialog>

    <!-- 编辑基础信息对话框 -->
    <el-dialog
      :title="basicInfoDialog.title"
      v-model="basicInfoDialog.open"
      width="650px"
      append-to-body
    >
      <el-form
        ref="basicInfoFormRef"
        :model="basicInfoForm"
        :rules="basicInfoRules"
        label-width="150px"
      >
        <el-row>
          <el-col :span="20">
            <el-form-item label="学员姓名" prop="nickname">
              <el-input
                v-model="basicInfoForm.nickname"
                placeholder="请输入学员姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="学员电话" prop="phone">
              <el-input
                v-model="basicInfoForm.phone"
                placeholder="请输入学员电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="学员微信" prop="wechat">
              <el-input
                v-model="basicInfoForm.wechat"
                placeholder="请输入学员微信"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="学员学号" prop="no">
              <el-input
                v-model="basicInfoForm.no"
                placeholder="请输入学员学号"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="20">
            <el-form-item label="学员年龄" prop="age">
              <el-input
                v-model="basicInfoForm.age"
                placeholder="请输入学员年龄"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="学员性别" prop="sex">
              <el-radio-group v-model="basicInfoForm.sex">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="20">
            <el-form-item label="学员头像" prop="photo">
              <image-upload
                :isShowTip="false"
                :multiple="false"
                v-model="basicInfoForm.photo"
                list-type="picture-card"
                :limit="1"
                customTip="请上传1张1寸免冠证件照"
              ></image-upload>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBasicInfoForm">确 定</el-button>
          <el-button @click="cancelBasicInfo">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Picture } from '@element-plus/icons-vue';
import { getUserDetail, updateUser, listUserWorks, downloadUserWork } from '@/api/teachersAndStudents';
import { formatDate } from '@/utils/index';
// 导入getCurrentInstance
import { getCurrentInstance } from 'vue';
// 修改导入方式，使用导入加默认导出
import password from './password.vue';

// 定义学生信息接口
interface IStudentInfo {
  id: string;
  nickname: string;
  phone: string;
  wechat: string;
  no: string;
  age: number | string;
  sex: number;
  status: string | number;
  orgName: string;
  photo?: string;
  [key: string]: any; // 允许其他属性
}

// 定义作品项目接口
interface IWorkItem {
  id: string;
  workType: number; // 1=视频 2=图片
  taskImage: string;
  ctime: string;
  [key: string]: any;
}

const route = useRoute();
const router = useRouter();
import useUserStore from '@/store/modules/user'

// 安全地获取实例和字典数据
const instance = getCurrentInstance();
// @ts-ignore - 忽略类型检查以解决Vue内部API使用问题
const proxy = instance?.appContext.config.globalProperties;
// @ts-ignore - 同上
const { user_sex, teacher_status: account_status } = proxy?.useDict ? proxy.useDict("user_sex", "teacher_status") : { user_sex: [], teacher_status: [] };

// 基础数据
const studentId = route.query.id as string;
const studentInfo = ref<IStudentInfo>({
  id: '',
  nickname: '',
  phone: '',
  wechat: '',
  no: '',
  age: '',
  sex: 1,
  status: '',
  orgName: ''
});
const loading = ref(true);
const activeTab = ref('works');
const userStore = useUserStore()

// 基础信息对话框
const basicInfoDialog = ref({
  open: false,
  title: "编辑学员信息"
});
const basicInfoFormRef = ref();
const basicInfoForm = ref<Partial<IStudentInfo>>({});
const basicInfoRules = {
  nickname: [
    { required: true, message: "学员姓名不能为空", trigger: "blur" }
  ],
  // phone: [
  //   { required: true, message: "学员电话不能为空", trigger: "blur" },
  //   { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  // ],
  no: [
    { required: true, message: "学员学号不能为空", trigger: "blur" }
  ]
};

// 作品列表
const worksList = ref<IWorkItem[]>([]);
const worksTotal = ref(0);
const worksLoading = ref(false);
const worksQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  uid: undefined as string | undefined
});

// 作品预览
const previewDialog = ref({
  open: false,
  title: "作品预览",
  currentWork: null as IWorkItem | null
});

/** 获取学员详情 */
const getStudentDetail = async () => {
  loading.value = true;
  try {
    const res = await getUserDetail(studentId);
    if (res.code === 200) {
      studentInfo.value = res.data as IStudentInfo;
      // 设置作品查询参数中的学员ID
      worksQueryParams.value.uid = studentInfo.value.id;
      // 查询作品列表
      getWorksList();
    } else {
      ElMessage.error(res.msg || '获取学员详情失败');
    }
  } catch (error) {
    console.error('获取学员详情出错:', error);
    ElMessage.error('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
};

/** 获取作品列表 */
const getWorksList = async () => {
  worksLoading.value = true;
  try {
    const res = await listUserWorks(worksQueryParams.value);
    if (res.code === 200) {
      worksList.value = res.data.records || [];
      worksTotal.value = res.data.total || 0;
    } else {
      ElMessage.error(res.msg || '获取作品列表失败');
    }
  } catch (error) {
    console.error('获取作品列表出错:', error);
    ElMessage.error('系统异常，请稍后重试');
  } finally {
    worksLoading.value = false;
  }
};

/** 打开编辑基础信息对话框 */
const handleUpdateBasicInfo = () => {
  basicInfoDialog.value.open = true;
  basicInfoForm.value = {
    id: studentInfo.value.id,
    nickname: studentInfo.value.nickname,
    phone: studentInfo.value.phone,
    wechat: studentInfo.value.wechat,
    no: studentInfo.value.no,
    age: studentInfo.value.age,
    sex: studentInfo.value.sex
  };
};

/** 提交基础信息表单 */
const submitBasicInfoForm = () => {
  basicInfoFormRef.value.validate((valid: boolean) => {
    if (valid) {
      updateUser({
        ...studentInfo.value,
        ...basicInfoForm.value,
      }).then((response: any) => {
        if (response.code === 200) {
          ElMessage.success("修改成功");
          basicInfoDialog.value.open = false;
          getStudentDetail();
        } else {
          ElMessage.error(response.msg || '修改失败');
        }
      });
    }
  });
};

/** 取消编辑基础信息 */
const cancelBasicInfo = () => {
  basicInfoDialog.value.open = false;
};

/** 查看作品 */
const handlePreview = (row: IWorkItem) => {
  previewDialog.value.currentWork = row;
  previewDialog.value.open = true;
};

/** 下载作品 */
const handleDownload = async (row: IWorkItem) => {
  try {
    const res = await downloadUserWork(row.id);
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement('a');
    link.href = url;
    // 设置文件名，根据类型区分
    const extension = row.workType === 1 ? '.mp4' : '.png';
    link.setAttribute('download', `学员作品_${row.id}${extension}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    ElMessage.success('下载成功');
  } catch (error) {
    console.error('下载出错:', error);
    ElMessage.error('下载失败，请稍后重试');
  }
};

// 页面初始化
onMounted(() => {
  if (!studentId) {
    ElMessage.error('未找到学员ID');
    router.push('/teachersAndStudents/students');
    return;
  }
  getStudentDetail();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mt20 {
  margin-top: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.info-label {
  min-width: 80px;
  font-weight: bold;
  color: #606266;
}

.info-content {
  flex: 1;
  word-break: break-all;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

.tab-container {
  padding: 20px 0;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
</style> 