<template>
  <div class="image-cut-diagram">
    <IMImageCutDiagram :src="src" @save="file => handleSaveSplit(file)" @close="close" />
  </div>
</template>

<script setup lang="ts">
import { ossClient, blobToBase64, resizeBase64Img } from '@/components/TUIKit/utils/tool'
import { jsbridge } from 'msb-public-library'
import { ref } from 'vue'
import { LocationQueryValue, useRoute } from 'vue-router'
import IMImageCutDiagram from '@/components/IMImageCutDiagram/index.vue'

const route = useRoute()

const { query } = route
const { url } = query
const canClose = ref(true)

// 判断key可能是数组或者字符串或者null，取数组的第一项或者字符串或者null
const getQueryValue = (key: LocationQueryValue | LocationQueryValue[]): string => {
  if (Array.isArray(key)) {
    return `${key[0]}`
  }
  return `${key}`
}

const src = ref(getQueryValue(url))

const handleSaveSplit = async (file: File) => {
  canClose.value = false
  const url = await ossClient().upload(file)
  const base64 = await blobToBase64(file)
  const img = await resizeBase64Img(base64, 1000, false)
  const params = {
    url,
    width: img?.width,
    height: img?.height,
  }
  console.log('onImageMattingResult', params)
  jsbridge?.sendMsg({
    action: 'onImageMattingResult',
    params,
  })
  canClose.value = true
  close()
}
const close = () => {
  console.log('close', canClose)
  if (canClose.value) {
    jsbridge?.sendMsg({
      action: 'backAction',
    })
  }
}
</script>

<style lang="scss" scoped>
.image-cut-diagram {
}
</style>
