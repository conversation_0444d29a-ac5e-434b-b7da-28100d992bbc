<template>
  <div class="common-layout">
    <el-container>
      <el-header>
        <el-page-header @back="goBack" class="custom-page-header">
          <template #content>
            <div class="title-content">
              <span>
                {{ applicationName }}
              </span>
              <el-button @click="handleClick" type="text" icon="Edit" />
            </div>
          </template>
          <template #extra>
            <div class="flex items-center">
              <el-button
                type="primary"
                @click="saveMarkdown"
                :loading="releaseLoadingStatus"
                v-hasPermi="['app:application:edit']"
                >发布</el-button
              >
            </div>
          </template>
        </el-page-header></el-header
      >
      <el-main>
        <el-row>
          <el-col :span="8">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>人设与回复逻辑</span>
                </div>
              </template>

              <mavon-editor
                :language="'zh-CN'"
                class="mavon-content"
                :placeholder="'请输入脚本'"
                :toolbarsFlag="false"
                :subfield="false"
                v-model="markdownContent"
              />
              <div class="btn-box">
                <el-button
                  type="primary"
                  @click="handleSaveContent"
                  :loading="saveLoadingStatus"
                  v-hasPermi="['app:application:save']"
                  >保存</el-button
                >
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>技能</span>
                </div>
              </template>
              <div class="plugin" @click="handlePluginClick">
                <el-icon>
                  <ArrowRightBold v-if="arrowStatus" />
                  <ArrowDownBold v-else /> </el-icon
                >插件
              </div>

              <el-row :gutter="20" v-show="!arrowStatus">
                <el-col v-for="(item, index) in skillList" :key="item">
                  <div class="skill-item-box">
                    <div class="skill-name">
                      {{ item.name }}
                    </div>
                    <div class="skill-icon">
                      <el-button
                        type="text"
                        class="copy-btn"
                        :data-clipboard-text="JSON.stringify(item.value)"
                        @click="handleCopyClick"
                      >
                        <el-icon
                          ><DocumentCopy style="color: #b4b5b6" /></el-icon
                      ></el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <div>预览</div>
                </div>
              </template>
              <Chat :to-account="id" :key="componentKey" />
            </el-card>
          </el-col> </el-row
      ></el-main>
    </el-container>
    <application-dialog
      @success="success"
      ref="applicationDialog"
      :detail="detailValue"
    />
  </div>
</template>

<script setup>
import {
  updateApplicationContent,
  getApplicationDetail,
  saveApplicationContent,
  getSkillList,
} from "@/api/application/list";
import Chat from "@/components/Chat/index.vue";
import { ElNotification } from "element-plus";
import ClipboardJS from "clipboard";
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import AuthModal from "@/plugins/auth";
import ApplicationDialog from "../list/ApplicationDialog.vue";
const { hasPermi } = AuthModal;
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const markdownContent = ref(`## 角色 
你是一个教8-15岁孩子做3D卡通形象设计的老师，通过如下步骤子以一问一答的形式 进行这节我的Q版3D形象设计课程课程，这个设计过程包含手绘和AIGC应用等。  
## 开场白
问好，并将如下创作步骤反馈给小朋友：
①、手绘形象线稿
②、创作2D形象
③、创作3D形象
④、创作3D模型
⑤、3D打印模型 
## 步骤流程
步骤1、开场白结束后,询问用户性别是男孩还是女孩;
步骤2、用户确认性别后,根据性别询问用户希望自己有一个什么样的发型（根据男孩和女孩的不同给出不同的建议）;
步骤3、用户确认发型后,只能询问用户喜欢的头发颜色;
步骤4、用户确认头发颜色后,只能询问用户希望自己穿着怎样的衣服;
步骤5、用户确认穿着后,给小朋友展示4种可选的姿势,4种姿势：1、站立双手插兜、2、坐着双手托腮、3、站立双手环胸、4、站立双手自然垂下、引导用户选择姿势或姿势对应的数字;
步骤6、询问用户是否喜欢这张图片{"function":"callServerTxt2Img","param":{"prompt":"结合以上对话内容，逐一提取出用户答案中性别、发型、头发颜色、穿着以及所选姿势等关键信息，并将这些信息整理成可直接用于生图软件的提示词，提示词中不展示性别、发型、穿着等标题，英文内容输出，不需要json","styleId":1}};
步骤7、用户回复不喜欢则重新执行步骤7，用户回复喜欢则执行步骤8;
步骤8、引导用户画完线稿后拍照上传图片{"function":"callNativeCamera"};
步骤9、引导用户回复是否使用这张照片;
步骤10、用户回复不使用则执行步骤9,用户回复使用则执行步骤11;
步骤11、询问用户是否喜欢这张图片,{"function":"callServerImg2Img","param":{"imageUrl":"输出格式为字符串，不需要json，获取用户确认的图片链接,输出结果为url路径,不展示其他文字内容，只展示英文字母","prompt":"结合以上对话内容，逐一提取出用户答案中性别、发型、穿着以及所选姿势、头发颜色等关键信息，并将这些信息整理成可直接用于生图软件的提示词，提示词中不展示性别、发型、穿着等标题，英文内容输出，不需要json","styleId":"2"}};
步骤12、用户回复不喜欢则重新执行步骤11,用户回复喜欢则执行步骤13;
步骤13、选择3D形象风格模型引导用户进入下一步,{"function":"callServerStyleList"};
步骤14、询问用户是否喜欢这张图片{"function":"callServerImg2Img","param":{"imageUrl":"输出格式为字符串，不需要json，获取用户确认的图片链接,输出结果为url路径,不展示其他文字内容，只展示英文字母","prompt":"结合以上对话内容，逐一提取出用户答案中性别、发型、穿着以及所选姿势、头发颜色等关键信息，并将这些信息整理成可直接用于生图软件的提示词，提示词中不展示性别、发型、穿着等标题，英文内容输出，不需要json","styleId":"3"}};
步骤15、用户回复不喜欢则重新执行步骤14，用户回复喜欢则执行步骤16;
步骤16、将3D形象图片连接变为二维码展示出来;

### 限制
严格按照步骤流程逐一进行，不允许回归和跳步骤
不要出现有关色情、政治、性骚扰、暴力及不符合社会主义核心价值观的文字
如果用户回答的内容与当前步骤无关，需要引导用户回答的内容回到当前步骤`);
const form = reactive({
  applicationName: "",
  applicationDesc: "",
  applicationAvatar: "",
  applicationType: "",
});
const { applicationName, applicationDesc, applicationAvatar, applicationType } =
  toRefs(form);
const arrowStatus = ref(true);
const saveLoadingStatus = ref(false);
const releaseLoadingStatus = ref(false);
const applicationDialog = ref(null);
const id = ref("");
const skillList = ref([]);
const componentKey = ref(0);
const detailValue = ref({});
id.value = route?.params?.id;
function saveMarkdown() {
  releaseLoadingStatus.value = true;
  updateApplicationContent({
    id: route?.params?.id,
    contentDetail: markdownContent?.value,
  })
    .then((res) => {
      proxy.$modal.alertSuccess("发布成功!");
      releaseLoadingStatus.value = false;
      getDetail();
    })
    .catch(() => {
      releaseLoadingStatus.value = false;
    });
}
function handlePluginClick() {
  if (!skillList?.value) {
    return;
  }
  arrowStatus.value = !arrowStatus.value;
}
function goBack() {
  router.back();
}

function handleClick() {
  if (applicationDialog.value) {
    applicationDialog.value.hadnleOpen();
  }
}

function handleCopyClick(e) {
  const clipboard = new ClipboardJS(".copy-btn");
  clipboard.on("success", (e) => {
    ElNotification({
      title: "成功",
      message: "复制成功",
      type: "success",
    });
    e.clearSelection();
    clipboard.destroy();
  });

  clipboard.on("error", () => {
    ElNotification({
      title: "错误",
      message: "复制失败",
      type: "error",
    });
    clipboard.destroy();
  });
}
// function handleSubmitClick() {
//   updateApplicationContent({
//     id: route?.params?.id,
//     applicationName: applicationName?.value,
//   }).then((res) => {
//     proxy.$modal.msgSuccess("修改成功!");
//     getDetail();

//   });
// }
function handleSaveContent() {
  saveLoadingStatus.value = true;
  saveApplicationContent({
    id: route?.params?.id,
    contentDetail: markdownContent?.value,
  })
    .then((res) => {
      proxy.$modal.alertSuccess("保存成功!");
      saveLoadingStatus.value = false;
      // getDetail();
    })
    .catch(() => {
      saveLoadingStatus.value = false;
    });
}
function getDetail() {
  const id = route.params.id;
  getApplicationDetail(id).then((res) => {
    markdownContent.value = res?.data?.contentDetail || markdownContent.value;
    applicationName.value = res?.data?.applicationName;
    detailValue.value = res?.data || {};
  });
}
function getSkillData() {
  getSkillList().then((res) => {
    skillList.value = res?.data || [];
  });
}
function success() {
  getDetail();
  componentKey.value += 1; //
}
onMounted(() => {
  getDetail();
  if (hasPermi("app:application:skill:list")) {
    getSkillData();
  }
});
</script>
<style scoped lang="scss">
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  width: 100%;
  height: 100vh; /* 视口高度，使布局占满整个屏幕 */

  .el-container {
    height: 100%; /* 使容器占满父元素高度 */
    display: flex;
    flex-direction: column; /* 子元素垂直排列 */

    .el-header {
      padding: 0 0; /* 可选，设置头部内边距 */
    }

    .el-main {
      overflow: hidden !important;

      .main-content {
        flex: 1; /* 关键点：使主内容区域占据剩余空间 */
        overflow-y: auto; /* 如果内容超出，允许垂直滚动 */
        padding: 20px; /* 可选，设置主内容区域内边距 */
      }

      .btn-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }

      .skill-item-box {
        width: 100%;
        height: 35px;
        padding: 10px 0 10px 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-radius: 5px;
        border-bottom: 1px solid #ebeef5;

        &:hover {
          background-color: #eff1f8;
        }

        .skill-name,
        .skill-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          width: 20%;
        }

        .skill-icon .el-icon {
          font-size: 20px;
          cursor: pointer;
        }
      }

      .bot-contain {
        width: 100vw;
        height: 100vh;
      }

      .mavon-content {
        height: 700px;
      }

      .box-card {
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: auto;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 800;
        }

        .plugin {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
        }
      }
    }
  }

  .custom-page-header {
    /* 自定义样式 */
    background-color: #f0f0f0;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    cursor: pointer;

    .el-page-header__left {
      /* 自定义返回按钮样式 */
      color: #1890ff;
      font-size: 18px;
    }

    .el-page-header__content {
      /* 自定义标题样式 */
      font-size: 20px;
      font-weight: bold;
      color: #333;
    }
  }

  .edit {
    font-size: 14px;
    margin-left: 10px;
  }

  .title-content {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>
