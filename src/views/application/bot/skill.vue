<!--
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-02-26 10:33:45
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-04 10:27:31
 * @FilePath: /miaobi-admin-magic-touch/src/views/application/bot/plugin.vue
 * @Description: 插件管理组件 - 用于展示和管理技能列表
-->
<template>
  <div class="plugin" :class="{ expanded: isPanelVisible }">
    <!-- 插件面板头部 -->
    <div class="plugin-header" @click="togglePanel">
      <div class="icon-box">
        <el-icon><component :is="panelIcon" /></el-icon>
      </div>
      <div class="title">插件</div>
    </div>

    <!-- 插件列表内容区 -->
    <el-collapse-transition>
      <div
        class="grid-container"
        v-show="isPanelVisible"
        v-if="skillList?.length"
      >
        <div v-for="item in skillList" :key="item.value" class="grid-item">
          <div class="item-name">{{ item.name }}</div>
          <div>
            <el-button
              type="text"
              class="copy-btn"
              @click="() => handleCopy(item.value)"
              :title="'复制' + item.name"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, computed } from "vue";
import { ElNotification } from "element-plus";
import {
  ArrowRightBold,
  ArrowDownBold,
  DocumentCopy,
} from "@element-plus/icons-vue";

// 定义技能项的类型接口
interface SkillItem {
  name: string;
  value: any;
}

// 定义组件属性
const props = defineProps<{
  skillList: SkillItem[]; // 技能列表数据
}>();

// 定义emit
const emit = defineEmits(["insertContent"]);

const { skillList } = toRefs(props);

// 控制面板展开/收起状态
const isPanelVisible = ref(false);

// 计算面板图标
const panelIcon = computed(() =>
  isPanelVisible.value ? "ArrowDownBold" : "ArrowRightBold"
);

// 切换面板显示状态
const togglePanel = () => {
  isPanelVisible.value = !isPanelVisible.value;
};

/**
 * 复制技能值到剪贴板
 * @param value - 要复制的值
 */
const handleCopy = async (value: any) => {
  try {
    // await navigator.clipboard.writeText(JSON.stringify(value));
    // 触发插入内容事件
    emit("insertContent", JSON.stringify(value));
    ElNotification({
      title: "成功",
      message: "插入成功",
      type: "success",
      duration: 2000,
    });
  } catch (error) {
    console.error("复制失败:", error);
    ElNotification({
      title: "错误",
      message: "插入失败，请重试",
      type: "error",
      duration: 3000,
    });
  }
};
</script>

<style lang="scss" scoped>
.plugin {
  // 定义常用变量
  $header-height: 40px;
  $container-width: 100%;
  $border-radius: 5px;
  $transition-time: 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10px;
  border-bottom: 1px solid rgba(87, 104, 161, 0.1);
  transition: border-bottom $transition-time ease;

  &:not(.expanded):hover {
    border-bottom-color: transparent;
  }

  // 头部样式
  &-header {
    display: flex;
    align-items: center;
    height: $header-height;
    width: $container-width;
    border-radius: $border-radius;
    cursor: pointer;
    transition: background-color $transition-time ease;

    &:hover {
      background: rgba(87, 104, 161, 0.08);
    }

    .icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: $header-height;
      height: $header-height;
    }

    .title {
      font-size: 16px;
      margin-left: 5px;
      line-height: $header-height;
      font-weight: 500;
    }
  }

  // 网格容器样式
  .grid-container {
    width: $container-width;
    margin: 10px;
    display: grid;
    grid-template-columns: auto;
    grid-gap: 10px;
  }

  // 网格项样式
  .grid-item {
    background: #fff;
    border-radius: 8px;
    padding: 10px 8px;
    min-height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color $transition-time ease;

    &:hover {
      background: #ebecf5;
    }

    .item-name {
      word-break: break-word;
      margin-right: 10px;
      flex: 1;
    }

    .copy-btn {
      :deep(.el-icon) {
        color: #b4b5b6;
        transition: color $transition-time ease;

        &:hover {
          color: #409eff;
        }
      }
    }
  }
}
</style>