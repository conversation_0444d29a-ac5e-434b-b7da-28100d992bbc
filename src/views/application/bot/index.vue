/**
 * Bot应用编辑页面
 * 包含三个主要部分：
 * 1. 左侧：人设与回复逻辑编辑区
 * 2. 中间：插件配置区
 * 3. 右侧：预览聊天窗口
 */
<template>
  <div class="common-layout">
    <header>
      <div class="header-left">
        <el-button
          type="link"
          link
          icon="ArrowLeft"
          @click="goBack"
          class="back"
        />
        <div class="agent">
          <div class="ava">
            <img :src="applicationAvatar" alt="头像" />
          </div>
          <div class="agent-name">{{ applicationName }}</div>
          <el-button
            type="link"
            link
            icon="Edit"
            class="edit"
            @click="handleClick"
          />
        </div>
      </div>
      <el-button
        type="primary"
        @click="saveMarkdown"
        :loading="releaseLoadingStatus"
        v-hasPermi="['app:application:edit']"
        >{{ releaseLoadingStatus ? "发布中..." : "发布" }}</el-button
      >
    </header>
    <div class="title-box">
      <div class="left-title">人设与回复逻辑</div>
      <div class="middle-title">插件</div>
      <div class="right-title">预览</div>
    </div>
    <div class="contain">
      <div class="left-content">
        <Orchestration
          :skillList="skillList"
          :markdownContentDetail="markdownContent"
          @getMarkdownContent="getMarkdownContent"
          ref="orchestrationRef"
        />
      </div>
      <div class="middle-content">
        <Skill
          :skillList="skillList"
          @insert-content="
            (content) => orchestrationRef.insertContentAtCursor(content)
          "
        />
      </div>
      <div class="right-content">
        <Chat :to-account="id" :key="componentKey" type="c2c" />
      </div>
    </div>
    <application-dialog
      @success="success"
      ref="applicationDialog"
      :detail="detailValue"
    />
  </div>
</template>

<script setup>
/**
 * 导入所需的API和组件
 */
import {
  updateApplicationContent,
  getApplicationDetail,
  saveApplicationContent,
  getSkillList,
} from "@/api/application/list";
import Chat from "@/components/Chat/index.vue";
import Skill from "./skill.vue";
import TUIChatEngine, {
  TUITranslateService,
} from "@tencentcloud/chat-uikit-engine";
import { ref, reactive, getCurrentInstance, onMounted, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import AuthModal from "@/plugins/auth";
import { getSign } from "@/api/common";
import ApplicationDialog from "../list/ApplicationDialog.vue";
import Orchestration from "./orchestration.vue";
const { hasPermi } = AuthModal;
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

/**
 * 响应式数据定义
 */
const form = reactive({
  applicationName: "", // 应用名称
  applicationDesc: "", // 应用描述
  applicationAvatar: "", // 应用头像
  applicationType: "", // 应用类型
});
const markdownContent = ref(""); // Markdown内容
const { applicationName, applicationDesc, applicationAvatar, applicationType } =
  toRefs(form);
const arrowStatus = ref(true);
const saveLoadingStatus = ref(false);
const releaseLoadingStatus = ref(false);
const applicationDialog = ref(null);
const id = ref("");
const skillList = ref([]); // 技能列表
const componentKey = ref(0); // 用于强制刷新Chat组件
const detailValue = ref({});
const orchestrationRef = ref(null);
id.value = route?.params?.id;

/**
 * 保存并发布Markdown内容
 */
function saveMarkdown() {
  releaseLoadingStatus.value = true;
  updateApplicationContent({
    id: route?.params?.id,
    contentDetail: markdownContent?.value,
  })
    .then((res) => {
      proxy.$modal.alertSuccess("发布成功!");
      releaseLoadingStatus.value = false;
      getDetail();
    })
    .catch(() => {
      releaseLoadingStatus.value = false;
    });
}

/**
 * 返回上一页
 */
function goBack() {
  router.back();
}

/**
 * 打开应用编辑对话框
 */
function handleClick() {
  if (applicationDialog.value) {
    applicationDialog.value.handleOpen();
  }
}

/**
 * 获取应用详情数据
 */
function getDetail() {
  const id = route.params.id;
  getApplicationDetail(id).then((res) => {
    markdownContent.value = res?.data?.contentDetail || "";
    applicationName.value = res?.data?.applicationName;
    applicationDesc.value = res?.data?.applicationDesc;
    applicationAvatar.value = res?.data?.applicationAvatar;
    applicationType.value = res?.data?.applicationType;
    detailValue.value = res?.data || {};
  });
}

/**
 * 获取技能列表数据
 */
function getSkillData() {
  getSkillList().then((res) => {
    skillList.value = res?.data || [];
  });
}

/**
 * 更新Markdown内容的回调函数
 * @param {string} value - 新的Markdown内容
 */
const getMarkdownContent = (value) => {
  markdownContent.value = value;
};

/**
 * 应用编辑成功后的回调函数
 * 重新获取详情并刷新Chat组件
 */
function success() {
  getDetail();
  componentKey.value += 1; //
}

/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  const init = () => {
    getSign().then((res) => {
      const initLogin = async () => {
        try {
          await TUIChatEngine.login({
            SDKAppID: import.meta.env.VITE_APP_BACK_END_TENCENT_IM_SDK_APP_ID,
            userID: res.data?.userId,
            userSig: res.data?.sig,
            useUploadPlugin: true, // 是否开启上传插件，true 为开启。即时通信 Chat SDK 发送图片、语音、视频、文件等消息需要使用上传插件，将文件上传到腾讯云对象存储。
          });
        } catch (error) {}
      };
      initLogin();
    });
  };
  if (hasPermi("txy:im:user:sig")) {
    init();
  }
  getDetail();
  if (hasPermi("app:application:skill:list")) {
    getSkillData();
  }
});

/**
 * 组件卸载时执行退登操作
 */
onUnmounted(() => {
  if (hasPermi("txy:im:user:sig")) {
    try {
      TUIChatEngine.logout();
    } catch (error) {}
  }
});
</script>

<style scoped lang="scss">
/**
 * 页面整体布局样式
 */
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  width: 100%;
  height: 100vh; /* 视口高度，使布局占满整个屏幕 */
  header {
    align-items: center;
    border-bottom: 1px solid rgba(82, 100, 154, 0.13);
    display: flex;
    height: 74px;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px;
    z-index: 999;
    .header-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      .back {
        padding: 4px;
        width: 32px;
        font-size: 24px;
      }
      .agent {
        height: 32px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        .ava {
          height: 32px;
          width: 32px;
          border-radius: 4px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        .agent-name {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 500;
        }
        .edit {
          margin-left: 10px;
        }
      }
    }
  }
  .title-box {
    display: flex;
    flex-direction: row;

    border-bottom: 1px dashed rgba(82, 100, 154, 0.13);
    .left-title {
      font-size: 16px;
      font-weight: 500;
      width: 35%;
      padding: 16px;
      border-right: 1px solid rgba(28, 29, 35, 0.12);
      background: rgba(252, 252, 255, 1);
    }
    .middle-title {
      font-size: 16px;
      font-weight: 500;
      width: 35%;
      padding: 16px;
      border-right: 1px solid rgba(28, 29, 35, 0.12);
      background: rgba(252, 252, 255, 1);
    }
    .right-title {
      font-size: 16px;
      font-weight: 500;
      width: 30%;
      padding: 16px;
    }
  }
  .contain {
    display: flex;
    flex: 1;
    overflow: auto;
    // margin-bottom: 20px;
    // border-bottom: 1px solid rgba(28, 29, 35, 0.12);
    .left-content {
      display: flex;
      width: 35%;
      background: rgba(252, 252, 255, 1);
      border-right: 1px solid rgba(28, 29, 35, 0.12);
    }
    .middle-content {
      display: flex;
      flex-direction: column;
      width: 35%;
      background: rgba(252, 252, 255, 1);
      border-right: 1px solid rgba(28, 29, 35, 0.12);
      overflow-y: scroll;
    }
    .right-content {
      display: flex;
      width: 30%;
      justify-content: flex-start;
      flex-direction: column;
      overflow: hidden;
    }
  }
}
</style>
