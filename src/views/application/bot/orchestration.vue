<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-26 15:32:56
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-02-26 18:45:16
 * @FilePath: /miaobi-admin-magic-touch/src/views/application/bot/orchestration.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-mention
    v-loading="saveLoadingStatus"
    v-model="markdownContent"
    type="textarea"
    :options="options"
    class="custom-mention"
    placeholder="请输入脚本～"
    style="height: 100%"
    :check-is-whole="checkIsWhole"
    resize="none"
    whole
    @blur="handleSaveContent"
  />
</template>
  
<script setup >
import { ref, toRefs, watch } from "vue";
const props = defineProps({
  skillList: {
    type: Array,
    default: () => [],
  },
  markdownContent: {
    type: String,
    default: "",
  },
});
const { skillList, markdownContent } = toRefs(props);

const options = ref([]);

const checkIsWhole = (pattern, prefix) => {
  const patternArr = options.value.map((item) => item.value);
  return patternArr.includes(pattern);
};
function handleSaveContent() {
  saveLoadingStatus.value = true;
  saveApplicationContent({
    id: route?.params?.id,
    contentDetail: markdownContent?.value,
  })
    .then((res) => {
      proxy.$modal.alertSuccess("保存成功!");
      saveLoadingStatus.value = false;
      // getDetail();
    })
    .catch(() => {
      saveLoadingStatus.value = false;
    });
}
watch(
  skillList,
  (newSkillList) => {
    options.value = newSkillList.map((item) => ({
      label: item.name,
      value: JSON.stringify(item.value),
    }));
  },
  { immediate: true }
);
</script>

<style scoped >
:deep(.el-mention) {
  height: 100%; /* 设置 el-mention 的高度为 100% */
  width: 90%;
}
:deep(.el-textarea__inner) {
  height: 90%; /* 设置 el-textarea__inner 的高度为 100% */
  background: rgba(252, 252, 255, 1);
  box-shadow: none;
  width: 90%;
  margin: 0 auto;
}
</style>