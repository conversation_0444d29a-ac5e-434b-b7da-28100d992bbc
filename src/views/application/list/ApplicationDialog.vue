<template>
  <el-dialog
    :title="title"
    v-model="open"
    width="500px"
    append-to-body
    @close="cancel"
  >
    <el-form
      ref="applicationRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="20">
          <el-form-item label="应用名称" prop="applicationName">
            <el-input
              v-model="form.applicationName"
              placeholder="请输入应用名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="应用介绍" prop="applicationDesc">
            <el-input
              type="textarea"
              v-model="form.applicationDesc"
              autosize
              placeholder="请输入应用介绍"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="应用封面图" prop="applicationAvatar">
            <image-upload-old
              :isShowTip="true"
              :multiple="true"
              v-model="form.applicationAvatar"
              list-type="picture-card"
              :limit="1"
            ></image-upload-old>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="应用分类" prop="applicationType">
            <el-select
              v-model="form.applicationType"
              placeholder="应用分类"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in application_classification"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              /> </el-select
          ></el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  addApplication,
  getApplicationList,
  updateApplicationContent,
  getApplicationDetail,
} from "@/api/application/list";
import { useRouter } from "vue-router";
import { ref, toRefs } from "vue";
const router = useRouter();
const { proxy } = getCurrentInstance();
const { application_classification } = proxy.useDict(
  "application_classification"
);
const props = defineProps({
  detail: {
    type: Object,
    default: () => {},
  },
});
const { detail } = toRefs(props);

const data = reactive({
  form: {
    applicationName: "",
    applicationAvatar: "",
    applicationDesc: "",
    applicationType: "",
    id: undefined,
  },
  rules: {
    applicationName: [
      { required: true, message: "应用名称不能为空", trigger: "blur" },
    ],
    applicationDesc: [
      { required: true, message: "应用描述不能为空", trigger: "blur" },
    ],
    applicationAvatar: [
      { required: true, message: "应用封面为必填项", trigger: "blur" },
    ],
    applicationType: [
      { required: true, message: "应用分类不能为空", trigger: "blur" },
    ],
  },
});
const open = ref(false);
const title = ref("");
const emit = defineEmits(["success"]);
const { form, rules } = toRefs(data);
/** 提交按钮 */
function submitForm() {
  proxy.$refs["applicationRef"].validate((valid) => {
    if (valid) {
      if (form.value.id) {
        updateApplicationContent(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          emit("success");
          open.value = false;
        });
      } else {
        addApplication(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          emit("success");
          router.push("/application/bot/" + response?.data);
        });
      }
    }
  });
}
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {};
  proxy.resetForm("applicationRef");
}
function handleOpen() {
  open.value = true;
  title.value = detail?.value?.id ? "编辑应用" : "新增应用";
  if (detail?.value) {
    form.value = {
      applicationName: detail.value.applicationName || "",
      applicationAvatar: detail.value.applicationAvatar || "",
      applicationDesc: detail.value.applicationDesc || "",
      applicationType: detail.value.applicationType + "" || "",
      id: detail.value.id || undefined,
    };
  }
}
defineExpose({
  handleOpen,
});
</script>

