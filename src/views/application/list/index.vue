<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="应用名称" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入应用名称"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="应用分类" prop="applicationType">
        <el-select
          v-model="queryParams.applicationType"
          placeholder="应用分类"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in application_classification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="应用状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in application_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="createDate"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59),
          ]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="编辑时间" style="width: 308px">
        <el-date-picker
          v-model="editDate"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59),
          ]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['app:application:add']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
          >展开/折叠</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch="showSearch"
        :search="false"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="applicationList"
      row-key="id"
    >
      <el-table-column
        prop="id"
        label="序号"
        width="80"
        :fixed="true"
      ></el-table-column>
      <el-table-column prop="applicationAvatar" label="应用封面图" width="100">
        <template #default="scope">
          <div style="width: 80px; height: 80px">
            <image-preview
              :src="scope?.row?.applicationAvatar"
              width="100%"
              height="100%"
              fit="fill"
            ></image-preview>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="applicationName"
        label="应用名称"
        width="160"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        prop="applicationType"
        label="应用分类"
        width="120"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <dict-tag
            :options="application_classification"
            :value="scope.row.applicationType"
          />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="应用状态" width="160">
        <template #default="scope">
          <dict-tag :options="application_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        prop="applicationDesc"
        label="应用介绍"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          {{ scope.row?.applicationDesc }}
        </template>
      </el-table-column>
      <el-table-column
        label="编辑时间"
        align="center"
        prop="updateTime"
        width="200"
      >
        <template #default="scope">
          <span>{{ formatDate(scope.row.mtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="200"
      >
        <template #default="scope">
          <span>{{ formatDate(scope.row.ctime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="210"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['app:application:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Sort"
            v-hasPermi="['app:application:edit']"
            @click="handleChangeStatus(scope.row)"
            >{{ scope?.row.status ? "启用" : "禁用" }}</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            v-hasPermi="['app:application:edit']"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <application-dialog ref="applicationDialog" @success="success" />
  </div>
</template>

<script setup name="application-list">
import { useRouter } from "vue-router";
import {
  addApplication,
  getApplicationList,
  updateApplicationContent,
  getApplicationDetail,
} from "@/api/application/list";
import { formatDate } from "@/utils/index";
import { date2timeStamp } from "../../../utils";
import ApplicationDialog from "./ApplicationDialog.vue";

const { proxy } = getCurrentInstance();
const router = useRouter();
const applicationList = ref([]);
const loading = ref(true);
const showSearch = ref(true);

const applicationDialog = ref(null);
const refreshTable = ref(true);
const createDate = ref([]);
const editDate = ref([]);
const total = ref(0);
const data = reactive({
  queryParams: {
    visible: undefined,
    pageNum: 1,
    pageSize: 10,
  },
});
const { application_status, application_classification } = proxy.useDict(
  "application_status",
  "application_classification"
);
const { queryParams, form, rules, statusMaps } = toRefs(data);

/** 查询菜单列表 */
function getList() {
  loading.value = true;
  getApplicationList({
    ...queryParams.value,
    createStartTime: date2timeStamp(createDate?.value[0]),
    createEndTime: date2timeStamp(createDate?.value[1]),
    updateStartTime: date2timeStamp(editDate?.value[0]),
    updateEndTime: date2timeStamp(editDate?.value[1]),
  }).then((response) => {
    applicationList.value = response?.rows;
    loading.value = false;
    total.value = response.total;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  createDate.value = [];
  editDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  if (applicationDialog.value) {
    applicationDialog.value.handleOpen();
  }
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  const id = row.id;
  router.push("/application/bot/" + id);
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(
      h("div", null, [
        h("p", null, `是否确认删除"${row.applicationName}"的数据项?`),
        h(
          "p",
          { style: "color: #f56c6c; margin-top: 10px" },
          "删除后应用将无法恢复，同时关联应用的课件将无法使用，请谨慎处理。"
        ),
      ])
    )
    .then(function () {
      return updateApplicationContent({ id: row?.id, del: 1 });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
  proxy.$modal
    .confirm(
      `是否确认${row?.status ? "启用" : "禁用"}名称为"${
        row.applicationName
      }"的数据项?`
    )
    .then(function () {
      return updateApplicationContent({
        id: row?.id,
        status: row?.status ? 0 : 1,
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess(`${row?.status ? "启用" : "禁用"}成功`);
    })
    .catch(() => {});
}
function success() {
  getList();
}
getList();
</script>
