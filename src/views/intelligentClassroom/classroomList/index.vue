<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-hasPermi="['system:room:add']">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">创建教室</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" :search="false" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="applicationList" row-key="id">
      <el-table-column prop="classNo" label="id" width="120" :fixed="true"></el-table-column>
      <el-table-column prop="roomName" label="教室名称" width="160" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column v-if="!userStore.orgId" prop="orgName" label="所属机构" width="160" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="deviceNum" label="设备数量" width="120"></el-table-column>
      <el-table-column label="编辑时间" align="center" prop="updateTime" min-width="180">
        <template #default="scope">
          <span>{{ formatDate(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="180">
        <template #default="scope">
          <span>{{ formatDate(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="210" class-name="small-padding fixed-width" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row, 'edit')">编辑</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row, 'check')">查看详情</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    <el-drawer :title="title" v-model="open" append-to-body size="40%">
      <el-form ref="roomRef" :model="roomForm" :rules="rules" label-width="150px" :disabled="title === '查看教室'" label-position="top">
        <el-row>
          <el-col :span="24">
            <el-form-item label="教室名称" prop="roomName">
              <el-input v-model="roomForm.roomName" :maxlength="20" placeholder="请输入教室名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="deviceNum">
              <template #label>
                <el-space style="width: 100%; justify-content: space-between">
                  <div>教室内的设备</div>
                  <el-button link type="primary" @click="handleDevice('', 'add')">添加设备</el-button>
                </el-space>
              </template>
            </el-form-item>
          </el-col>
          <el-divider />
        </el-row>
      </el-form>
      <el-table :data="deviceList" row-key="id" border style="width: 100%">
        <el-table-column prop="seatNo" label="座位号"></el-table-column>
        <el-table-column prop="deviceNo" label="设备序列号"></el-table-column>
        <el-table-column prop="status" label="是否为主机">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" :disabled="true" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" v-if="title !== '查看教室'" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleDevice(scope.row, 'edit')">编辑</el-button>
            <el-button link type="primary" @click="handleDevice(scope.row, 'delete')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogOpen" width="650px" append-to-body :show-close="false">
      <el-form ref="applicationRef" :model="form" :rules="dialogRules" label-width="150px">
        <el-row>
          <el-col :span="20">
            <el-form-item label="座位号" prop="seatNo">
              <el-input v-model="form.seatNo" placeholder="请输入座位号" />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="设备序列号" prop="deviceNo">
              <el-select v-model="form.deviceNo" placeholder="请选择设备序列号" clearable style="width: 100%">
                <el-option v-for="item in equipmentModelList" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="是否为主机" prop="status">
              <el-switch v-model="form.status" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFormDialog">确 定</el-button>
          <el-button @click="cancelDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="institution-list">
import { listRoom, addRoom, updateRoom, delRoom } from '@/api/room'
import { getEquipmentModelList } from '@/api/equipment/index'
import useUserStore from '@/store/modules/user'

import { formatDate } from '@/utils/index'
const { proxy } = getCurrentInstance()
const applicationList = ref([])
const open = ref(false)
const dialogOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const dialogTitle = ref('')
const refreshTable = ref(true)
const createDate = ref([])
const equipmentModelList = ref([])
const editDate = ref([])
const total = ref(0)
const deviceList = ref([])
const userStore = useUserStore()

const data = reactive({
  roomForm: {},
  form: {},
  queryParams: {
    visible: undefined,
    pageNum: 1,
    pageSize: 10,
  },
  dialogRules: {
    deviceNo: [{ required: true, message: '请输入设备序列号', trigger: 'blur' }],
    seatNo: [{ required: true, message: '请输入座位号', trigger: 'blur' }],
  },
  rules: {
    roomName: [{ required: true, message: '请输入教室名称', trigger: 'blur' }],
  },
})

const { org_type: organizationt_ype, account_status } = proxy.useDict('org_type', 'account_status')
const { queryParams, rules, statusMaps, form, roomForm, dialogRules } = toRefs(data)

watch(
  () => form.value.orgEmail,
  (newVal, oldVal) => {
    if (newVal && !oldVal && form.value.account && form.value.account !== newVal) {
      return
    }
    form.value.account = newVal
  }
)
function queryEquipmentModelList() {
  getEquipmentModelList().then(response => {
    console.log('🚀 ~ getEquipmentModelList ~ response:', response)
    equipmentModelList.value = response?.data
  })
}
/** 查询菜单列表 */
function getList() {
  loading.value = true
  listRoom(queryParams.value).then(response => {
    applicationList.value = response?.rows
    loading.value = false
    total.value = response.total
  })
  queryEquipmentModelList()
}

/** 取消按钮 */
function cancel() {
  open.value = false
  resetDrawer()
}
function cancelDialog() {
  dialogOpen.value = false
  reset()
}

function handleDevice(row, type) {
  console.log('deviceList.value', deviceList.value, row)
  dialogTitle.value = type === 'add' ? '添加设备' : '编辑设备'
  if (type === 'edit') {
    dialogTitle.value = '编辑设备'
    dialogOpen.value = true
    form.value = {
      ...row,
    }
    return
  }
  if (type === 'delete') {
    const index = deviceList.value.findIndex(item => item.id === row.id)
    deviceList.value.splice(index, 1)
    return
  }
  dialogOpen.value = true
  dialogTitle.value = '添加设备'
}

/** 表单重置 */
function reset() {
  form.value = {}
  proxy.resetForm('applicationRef')
}
/** 表单重置 */
function resetDrawer() {
  roomForm.value = {}
  proxy.resetForm('roomForm')
  deviceList.value = []
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  createDate.value = []
  editDate.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  resetDrawer()
  open.value = true
  title.value = '添加教室'
}

/** 修改按钮操作 */
async function handleUpdate(data, type) {
  const row = JSON.parse(JSON.stringify(data))
  open.value = true
  title.value = type === 'edit' ? '编辑教室' : '查看教室'
  roomForm.value = {
    roomName: row.roomName,
    id: row.id,
    deviceNum: row.deviceNum,
  }
  deviceList.value = row.deviceList
}
function submitFormDialog() {
  proxy.$refs['applicationRef'].validate(valid => {
    if (valid) {
      // if (deviceList.value.find(item => item.status === '1') && form.value.status === '1') {
      //   proxy.$modal.msgError("教室内只能有一个主机");
      //   return
      // }

      // if (deviceList.value.find(item => item.seatNo === form.value.seatNo) || deviceList.value.find(item => item.deviceNo === form.value.deviceNo)) {
      //   proxy.$modal.msgError("座位号或设备序列号已存在");
      //   return
      // }
      if (dialogTitle.value === '编辑设备') {
        const index = deviceList.value.findIndex(item => item.id === form.value.id)
        deviceList.value[index] = {
          ...form.value,
        }
        dialogOpen.value = false
        reset()
      } else {
        deviceList.value.push({
          ...form.value,
          id: Math.random(),
        })
        dialogOpen.value = false
        reset()
      }
    }
  })
}
/** 提交按钮 */
function submitForm() {
  if (deviceList.value.length === 0) {
    proxy.$modal.msgError('教室内设备不能为空')
    return
  }
  const statusCount = deviceList.value.filter(device => device.status === '1').length
  if (statusCount > 1) {
    proxy.$modal.msgError('教室内只能有一个主机')
    return
  }
  const seatNoSet = new Set()
  const deviceNoSet = new Set()
  for (const device of deviceList.value) {
    if (seatNoSet.has(device.seatNo) || deviceNoSet.has(device.deviceNo)) {
      proxy.$modal.msgError('座位号或设备序列号不能重复')
      return
    }
    seatNoSet.add(device.seatNo)
    deviceNoSet.add(device.deviceNo)
  }
  proxy.$refs['roomRef'].validate(valid => {
    if (valid) {
      const parmas = {
        ...roomForm.value,
        deviceList: deviceList.value,
        deviceNum: deviceList.value.length,
      }
      if (roomForm.value.id != undefined) {
        updateRoom(parmas).then(response => {
          proxy.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        })
      } else {
        addRoom(parmas).then(response => {
          proxy.$modal.msgSuccess('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.roomName + '"的数据项?')
    .then(function () {
      return delRoom(row.id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}
/** 启用禁用按钮操作 */
function handleChangeStatus(row) {
  proxy.$modal
    .confirm(`是否确认${row?.status !== '0' ? '启用' : '禁用'}名称为"${row.orgName}"的数据项?`)
    .then(function () {
      return updateApplicationContent({
        id: row?.id,
        status: row?.status ? 0 : 1,
      })
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess(`${row?.status ? '启用' : '禁用'}成功`)
    })
    .catch(() => {})
}
getList()
</script>
<style scoped>
.info-box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-start;
  width: 100%;
}
.info-box div {
  width: 100%;
  margin-bottom: 5px;
}
</style>
