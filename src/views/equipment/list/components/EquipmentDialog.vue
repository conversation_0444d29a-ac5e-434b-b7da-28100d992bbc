<template>
  <!-- 设备信息编辑对话框 -->
  <el-dialog :title="form.id ? (type === 'detail' ? '查看设备' : '编辑设备') : '新增设备'" v-model="open" width="500px" append-to-body>
    <!-- 设备信息表单 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" :disabled="type === 'detail'">
      <!-- 设备基本信息区域 -->
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="form.deviceName" placeholder="请输入设备名称" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="设备型号" prop="deviceModel">
        <el-input v-model="form.deviceModel" placeholder="请输入设备型号" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="设备品牌" prop="deviceBrand">
        <el-input v-model="form.deviceBrand" placeholder="请输入设备品牌" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="设备序列号" prop="deviceSn">
        <el-input v-model="form.deviceSn" placeholder="请输入设备序列号" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="设备状态" prop="deviceStatus">
        <el-radio-group v-model="form.deviceStatus">
          <el-radio v-for="dict in device_status" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分配状态" prop="dispatchStatus">
        <el-radio-group v-model="form.dispatchStatus">
          <el-radio v-for="dict in allocate_status" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分配机构" prop="orgId" v-if="form.dispatchStatus == 0">
        <el-select v-model="form.orgId" placeholder="请选择分配机构" clearable style="width: 100%">
          <el-option v-for="item in orgList" :key="item.id" :label="item.orgName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息，例如：设备的物理规格、设备配置、配件清单等" maxlength="100" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer v-if="type !== 'detail'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="equipment-dialog">
// 导入API接口函数
import { addEquipment, updateEquipment, getEquipmentDetail, getOrgList } from '@/api/equipment/index'

// 定义组件事件
const emit = defineEmits(['success'])
const { proxy } = getCurrentInstance()

// 组件状态变量
const open = ref(false) // 对话框显示状态
const loading = ref(false) // 加载状态
const orgList = ref([]) // 机构列表数据
const type = ref('') // 类型
// 获取字典数据
const { device_status, allocate_status } = proxy.useDict('device_status', 'allocate_status')

// 表单数据和验证规则
const data = reactive({
  // 表单数据对象
  form: {
    id: undefined,
    deviceName: undefined,
    deviceSn: undefined,
    deviceBrand: undefined,
    deviceStatus: '0',
    dispatchStatus: '1',
    orgId: undefined,
    remark: undefined,
    deviceModel: undefined,
  },
  // 表单验证规则
  rules: {
    deviceName: [
      { required: true, message: '设备名称不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
    ],
    deviceModel: [{ required: true, message: '设备型号不能为空', trigger: 'blur' }],
    deviceBrand: [{ required: true, message: '设备品牌不能为空', trigger: 'blur' }],
    deviceSn: [{ required: true, message: '设备序列号不能为空', trigger: 'blur' }],
    deviceStatus: [{ required: true, message: '设备状态不能为空', trigger: 'change' }],
    dispatchStatus: [{ required: true, message: '分配状态不能为空', trigger: 'change' }],
    orgId: [{ required: true, message: '分配信息不能为空', trigger: 'change' }],
  },
})

const { form, rules } = toRefs(data)

/** 取消操作 */
function cancel() {
  open.value = false
  reset()
}

/** 重置表单数据 */
function reset() {
  form.value = {
    id: undefined,
    deviceName: undefined,
    deviceSn: undefined,
    deviceBrand: undefined,
    deviceStatus: '0',
    dispatchStatus: '1',
    orgId: undefined,
    remark: undefined,
    deviceModel: undefined,
  }
  proxy.resetForm('formRef')
}

/** 查询机构列表 */
function queryOrgList() {
  getOrgList().then(response => {
    orgList.value = response?.data
  })
}

/** 打开对话框 */
function handleOpen(id, play_type = undefined) {
  type.value = play_type
  queryOrgList()
  reset()
  if (id) {
    // 编辑模式：获取设备详情
    getEquipmentDetail(id).then(response => {
      form.value = response.data
      form.value.deviceStatus = response.data.deviceStatus + ''
      form.value.dispatchStatus = response.data.dispatchStatus + ''
      open.value = true
    })
  } else {
    // 新增模式
    open.value = true
  }
}

/** 提交表单 */
function submitForm() {
  proxy.$refs['formRef'].validate(valid => {
    if (valid) {
      loading.value = true
      if (form.value.id) {
        // 更新设备信息
        updateEquipment(form.value)
          .then(response => {
            proxy.$modal.msgSuccess('修改成功')
            open.value = false
            emit('success')
          })
          .finally(() => {
            loading.value = false
          })
      } else {
        // 新增设备信息
        addEquipment(form.value)
          .then(response => {
            proxy.$modal.msgSuccess('新增成功')
            open.value = false
            emit('success')
          })
          .finally(() => {
            loading.value = false
          })
      }
    }
  })
}

// 暴露组件方法
defineExpose({
  handleOpen,
})
</script>

<style scoped>
.dialog-footer {
  padding: 20px;
  text-align: right;
}
</style>
