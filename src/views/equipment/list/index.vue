<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <!-- 搜索条件表单项 -->
      <el-form-item label="所属机构" prop="orgId" v-if="!userStore.orgId">
        <el-select v-model="queryParams.orgId" placeholder="全部" clearable style="width: 200px">
          <el-option v-for="item in orgList" :key="item?.id" :label="item?.orgName" :value="item?.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备型号" prop="deviceModel">
        <el-input v-model="queryParams.deviceModel" placeholder="请输入设备型号" clearable style="width: 200px" />
       
      </el-form-item>
      <el-form-item label="设备序列号" prop="deviceSn">
        <el-select v-model="queryParams.deviceSn" placeholder="全部" clearable style="width: 200px">
          <el-option v-for="item in equipmentModelList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" prop="deviceStatus">
        <el-select v-model="queryParams.deviceStatus" placeholder="全部" clearable style="width: 200px">
          <el-option v-for="dict in device_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="编辑时间" style="width: 308px">
        <el-date-picker
          v-model="editDate"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="createDate"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['device:add']">新增设备</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" :search="false" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="equipmentList" row-key="id">
      <el-table-column prop="id" label="序号" width="80" :fixed="true"></el-table-column>
      <el-table-column prop="deviceName" label="设备名称" width="120"></el-table-column>
      <el-table-column prop="deviceModel" label="设备型号" width="180"> </el-table-column>
      <el-table-column prop="deviceSn" label="序列号" width="180"></el-table-column>
      <el-table-column prop="deviceBrand" label="品牌" width="80"></el-table-column>
      <el-table-column prop="deviceStatus" label="设备状态" width="100">
        <template #default="scope">
          <dict-tag :options="device_status" :value="scope.row.deviceStatus" />
        </template>
      </el-table-column>
      <el-table-column prop="dispatchStatus" label="分配状态" width="100" v-if="!userStore.orgId">
        <template #default="scope">
          <dict-tag :options="allocate_status" :value="scope.row.dispatchStatus" />
        </template>
      </el-table-column>
      <el-table-column prop="orgName" label="分配机构" :show-overflow-tooltip="true" width="100" v-if="!userStore.orgId"></el-table-column>
      <el-table-column label="编辑时间" align="center" prop="mtime" min-width="160">
        <template #default="scope">
          <span>{{ formatDate(scope.row.mtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="ctime" min-width="160">
        <template #default="scope">
          <span>{{ formatDate(scope.row.ctime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['device:update']">编辑</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['device:delete']">删除</el-button>
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['device:detail']">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    <equipment-dialog ref="equipmentDialog" @success="success" />
  </div>
</template>

<script setup name="equipment-index">
// 导入所需的工具函数和组件
import { useRouter } from 'vue-router'
import { formatDate } from '@/utils/index'
import { date2timeStamp } from '@/utils'
import { getEquipmentList, deleteEquipment, getEquipmentModelList, getOrgList, updateEquipment } from '@/api/equipment/index'
import EquipmentDialog from './components/EquipmentDialog.vue'
import useUserStore from '@/store/modules/user'
// 获取组件实例和路由
const { proxy } = getCurrentInstance()
const router = useRouter()

// 组件状态变量
const equipmentList = ref([]) // 设备列表数据
const userStore = useUserStore()
const loading = ref(true) // 加载状态
const showSearch = ref(true) // 是否显示搜索区域
const equipmentDialog = ref(null) // 设备对话框组件引用
const refreshTable = ref(true) // 表格刷新标志
const createDate = ref([]) // 创建时间范围
const editDate = ref([]) // 编辑时间范围
const total = ref(0) // 总记录数
const equipmentModelList = ref([]) // 设备型号列表
const orgList = ref([]) // 机构列表
// 查询参数对象
const data = reactive({
  queryParams: {
    createEndTime: 0,
    createStartTime: 0,
    deviceModel: '',
    deviceSn: '',
    deviceStatus: undefined,
    modifyEndTime: 0,
    modifyStartTime: 0,
    orgId: undefined,
    pageNum: 1,
    pageSize: 10,
  },
})

// 获取字典数据
const { device_status, allocate_status, supplier_list } = proxy.useDict('device_status', 'allocate_status', 'supplier_list')

const { queryParams } = toRefs(data)

/** 查询设备列表数据 */
function getList() {
  loading.value = true
  getEquipmentList({
    ...queryParams.value,
    createStartTime: date2timeStamp(createDate?.value[0]),
    createEndTime: date2timeStamp(createDate?.value[1]),
    modifyStartTime: date2timeStamp(editDate?.value[0]),
    modifyEndTime: date2timeStamp(editDate?.value[1]),
  }).then(response => {
    console.log('🚀 ~ getList ~ response:', response)
    equipmentList.value = response?.data?.records
    loading.value = false
    total.value = response?.data?.total
  })
}

/** 搜索按钮点击事件处理 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置搜索条件 */
function resetQuery() {
  createDate.value = []
  editDate.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 新增设备操作 */
function handleAdd() {
  if (equipmentDialog.value) {
    equipmentDialog.value.handleOpen()
  }
}

/** 修改设备信息 */
function handleUpdate(row) {
  if (equipmentDialog.value) {
    equipmentDialog.value.handleOpen(row.id, 'edit')
  }
}

/** 查看设备详情 */
function handleDetail(row) {
  // router.push('/equipment/detail/' + row.id)
  equipmentDialog.value.handleOpen(row.id, 'detail')
}

/** 删除设备操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(h('div', null, [h('p', null, `是否确认删除"${row.deviceName}"的数据项?`), h('p', { style: 'color: #f56c6c; margin-top: 10px' }, '删除后设备将无法恢复，请谨慎处理。')]))
    .then(function () {
      return deleteEquipment(row.id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}
function queryEquipmentModelList() {
  getEquipmentModelList().then(response => {
    console.log('🚀 ~ getEquipmentModelList ~ response:', response)
    equipmentModelList.value = response?.data
  })
}
function queryOrgList() {
  getOrgList().then(response => {
    console.log('🚀 ~ getOrgList ~ response:', response)
    orgList.value = response?.data
  })
}
/** 操作成功后的回调函数 */
function success() {
  getList()
}

// 初始化加载设备列表
getList()
// 初始化加载设备型号列表
queryEquipmentModelList()
queryOrgList()
</script>
