<template>
  <!-- 排课中心容器 -->
  <div class="scheduling-center-container">
    <!-- 顶部导航和筛选区域 -->
    <div class="filter-container">
      <!-- 导航标签栏 -->
      <div class="nav-tabs">
        <!-- 视图切换按钮：课目、按周、按月 -->
        <div
          class="nav-item"
          :class="{ active: viewType === 'day' }"
          @click="switchView('day')"
        >
          课目
        </div>
        <div
          class="nav-item"
          :class="{ active: viewType === 'week' }"
          @click="switchView('week')"
        >
          按周
        </div>
        <div
          class="nav-item"
          :class="{ active: viewType === 'month' }"
          @click="switchView('month')"
        >
          按月
        </div>
        <div class="nav-item" @click="handleNavDate('prev')">
          {{ prevText }}
        </div>
        <div
          class="nav-item current-nav"
          :class="{
            active:
              (viewType === 'day' && isCurrentDay) ||
              (viewType === 'week' && isCurrentWeek) ||
              (viewType === 'month' && isCurrentMonth),
          }"
          @click="goToToday()"
        >
          {{ currentText }}
        </div>
        <div class="nav-item" @click="handleNavDate('next')">
          {{ nextText }}
        </div>
        <div class="date-display" v-if="viewType === 'week'">
          {{ formatDateRange(currentStartDate, currentEndDate) }}
        </div>
        <div class="date-display" v-else-if="viewType === 'month'">
          {{ formatYearMonth(currentDate) }}
        </div>
        <div class="date-display" v-else>
          {{ formatFullDate(currentDate) }}
        </div>
      </div>

      <!-- 筛选表单 -->
      <el-form
        :model="filters"
        ref="filterRef"
        :inline="true"
        class="filter-options"
        v-show="true"
      >
        <!-- 课程名称筛选 -->
        <el-form-item label="课程名称" prop="courseName">
          <el-select
            v-model="filters.courseName"
            placeholder="全部课程"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="AIGC课程" value="AIGC课程"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上课老师" prop="teacher">
          <el-select
            v-model="filters.teacher"
            placeholder="全部老师"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="蒋陈子老师" value="蒋陈子老师"></el-option>
            <el-option label="王菲老师" value="王菲老师"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上课教室" prop="classroom">
          <el-select
            v-model="filters.classroom"
            placeholder="全部教室"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="203课室" value="203课室"></el-option>
            <el-option label="206教室" value="206教室"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上课班级" prop="class">
          <el-select
            v-model="filters.class"
            placeholder="全部班级"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="2024年1期4班" value="2024年1期4班"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 功能按钮区域 -->
    <div class="action-buttons">
      <el-button
        type="primary"
        class="timeline-btn"
        plain
        @click="showTimeline"
      >
        时间线表
      </el-button>
      <div class="spacer"></div>
      <el-button type="primary" class="create-btn" @click="createSchedule">
        <el-icon><Plus /></el-icon>新建排课
      </el-button>
    </div>

    <!-- 日历视图容器 -->
    <div class="calendar-container">
      <!-- 周视图 -->
      <div v-if="viewType === 'week'" class="week-view">
        <!-- 周视图头部 -->
        <div class="week-header">
          <!-- 时间列和日期列头部 -->
          <div class="time-column"></div>
          <div
            v-for="(day, index) in weekDays"
            :key="index"
            class="day-column-header"
            :class="{ today: isSameDay(day.date, new Date()) }"
          >
            <div class="weekday">{{ day.weekday }}</div>
            <div class="date">{{ day.date }}</div>
            <div class="lunar-date">{{ day.lunarDate }}</div>
          </div>
        </div>

        <!-- 周视图主体 -->
        <div class="week-body">
          <!-- 时间列 -->
          <div class="time-column">
            <div v-for="hour in timeSlots" :key="hour" class="time-slot">
              {{ hour }}:00
            </div>
          </div>

          <!-- 日期列容器 -->
          <div class="day-columns-container">
            <!-- 日期列 -->
            <div
              v-for="(day, dayIndex) in weekDays"
              :key="dayIndex"
              class="day-column"
            >
              <div
                v-for="(hour, hourIndex) in timeSlots"
                :key="`${dayIndex}-${hour}`"
                class="hour-cell"
              >
                <!-- 显示当前时段的课程 -->
                <template
                  v-if="getScheduleByDayAndHour(day.date, hour).length > 0"
                >
                  <div
                    v-for="(schedule, scheduleIndex) in showLimitedSchedules(
                      getScheduleByDayAndHour(day.date, hour),
                      2
                    )"
                    :key="scheduleIndex"
                    class="schedule-item"
                    @click.stop="showScheduleDetail(schedule)"
                  >
                    {{ schedule.name }} {{ schedule.teacher }}
                    {{ schedule.classroom }}
                  </div>
                  <!-- 显示更多课程的指示器 -->
                  <div
                    v-if="getScheduleByDayAndHour(day.date, hour).length > 2"
                    class="more-indicator"
                    @click.stop="
                      showMoreSchedules(getScheduleByDayAndHour(day.date, hour))
                    "
                  >
                    还有{{
                      getScheduleByDayAndHour(day.date, hour).length - 2
                    }}个课程...
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 月视图 -->
      <div v-if="viewType === 'month'" class="month-view">
        <div class="month-header">
          <div
            v-for="(weekday, index) in [
              '周一',
              '周二',
              '周三',
              '周四',
              '周五',
              '周六',
              '周日',
            ]"
            :key="index"
            class="weekday-header"
          >
            {{ weekday }}
          </div>
        </div>
        <div class="month-body">
          <div
            v-for="(week, weekIndex) in monthDays"
            :key="weekIndex"
            class="week-row"
          >
            <div
              v-for="(day, dayIndex) in week"
              :key="`${weekIndex}-${dayIndex}`"
              class="day-cell"
              :class="{
                'other-month': day.isOtherMonth,
                today: isSameDay(day.date, new Date()),
                selected: day.date === selectedDate,
              }"
              @click="handleMonthDayClick(day)"
            >
              <div class="day-number">
                {{ day.day }}
                <span class="lunar-date">{{ day.lunarDate }}</span>
              </div>
              <div class="day-content">
                <template v-if="day.schedules.length > 0">
                  <div
                    v-for="(schedule, scheduleIndex) in showLimitedSchedules(
                      day.schedules,
                      3
                    )"
                    :key="scheduleIndex"
                    class="month-schedule-item"
                    @click.stop="showScheduleDetail(schedule)"
                  >
                    {{ schedule.name }}
                  </div>
                  <div
                    v-if="day.schedules.length > 3"
                    class="more-indicator"
                    @click.stop="showMoreSchedules(day.schedules)"
                  >
                    还有{{ day.schedules.length - 3 }}个...
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日视图 -->
      <div v-if="viewType === 'day'" class="day-view">
        <div class="day-header">
          <div class="day-title">
            {{ formatDayHeader(currentDate) }}
          </div>
        </div>
        <div class="day-body">
          <div class="time-column">
            <div v-for="hour in timeSlots" :key="hour" class="time-slot">
              {{ hour }}:00
            </div>
          </div>
          <div class="day-content">
            <div
              v-for="hour in timeSlots"
              :key="`day-${hour}`"
              class="hour-cell"
            >
              <template
                v-if="
                  getScheduleByDayAndHour(formatDate(currentDate), hour)
                    .length > 0
                "
              >
                <div
                  v-for="(schedule, scheduleIndex) in showLimitedSchedules(
                    getScheduleByDayAndHour(formatDate(currentDate), hour),
                    2
                  )"
                  :key="scheduleIndex"
                  class="schedule-item-day"
                  @click.stop="showScheduleDetail(schedule)"
                >
                  {{ schedule.name }} {{ schedule.teacher }}
                  {{ schedule.classroom }}
                </div>
                <div
                  v-if="
                    getScheduleByDayAndHour(formatDate(currentDate), hour)
                      .length > 2
                  "
                  class="more-indicator"
                  @click.stop="
                    showMoreSchedules(
                      getScheduleByDayAndHour(formatDate(currentDate), hour)
                    )
                  "
                >
                  还有{{
                    getScheduleByDayAndHour(formatDate(currentDate), hour)
                      .length - 2
                  }}个课程...
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      title="课程详情"
      v-model="scheduleDetailVisible"
      width="400px"
      center
    >
      <div v-if="currentSchedule" class="schedule-detail">
        <!-- 课程详情各项信息 -->
        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.name }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.date }} {{ currentSchedule.weekday }}
            {{ currentSchedule.startTime }}-
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><School /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.classroom }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.teacher }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><OfficeBuilding /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.class }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><Notebook /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.content }}
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="detail-content">
            {{ currentSchedule.creator }} 创建 {{ currentSchedule.createTime }}
          </div>
        </div>
      </div>
      <!-- 弹窗底部按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scheduleDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="editSchedule">编辑</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更多课程弹窗 -->
    <el-dialog
      title="该时段所有课程"
      v-model="moreSchedulesVisible"
      width="500px"
      center
    >
      <!-- 课程列表 -->
      <div class="all-schedules-list">
        <div
          v-for="(schedule, index) in moreSchedulesList"
          :key="index"
          class="schedule-list-item"
          @click.stop="showScheduleDetail(schedule)"
        >
          <!-- 课程时间和基本信息 -->
          <div class="schedule-list-item-time">
            {{ schedule.startTime }} - {{ schedule.endTime }}
          </div>
          <div class="schedule-list-item-content">
            <div class="schedule-list-item-title">{{ schedule.name }}</div>
            <div class="schedule-list-item-info">
              {{ schedule.teacher }} | {{ schedule.classroom }} |
              {{ schedule.class }}
            </div>
          </div>
        </div>
      </div>
      <!-- 弹窗底部按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moreSchedulesVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// 引入必要的 Vue 组合式 API 和依赖
import { ref, reactive, computed, onMounted } from "vue";
import { Lunar } from "lunar-javascript";
import { ElMessage } from "element-plus";
// 导入Element Plus图标
import {
  Calendar,
  Timer,
  School,
  User,
  OfficeBuilding,
  Notebook,
  Edit,
  Search,
  Refresh,
  Plus,
} from "@element-plus/icons-vue";

// 响应式状态定义
// 视图类型：周/月/日
const viewType = ref("week");
// 当前选中的日期
const currentDate = ref(new Date());
// 当前周的开始和结束日期
const currentStartDate = ref(null);
const currentEndDate = ref(null);
// 选中的具体日期
const selectedDate = ref(null);

// 筛选条件响应式对象
const filters = reactive({
  courseName: "",
  teacher: "",
  classroom: "",
  class: "",
});

// 时间槽（小时）
const timeSlots = ref([
  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
  22, 23,
]);

// 周视图和月视图的数据
const weekDays = ref([]);
const monthDays = ref([]);

// 课程数据
const schedules = ref([
  {
    id: 1,
    name: "Java基础",
    teacher: "张老师",
    classroom: "教室A101",
    class: "2025年1期4班",
    date: "2025-03-15",
    startTime: "09:00",
    endTime: "11:00",
  },
  {
    id: 2,
    name: "Python编程",
    teacher: "李老师",
    classroom: "教室B203",
    class: "2025年1期4班",
    date: "2025-03-07",
    startTime: "14:00",
    endTime: "16:00",
  },
  {
    id: 3,
    name: "Web前端",
    teacher: "王老师",
    classroom: "教室C305",
    class: "2025年1期4班",
    date: "2025-03-07",
    startTime: "09:00",
    endTime: "11:00",
  },
  {
    id: 4,
    name: "数据库",
    teacher: "赵老师",
    classroom: "教室A102",
    class: "2025年1期4班",
    date: "2025-03-16",
    startTime: "14:00",
    endTime: "16:00",
  },
  {
    id: 5,
    name: "算法基础",
    teacher: "孙老师",
    classroom: "教室B201",
    class: "2025年1期4班",
    date: "2024-03-17",
    startTime: "09:00",
    endTime: "11:00",
  },
]);

// 弹窗和详情相关的响应式状态
const scheduleDetailVisible = ref(false);
const currentSchedule = ref(null);
const moreSchedulesVisible = ref(false);
const moreSchedulesList = ref([]);

// 计算属性：导航文案
const prevText = computed(() => {
  return viewType.value === "day"
    ? "上一天"
    : viewType.value === "week"
    ? "上一周"
    : "上一月";
});

const currentText = computed(() => {
  return viewType.value === "day"
    ? "今天"
    : viewType.value === "week"
    ? "本周"
    : "当月";
});

const nextText = computed(() => {
  return viewType.value === "day"
    ? "下一天"
    : viewType.value === "week"
    ? "下一周"
    : "下一月";
});

const isCurrentDay = computed(() => {
  const today = new Date();
  return isSameDay(currentDate.value, today);
});

const isCurrentWeek = computed(() => {
  const today = new Date();
  if (currentStartDate.value && currentEndDate.value) {
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const weekStart = new Date(
      currentStartDate.value.getFullYear(),
      currentStartDate.value.getMonth(),
      currentStartDate.value.getDate()
    );
    const weekEnd = new Date(
      currentEndDate.value.getFullYear(),
      currentEndDate.value.getMonth(),
      currentEndDate.value.getDate(),
      23,
      59,
      59
    );

    return todayStart >= weekStart && todayStart <= weekEnd;
  }
  return false;
});

const isCurrentMonth = computed(() => {
  const today = new Date();
  return (
    today.getFullYear() === currentDate.value.getFullYear() &&
    today.getMonth() === currentDate.value.getMonth()
  );
});

// 方法：初始化日期
function initDates() {
  // 根据当前视图类型生成对应的日期数据
  if (viewType.value === "week") {
    generateWeekDays();
  } else if (viewType.value === "month") {
    generateMonthDays();
  }
}

// 方法：切换视图
function switchView(type) {
  // 重置日期和选中状态
  viewType.value = type;
  currentDate.value = new Date();
  selectedDate.value = null;
  initDates();
}

// 方法：处理日期导航（上一个/下一个）
function handleNavDate(direction) {
  // 根据不同视图类型调整日期
  if (viewType.value === "day") {
    const newDate = new Date(currentDate.value);
    newDate.setDate(newDate.getDate() + (direction === "prev" ? -1 : 1));
    currentDate.value = newDate;
  } else if (viewType.value === "week") {
    const newDate = new Date(currentDate.value);
    newDate.setDate(newDate.getDate() + (direction === "prev" ? -7 : 7));
    currentDate.value = newDate;
    generateWeekDays();
  } else if (viewType.value === "month") {
    const newDate = new Date(currentDate.value);
    newDate.setMonth(newDate.getMonth() + (direction === "prev" ? -1 : 1));
    currentDate.value = newDate;
    generateMonthDays();
  }
}

// 方法：返回今天
function goToToday() {
  currentDate.value = new Date();
  initDates();
}

// 方法：判断是否是同一天
function isSameDay(date1, date2) {
  // 如果是字符串格式，先转为Date对象
  if (typeof date1 === "string") {
    const parts = date1.split("-");
    date1 = new Date(
      parseInt(parts[0]),
      parseInt(parts[1]) - 1,
      parseInt(parts[2])
    );
  }
  if (typeof date2 === "string") {
    const parts = date2.split("-");
    date2 = new Date(
      parseInt(parts[0]),
      parseInt(parts[1]) - 1,
      parseInt(parts[2])
    );
  }

  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

// 方法：生成周视图数据
function generateWeekDays() {
  const date = new Date(currentDate.value);
  // 设置为本周的周一
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1);
  const monday = new Date(date.setDate(diff));

  weekDays.value = [];
  currentStartDate.value = new Date(monday);

  // 生成周一到周日
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(monday);
    currentDate.setDate(monday.getDate() + i);

    weekDays.value.push({
      date: formatDate(currentDate),
      weekday: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][i],
      lunarDate: getLunarDate(currentDate),
      schedules: getSchedulesByDate(formatDate(currentDate)),
    });
  }

  currentEndDate.value = new Date(monday);
  currentEndDate.value.setDate(monday.getDate() + 6);
}

// 方法：生成月视图数据
function generateMonthDays() {
  const date = new Date(currentDate.value);
  const year = date.getFullYear();
  const month = date.getMonth();

  // 获取当月第一天
  const firstDay = new Date(year, month, 1);
  // 获取当月最后一天
  const lastDay = new Date(year, month + 1, 0);

  // 获取第一天是周几 (0是周日，1是周一...)
  const firstDayOfWeek = firstDay.getDay() || 7;

  monthDays.value = [];
  let currentWeek = [];

  // 添加上个月的日期
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  for (let i = 1; i < firstDayOfWeek; i++) {
    const prevMonthDay = prevMonthLastDay - firstDayOfWeek + i + 1;
    currentWeek.push({
      day: prevMonthDay,
      date: formatDate(new Date(year, month - 1, prevMonthDay)),
      lunarDate: getLunarDate(new Date(year, month - 1, prevMonthDay)),
      isOtherMonth: true,
      schedules: [],
    });
  }

  // 添加当月日期
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const currentDate = new Date(year, month, i);
    currentWeek.push({
      day: i,
      date: formatDate(currentDate),
      lunarDate: getLunarDate(currentDate),
      isOtherMonth: false,
      schedules: getSchedulesByDate(formatDate(currentDate)),
    });

    // 如果是周日或者最后一天，开始新的一周
    if (currentWeek.length === 7) {
      monthDays.value.push(currentWeek);
      currentWeek = [];
    }
  }

  // 添加下个月的日期
  if (currentWeek.length > 0) {
    const nextMonthDay = 1;
    while (currentWeek.length < 7) {
      const currentNextMonthDay =
        nextMonthDay +
        currentWeek.length -
        currentWeek.filter((d) => !d.isOtherMonth).length;
      currentWeek.push({
        day: currentNextMonthDay,
        date: formatDate(new Date(year, month + 1, currentNextMonthDay)),
        lunarDate: getLunarDate(new Date(year, month + 1, currentNextMonthDay)),
        isOtherMonth: true,
        schedules: [],
      });
    }
    monthDays.value.push(currentWeek);
  }
}

// 日期格式化相关方法
function formatDate(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
}

function formatYearMonth(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  return `${year}-${month}`;
}

function formatFullDate(date) {
  const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const weekday = weekdays[date.getDay()];
  return `${year}-${month}-${day} ${weekday}`;
}

function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return "";
  const startYear = startDate.getFullYear();
  const startMonth = (startDate.getMonth() + 1).toString().padStart(2, "0");
  const startDay = startDate.getDate().toString().padStart(2, "0");

  const endYear = endDate.getFullYear();
  const endMonth = (endDate.getMonth() + 1).toString().padStart(2, "0");
  const endDay = endDate.getDate().toString().padStart(2, "0");

  return `${startYear}-${startMonth}-${startDay}到${endYear}-${endMonth}-${endDay}`;
}

function formatDayHeader(date) {
  const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const weekday = weekdays[date.getDay()];

  return `${weekday} ${year}年${month}月${day}日 ${getLunarDate(date)}`;
}

// 农历相关方法
function getLunarDate(date) {
  try {
    // 使用lunar-javascript转换日期
    const lunar = Lunar.fromDate(date);

    // 获取节日信息
    const festivals = getFestivals(date, lunar);
    if (festivals.length > 0) {
      return festivals[0];
    }

    // 返回农历日期，格式：初一、初二...廿九、三十
    return formatLunarDay(lunar.getDay());
  } catch (error) {
    console.error("获取农历日期出错:", error);
    // 出错时返回默认值
    return "";
  }
}

function formatLunarDay(day) {
  const lunarDays = [
    "",
    "初一",
    "初二",
    "初三",
    "初四",
    "初五",
    "初六",
    "初七",
    "初八",
    "初九",
    "初十",
    "十一",
    "十二",
    "十三",
    "十四",
    "十五",
    "十六",
    "十七",
    "十八",
    "十九",
    "二十",
    "廿一",
    "廿二",
    "廿三",
    "廿四",
    "廿五",
    "廿六",
    "廿七",
    "廿八",
    "廿九",
    "三十",
  ];
  return lunarDays[day];
}

function getFestivals(date, lunar) {
  return [];
}

function getSchedulesByDate(dateStr) {
  return schedules.value.filter((schedule) => schedule.date === dateStr);
}

function getScheduleByDayAndHour(dateStr, hour) {
  const hourStr = hour.toString().padStart(2, "0");
  return schedules.value.filter(
    (schedule) =>
      schedule.date === dateStr && schedule.startTime.split(":")[0] === hourStr
  );
}

function showScheduleDetail(schedule) {
  currentSchedule.value = schedule;
  scheduleDetailVisible.value = true;
}

function createSchedule() {
  ElMessage.info("新建排课功能开发中...");
}

function editSchedule() {
  ElMessage.info("编辑排课功能开发中...");
}

function showTimeline() {
  ElMessage.info("时间线表功能开发中...");
}

function showLimitedSchedules(schedules, limit) {
  return schedules.slice(0, limit);
}

function showMoreSchedules(schedules) {
  moreSchedulesList.value = schedules;
  moreSchedulesVisible.value = true;
}

function handleMonthDayClick(day) {
  // 如果点击的是非当前月的日期，切换到对应月份
  if (day.isOtherMonth) {
    const clickedDate = new Date(day.date);
    currentDate.value = clickedDate;
    generateMonthDays();
  }

  // 设置选中的日期
  selectedDate.value = day.date;
}

function handleQuery() {
  // 根据筛选条件过滤日程
  const filteredSchedules = schedules.value.filter((schedule) => {
    return (
      (!filters.courseName || schedule.name === filters.courseName) &&
      (!filters.teacher || schedule.teacher === filters.teacher) &&
      (!filters.classroom || schedule.classroom === filters.classroom) &&
      (!filters.class || schedule.class === filters.class)
    );
  });

  // 更新视图中的日程
  if (viewType.value === "week") {
    generateWeekDays();
  } else if (viewType.value === "month") {
    generateMonthDays();
  }
}

function resetQuery() {
  // 重置筛选条件
  filters.courseName = "";
  filters.teacher = "";
  filters.classroom = "";
  filters.class = "";

  // 重新加载所有日程
  if (viewType.value === "week") {
    generateWeekDays();
  } else if (viewType.value === "month") {
    generateMonthDays();
  }
}

onMounted(() => {
  initDates();
});
</script>

<style lang="scss" scoped>
.scheduling-center-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background-color: #f5f7fa;
}

.filter-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.nav-tabs {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  padding-bottom: 16px;
  align-items: center;

  .nav-item {
    padding: 8px 16px;
    margin-right: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #6366f1;
      color: white;
    }

    &.current-nav {
      background-color: #6366f1;
      color: white;
    }
  }

  .date-display {
    margin-left: auto;
    font-size: 14px;
    color: #606266;
  }
}

.filter-options {
  display: flex;
  margin-top: 16px;
  flex-wrap: wrap;

  .filter-item {
    margin-right: 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    .label {
      margin-right: 8px;
      color: #606266;
    }
  }
}

.action-buttons {
  display: flex;
  margin-bottom: 20px;

  .timeline-btn {
    background-color: #6366f1;
    border-color: #6366f1;
    color: white;
  }

  .spacer {
    flex-grow: 1;
  }

  .create-btn {
    background-color: #6366f1;
    border-color: #6366f1;
  }
}

.calendar-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 周视图样式 */
.week-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden; /* 防止整体出现滚动条 */

  .week-header {
    display: flex;
    box-sizing: border-box;
    border-bottom: 1px solid #e6e6e6;
    flex-shrink: 0; /* 防止头部被压缩 */

    .time-column {
      width: 60px;
      min-width: 60px;
      flex-shrink: 0;
      box-sizing: border-box;
      border-right: 1px solid #e6e6e6;
    }

    .day-column-header {
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
      padding: 8px;
      text-align: center;
      border-right: 1px solid #e6e6e6;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        border-right: none;
      }

      .weekday {
        font-weight: bold;
      }

      .date {
        font-size: 12px;
        color: #606266;
      }

      .lunar-date {
        font-size: 12px;
        color: #909399;
      }

      &.today {
        background-color: rgba(99, 102, 241, 0.1);
      }
    }
  }

  .week-body {
    display: flex;
    flex: 1;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动 */

    .time-column {
      width: 60px;
      min-width: 60px;
      flex-shrink: 0;
      box-sizing: border-box;
      border-right: 1px solid #e6e6e6;

      .time-slot {
        height: 60px;
        padding: 4px;
        border-bottom: 1px solid #e6e6e6;
        font-size: 12px;
        color: #606266;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .day-columns-container {
      display: flex;
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
    }

    .day-column {
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
      box-sizing: border-box;
      border-right: 1px solid #e6e6e6;

      &:last-child {
        border-right: none;
      }

      .hour-cell {
        height: 60px;
        padding: 4px;
        border-bottom: 1px solid #e6e6e6;
        box-sizing: border-box;
        overflow-y: auto;
        position: relative;
        &:last-child {
          border-bottom: none;
        }

        .schedule-item {
          background-color: #6366f1;
          color: white;
          border-radius: 4px;
          padding: 3px 6px;
          font-size: 12px;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 2px;
          box-sizing: border-box;
          max-height: 24px;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
        }
      }
    }
  }
}

/* 月视图样式 */
.month-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden; /* 防止整体出现滚动条 */

  .month-header {
    display: flex;
    border-bottom: 1px solid #e6e6e6;
    box-sizing: border-box;
    flex-shrink: 0; /* 防止头部被压缩 */

    .weekday-header {
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
      padding: 8px;
      text-align: center;
      font-weight: bold;
      box-sizing: border-box;
      border-right: 1px solid #e6e6e6;

      &:last-child {
        border-right: none;
      }
    }
  }

  .month-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动 */

    .week-row {
      display: flex;
      flex: 1;
      min-height: 100px; /* 确保每周有最小高度 */
      border-bottom: 1px solid #e6e6e6;
      box-sizing: border-box;

      &:last-child {
        border-bottom: none;
      }

      .day-cell {
        flex: 1;
        min-width: 0; /* 防止内容溢出 */
        padding: 8px;
        border-right: 1px solid #e6e6e6;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        cursor: pointer;
        height: 100px;
        overflow-y: auto;
        &:last-child {
          border-right: none;
        }

        &.other-month {
          background-color: #f5f7fa;
          color: #909399;
        }

        &.today {
          background-color: rgba(99, 102, 241, 0.1);
        }

        &.selected {
          background-color: rgba(99, 102, 241, 0.2);
          font-weight: bold;
        }

        .day-number {
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          box-sizing: border-box;

          .lunar-date {
            font-size: 12px;
            color: #909399;
            font-weight: normal;
          }
        }

        .day-content {
          flex: 1;
          overflow-y: auto;
          box-sizing: border-box;

          .month-schedule-item {
            background-color: #6366f1;
            color: white;
            border-radius: 4px;
            padding: 2px 4px;
            font-size: 12px;
            margin-bottom: 2px;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
            max-height: 20px;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
          }
        }
      }
    }
  }
}

/* 日视图样式 */
.day-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden; /* 防止整体出现滚动条 */

  .day-header {
    border-bottom: 1px solid #e6e6e6;
    padding: 16px;
    text-align: center;
    box-sizing: border-box;
    flex-shrink: 0; /* 防止头部被压缩 */

    .day-title {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .day-body {
    display: flex;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动 */
    box-sizing: border-box;

    .time-column {
      width: 60px;
      min-width: 60px;
      flex-shrink: 0;
      box-sizing: border-box;
      border-right: 1px solid #e6e6e6;

      .time-slot {
        height: 60px;
        padding: 4px;
        border-bottom: 1px solid #e6e6e6;
        font-size: 12px;
        color: #606266;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .day-content {
      flex: 1;
      min-width: 0; /* 防止内容溢出 */
      box-sizing: border-box;

      .hour-cell {
        height: 60px;
        padding: 4px;
        border-bottom: 1px solid #e6e6e6;
        box-sizing: border-box;
        overflow-y: auto;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        .schedule-item-day {
          background-color: #6366f1;
          color: white;
          border-radius: 4px;
          padding: 6px 8px;
          font-size: 14px;
          cursor: pointer;
          margin-bottom: 2px;
          box-sizing: border-box;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-height: 54px;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
        }
      }
    }
  }
}

/* 课程详情样式 */
.schedule-detail {
  .detail-item {
    display: flex;
    margin-bottom: 16px;

    .detail-icon {
      width: 24px;
      margin-right: 8px;
      color: #409eff;
    }

    .detail-content {
      flex: 1;
    }
  }
}

.more-indicator {
  font-size: 12px;
  color: #6366f1;
  text-align: center;
  cursor: pointer;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 4px;
  padding: 2px 4px;
  margin-top: 2px;

  &:hover {
    background-color: rgba(99, 102, 241, 0.2);
  }
}

.all-schedules-list {
  max-height: 400px;
  overflow-y: auto;

  .schedule-list-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;

    &:hover {
      background-color: #f8f8f8;
    }

    &:last-child {
      border-bottom: none;
    }

    .schedule-list-item-time {
      width: 100px;
      color: #606266;
      font-weight: bold;
    }

    .schedule-list-item-content {
      flex: 1;

      .schedule-list-item-title {
        font-weight: bold;
        margin-bottom: 4px;
      }

      .schedule-list-item-info {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}
</style>
