<!--
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-03-04 11:47:34
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-05 15:04:31
 * @FilePath: /miaobi-admin-magic-touch/src/views/welcome/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="welcome" :style="{ height: welcomeHeight }">
    <div class="animated-background">
      <div class="wave"></div>
      <div class="wave"></div>
      <div class="wave"></div>
    </div>
    <div class="welcome-container">
      <div class="logo-section">
        <div class="logo">
          <img src="/src/assets/logo/logoShrink.png" alt="AI Logo" />
        </div>
        <div class="version">V1.0</div>
      </div>
      <h1 class="title">妙笔AI管理平台</h1>
      <div class="subtitle">让创作更简单</div>
      <div class="features">
        <div class="feature-item">
          <div class="feature-icon">🎨</div>
          <div class="feature-text">智能创作</div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">⚡️</div>
          <div class="feature-text">高效管理</div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🔮</div>
          <div class="feature-text">智能分析</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";

const welcomeTop = ref(0);
const welcomeHeight = computed(() => `calc(100vh - ${welcomeTop.value}px)`);

const updateWelcomeTop = () => {
  const welcomeEl = document.querySelector(".welcome");
  if (welcomeEl) {
    welcomeTop.value = welcomeEl.getBoundingClientRect().top;
  }
};

onMounted(() => {
  updateWelcomeTop();
  window.addEventListener("resize", updateWelcomeTop);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWelcomeTop);
});
</script>

<style lang="scss" scoped>
.welcome {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
  overflow: hidden;

  .animated-background {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: -1;
  }

  .wave {
    position: absolute;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      rgba(107, 141, 255, 0.03),
      rgba(67, 24, 255, 0.05)
    );
    border-radius: 38% 42%;

    &:nth-child(1) {
      animation: wave 20s infinite linear;
    }

    &:nth-child(2) {
      animation: wave 15s infinite linear;
      opacity: 0.5;
      animation-delay: -5s;
    }

    &:nth-child(3) {
      animation: wave 10s infinite linear;
      opacity: 0.2;
      animation-delay: -2s;
    }
  }

  .welcome-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    backdrop-filter: blur(12px);
    padding: 50px;
    border-radius: 30px;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9),
      rgba(107, 141, 255, 0.15)
    );
    box-shadow: 0 8px 32px rgba(107, 141, 255, 0.15),
      0 2px 4px rgba(0, 0, 0, 0.05), inset 0 0 0 1px rgba(255, 255, 255, 0.4);
    min-width: 380px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 12px 40px rgba(107, 141, 255, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
  }

  .logo-section {
    position: relative;
    display: inline-block;
    margin-bottom: 24px;
  }

  .logo {
    width: 90px;
    height: 90px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #6b8dff, #4318ff);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(67, 24, 255, 0.15);
    transition: all 0.3s ease;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(67, 24, 255, 0.2);
    }
  }

  .version {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #4318ff;
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 10px;
    font-weight: 500;
  }

  .title {
    font-size: 36px;
    font-weight: 700;
    background: linear-gradient(120deg, #1a1f36, #4318ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 16px;
    letter-spacing: 1px;
  }

  .subtitle {
    font-size: 18px;
    color: #697386;
    letter-spacing: 0.5px;
    margin-bottom: 40px;
  }

  .features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
  }

  .feature-item {
    text-align: center;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-3px);
    }
  }

  .feature-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .feature-text {
    font-size: 14px;
    color: #4318ff;
    font-weight: 500;
  }
}

@keyframes wave {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
