<template>
  <div class="video-bottom">
    <!-- 功能按钮区域 -->
    <div class="video-bottom-btn" v-if="show && !listShow && message && !dubbingMode">
      <div class="btn-box" @click="startDubbingMode">
        <img :src="dubbingImg" alt="" />
        <span>AI配音</span>
      </div>
      <div class="btn-box" @click="handleCutVideo">
        <img :src="cutImg" alt="" />
        <span>截取</span>
      </div>
      <div class="btn-box" @click="listShow = true">
        <img :src="music2Img" alt="" />
        <span>AI音效</span>
      </div>
      <div class="btn-box" @click="quoteVideo">
        <img :src="quote" alt="" />
        <span>引用</span>
      </div>
    </div>

    <!-- 音乐列表 -->
    <div v-if="show && listShow" class="music-list">
      <div class="title">
        <div class="left" @click="listShow = false">
          <el-icon>
            <CircleCloseFilled />
          </el-icon>
        </div>
        <div class="center">AI音效</div>
        <div class="right">
          <div :class="['bottom', { ash: mergeLoading }]" @click="mergeLoading ? '' : merge()">
            {{ mergeLoading ? '提交中...' : '合成' }}
          </div>
        </div>
      </div>
      <div class="menu">
        <div class="level1">
          <div @click="() => changeMusicCategoryCheck(item)" :class="{ item: true, active: musicCategoryCheck === item }" v-for="item in musicCategory" :key="item.title">
            {{ item.title }}
          </div>
        </div>
        <div class="level2">
          <div @click="() => changeMusicCategoryChildCheck(item)" :class="{ item: true, active: musicCategoryChildCheck?.id === item.id }" v-for="item in musicCategoryCheck?.list" :key="item.title">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div v-if="!musicList.length" class="no_music">暂无音乐!</div>
      <div v-else class="list">
        <div :class="['item', { active: item.musicUrl === music }]" v-for="item in musicList" :key="item.musicName" @click="chooseMusic(item.musicUrl)">
          <div class="icon" @click="e => playAudio(e, item.musicUrl, item.durationSeconds)">
            <img v-if="item.musicUrl === playCurrent" :src="playingImg" alt="" />
            <img v-else :src="item.musicUrl === music ? paly2Img : palyImg" alt="" />
          </div>
          <div class="name">
            <div :class="['text', { scroll: item.musicName.length > 8 && item.musicUrl === playCurrent }]">
              {{ item.musicName }}
            </div>
          </div>
          <div class="second">{{ item.musicUrl === playCurrent && duration ? duration : item.durationSeconds }}s</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { ElIcon, ElMessage } from 'element-plus'
import { getMusicCategory, getMusicByCategory } from '@/api/aiChat'
import { addMessageToQuoteList } from '@/utils/messageUtils'
import { sendCustomMessage } from '@/utils/customIM'
import { useRoute } from 'vue-router'

// 图片导入
import music2Img from '@/assets/images/ChatAiTool/music2.png'
import paly2Img from '@/assets/images/ChatAiTool/paly2.png'
import palyImg from '@/assets/images/ChatAiTool/paly.png'
import playingImg from '@/assets/images/ChatAiTool/playing.gif'
import quote from '@/assets/images/ChatAiTool/quote.png'
import cutImg from '@/assets/images/ChatAiTool/cut.png'
import dubbingImg from '@/assets/images/ChatAiTool/dubbing.png'

// 接口定义
interface MusicCategoryChild {
  title: string
  id: number
}
interface MusicCategory {
  title: string
  list: MusicCategoryChild[]
}
interface Music {
  musicUrl: string
  musicName: string
  durationSeconds: number
  categoryId: number
  delFlag: string
  status: number
}

const props = defineProps({
  video: String,
  action: Function,
  poster: String,
  message: Object,
  show: {
    type: Boolean,
    default: true,
  },
  dubbingMode: Boolean,
})

const emit = defineEmits<{
  (e: 'exitFullscreen'): void
  (e: 'cutVideo'): void
}>()

// 响应式数据
const listShow = ref(false)
const musicCategory = ref<MusicCategory[]>([])
const musicCategoryCheck = ref<MusicCategory | null>(null)
const musicCategoryChildCheck = ref<MusicCategoryChild | null>(null)
const musicList = ref<Music[]>([])
const music = ref('')
const playCurrent = ref('')
const timer = ref(0)
const duration = ref('')
const audio = ref<HTMLAudioElement | null>(null)
const mergeLoading = ref(false)
const route = useRoute()

// 监听音乐列表显示状态
watch(
  () => listShow.value,
  newValue => {
    props.action?.('music', newValue)
  }
)

onMounted(() => {
  getMusicList()
})

// 音频播放控制
const playAudio = (e: { stopPropagation: () => void; preventDefault: () => void }, url: string, durationSeconds: number) => {
  e.stopPropagation()
  e.preventDefault()
  if (url === playCurrent.value) {
    audio.value?.pause()
    playCurrent.value = ''
    duration.value = ''
    timer.value && clearInterval(timer.value)
    return
  }
  if (audio.value && !audio.value.paused) {
    audio.value.pause()
  }
  audio.value = new Audio(url)
  audio.value.play()
  audio.value.onended = () => {
    playCurrent.value = ''
  }
  playCurrent.value = url

  timer.value && clearInterval(timer.value)
  timer.value = setInterval(() => {
    const currentTime = audio.value?.currentTime || 0
    duration.value = Math.max(0, durationSeconds - currentTime).toFixed(1)
  }, 100)
}

// 音乐选择
const chooseMusic = (url: string) => {
  music.value = url
}

// 获取音乐分类
const getMusicList = () => {
  getMusicCategory().then((res: { data: { [x: string]: { [key: string]: number }[] } }) => {
    const categories: MusicCategory[] = []
    Object.keys(res.data).forEach((key: string) => {
      const category: MusicCategory = {
        title: key,
        list: [],
      }
      res.data[key].forEach((item: { [key: string]: number }) => {
        const subCategory = Object.keys(item)[0]
        category.list.push({
          title: subCategory,
          id: item[subCategory],
        })
      })
      categories.push(category)
    })
    musicCategory.value = categories
    changeMusicCategoryCheck(musicCategory.value[0])
  })
}

// 获取子分类音乐
const getChildMusic = (categoryId: number) => {
  getMusicByCategory(categoryId).then((res: { data: Music[] }) => {
    musicList.value = res.data
  })
}

// 切换音乐分类
const changeMusicCategoryCheck = (item: MusicCategory) => {
  musicCategoryCheck.value = item
  changeMusicCategoryChildCheck(item.list[0])
}

const changeMusicCategoryChildCheck = (item: MusicCategoryChild) => {
  musicCategoryChildCheck.value = item
  getChildMusic(item.id)
  if (audio.value) {
    audio.value.pause()
    audio.value = null
  }
  timer.value && clearInterval(timer.value)
  playCurrent.value = ''
  duration.value = ''
}

// 合成音乐
const sleep = (time = 1000) => {
  return new Promise(resolve => {
    setTimeout(resolve, time)
  })
}

const merge = async () => {
  if (!music.value) {
    ElMessage({ message: '请选择配乐', appendTo: document.querySelector('#ai-music') as HTMLElement })
    return
  }
  mergeLoading.value = true
  await sleep(600)

  const payload = {
    data: {
      businessID: 'say_hidden_message',
      content: {
        name: 'cmd_msg',
        data: {
          text: '',
          referenceList: [
            {
              type: 'video',
              data: props.video,
            },
            {
              type: 'audio',
              data: music.value,
            },
          ],
          cmd: '/视频配音乐',
        },
      },
    },
  }
  sendCustomMessage(payload).finally(() => {
    props.action?.('merge')
    mergeLoading.value = false
  })
}

// 功能按钮事件
const handleCutVideo = () => {
  emit('cutVideo')
}

const startDubbingMode = () => {
  props.action?.('dubbing', true)
}

const quoteVideo = () => {
  addMessageToQuoteList(props.message, { video: props.video, poster: props.poster })
  emit('exitFullscreen')
}
</script>

<style lang="scss" scoped>
.video-bottom {
  width: 100%;

  .video-bottom-btn {
    height: 107px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding-left: 37px;

    .btn-box {
      display: flex;
      flex-direction: row;
      background: #2967f3;
      padding: 10px;
      border-radius: 5px;
      // min-width: 87px;
      margin-right: 20px;
      justify-content: space-between;

      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }

      span {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #ffffff;
        flex: 1;
        text-align: center;
      }
    }
  }
}

.music-list {
  background: #dee5f6;
  border-radius: 15px 15px 0px 0px;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;

  .title {
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .left {
      width: 40px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      i {
        font-size: 34px;
        color: #9ba0ac;
      }
    }

    .center {
      flex: 1;
      text-align: center;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 20px;
      color: #000000;
    }

    .right {
      width: 100px;

      .bottom {
        margin: 0 auto;
        text-align: center;
        width: 100px;
        height: 40px;
        background: #2266ff;
        border-radius: 5px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        cursor: pointer;
      }

      .bottom.ash {
        background: #9ba0ac;
        cursor: not-allowed;
        color: #fff;
      }
    }
  }

  .menu {
    height: 94px;
    margin-bottom: 7px;

    .level1 {
      height: 50px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      overflow: auto;

      .item {
        height: 30px;
        padding: 0 13px;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #687086;
        margin-right: 10px;
        flex-shrink: 0;
        cursor: pointer;
      }

      .active {
        background: #006eff;
        color: #ffffff;
      }
    }

    .level2 {
      height: 44px;
      padding: 0 20px;
      display: flex;
      overflow: auto;

      .item {
        height: 100%;
        white-space: nowrap;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #687086;
        margin-right: 32px;
        cursor: pointer;
      }

      .active {
        position: relative;
        color: #006eff;

        &::after {
          position: absolute;
          left: calc(50% - 9px);
          bottom: 0;
          content: '';
          width: 18px;
          height: 5px;
          background: #006eff;
          border-radius: 3px;
        }
      }
    }
  }

  .no_music {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #483939;
    padding-bottom: 17px;
  }

  .list {
    margin: 0 10px 20px;
    max-height: 265px;
    background: #c5cee6;
    border-radius: 15px;
    justify-items: center;
    display: grid;
    grid-template-columns: repeat(auto-fill, 225px);
    justify-content: space-evenly;
    padding-top: 17px;
    overflow-x: hidden;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    z-index: 1500;

    .item {
      display: flex;
      align-items: center;
      width: 225px;
      height: 45px;
      background: white;
      border-radius: 5px;
      margin-bottom: 17px;
      cursor: pointer;

      .icon {
        width: 40px;
        height: 25px;
        text-align: center;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 25px;
          height: 25px;
        }
      }

      .name {
        flex: 1;
        overflow: hidden;

        .text {
          flex: 1;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #000000;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .text.scroll {
          animation: scroll-text 6s linear infinite;
          overflow: visible;
        }
      }

      .second {
        width: 40px;
        text-align: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #909090;
      }
    }

    .active {
      background: #2967f3;

      .name .text {
        color: white;
      }

      .second {
        color: white;
      }
    }
  }
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}
</style>
