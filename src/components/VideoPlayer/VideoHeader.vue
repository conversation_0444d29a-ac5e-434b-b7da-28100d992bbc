<template>
  <div class="video-header">
    <!-- 关闭按钮 -->
    <div class="close-btn" v-if="!dubbingMode" @click="handleClose">
      <img :src="back" alt="" />
    </div>
    <div class="close-btn-dub" @click="handleCloseDubbing" v-if="dubbingMode">
      <img :src="closeImg" alt="" />
    </div>

    <!-- 右侧按钮组 -->
    <div class="tool-box" v-if="!dubbingMode && message">
      <div class="icon-box" @click.stop="joinVideo">
        <img :src="joinImg" alt="" />
      </div>
      <div class="icon-box" @click.stop="downloadVideo">
        <img :src="save" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { addMessageToQuoteList, joinMediaViaJSBridge, saveMediaViaJSBridge } from '@/utils/messageUtils'
import joinImg from '@/assets/images/ChatAiTool/join.png'
import save from '@/assets/images/ChatAiTool/save.png'
import back from '@/assets/images/img-pre/back.png'
import closeImg from '@/assets/images/ChatAiTool/close-dub.png'

const props = defineProps({
  message: Object,
  dubbingMode: Boolean,
  action: Function,
})

const emit = defineEmits<{
  (e: 'exitFullscreen'): void
}>()

const handleClose = () => {
  emit('exitFullscreen')
}

const handleCloseDubbing = () => {
  props.action?.('dubbing', false)
}

const joinVideo = () => {
  joinMediaViaJSBridge(props.message).then(() => {
    emit('exitFullscreen')
  })
}

const downloadVideo = () => {
  saveMediaViaJSBridge(props.message, () => {
    emit('exitFullscreen')
  })
}
</script>

<style lang="scss" scoped>
.video-header {
  width: 100%;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 7px;

  .close-btn {
    width: 23px;
    height: 23px;
    margin-left: 21px;
    cursor: pointer;

    img {
      width: 100%;
    }
  }

  .close-btn-dub {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 22px;
      height: 20px;
    }
  }

  .tool-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .icon-box {
      width: 28px;
      height: 25px;
      margin-right: 30px;

      img {
        width: 100%;
      }
    }
  }
}
</style>
