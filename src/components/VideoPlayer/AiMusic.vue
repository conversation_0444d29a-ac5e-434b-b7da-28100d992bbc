<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-15 10:04:43
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-15 10:23:42
 * @FilePath: /msb-users-magic-brush/src/components/VideoPlayer/AiMusic.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai-music-container">
    <VideoHeader 
      :message="message"
      :dubbingMode="dubbingMode"
      :action="action"
      @exitFullscreen="handleClose"
    />
    
    <VideoBottom
      :video="video"
      :poster="poster"
      :message="message"
      :show="show"
      :dubbingMode="dubbingMode"
      :action="action"
      @exitFullscreen="handleClose"
      @cutVideo="handleCutVideo"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import VideoHeader from './VideoHeader.vue'
import VideoBottom from './VideoBottom.vue'

const props = defineProps({
  video: String,
  action: Function,
  poster: String,
  message: Object,
})

const emit = defineEmits<{
  (e: 'exitFullscreen'): void
  (e: 'cutVideo'): void
  (e: 'resetVideo'): void
  (e: 'playVideo'): void
  (e: 'pauseVideo'): void
  (e: 'getVideoVolume'): number
  (e: 'setVideoVolume', volume: number): void
  (e: 'onVideoEnded', callback: () => void): void
  (e: 'offVideoEnded', callback: () => void): void
}>()

const show = ref(true)
const dubbingMode = ref(false)

defineOptions({
  name: 'AiMusic',
})

const handleClose = () => {
  emit('exitFullscreen')
}

const handleCutVideo = () => {
  emit('cutVideo')
}
</script>

<style lang="scss" scoped>
.ai-music-container {
  width: 100%;
  position: relative;
}
</style>
