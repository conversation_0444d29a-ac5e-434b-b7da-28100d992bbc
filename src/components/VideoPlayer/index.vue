<template>
  <div class="video-player-container" ref="videoContainer">
    <div class="fake-player" @click="handlePlayClick">
      <img v-if="poster" :src="poster" class="poster-image" alt="视频封面" />
      <div v-else class="default-poster"></div>
      <div class="play-button"></div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'VideoPlayer',
}
</script>

<script lang="ts" setup>
import { ref } from 'vue'
import useChatStore from '@/store/modules/chat'

const chatStore = useChatStore()
const videoContainer = ref<HTMLElement | null>(null)

interface Props {
  src: string
  poster?: string
  fluid?: boolean
  autoplay?: boolean
  controls?: boolean
  preload?: 'auto' | 'metadata' | 'none'
  width?: number | string
  height?: number | string
  message?: any
  onFullscreenClick?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  fluid: true,
  autoplay: false,
  controls: true,
  preload: 'metadata',
  width: '100%',
  height: 'auto',
  message: {},
})

const emit = defineEmits<{
  (e: 'fullscreenClick'): void
}>()

// 处理播放按钮点击
const handlePlayClick = () => {
  // 使用store设置全屏状态和视频信息
  chatStore.setFullscreen(true)
  chatStore.setVideoSrc(props.src)
  chatStore.setVideoPoster(props.poster || '')
  chatStore.setVideoMessage(props.message || {})

  // 设置播放时间为0并标记为正在播放
  chatStore.setFullscreenVideoTime(0)
  chatStore.setFullscreenVideoPlaying(true)

  // 触发全屏点击事件
  emit('fullscreenClick')
}
</script>

<style lang="scss" scoped>
.video-player-container {
  width: 100%;
  border-radius: 0px;
  overflow: hidden;
  background: #ffffff;
  position: relative;
  height: 100%;

  .fake-player {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    .play-button {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('@/assets/images/play.png');
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

  .poster-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .default-poster {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
  }

  .play-button {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    z-index: 2;

    :deep(svg) {
      width: 32px;
      height: 32px;
      color: #ffffff;
    }
  }
}
</style>
