<!--
 * @Author: 
 * @Date: 2025-07-07 10:32:00
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-18 18:14:54
 * @FilePath: /msb-users-magic-brush/src/components/ImageEditor/index.vue
 * @Description: 图片编辑组件 - 支持画笔绘制、橡皮擦和撤销重做功能 (性能优化版)
-->
<template>
  <div v-if="visible" class="image-editor-overlay" @click.stop>
    <div class="image-editor-container">
      <div class="header">
        <div class="close" @click="cancelEdit">
          <img :src="closeIcon" alt="" />
        </div>
        <div class="title">{{ getTitle }}</div>
        <div class="save" @click="saveEdit" :class="{ 'disabled': isSaveDisabled }"
          :disabled="isSaveDisabled || isLoading">
          <div class="success" v-if="type === 'edit'">
            <img :src="saveIcon" alt="" />
          </div>
          <span>{{ getSaveTitle }}</span>
        </div>
      </div>
      <!-- 画布容器 -->
      <div class="edit-canvas-container">
        <canvas ref="editCanvas" class="edit-canvas"></canvas>
        <!-- 画笔预览圆圈 - 固定在画布中心 -->
        <div v-show="showPreview && !isLoading" class="brush-preview" :style="brushPreviewStyle"></div>
        <!-- 加载指示器 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载图片...</div>
        </div>
        <!-- Toast提示 -->
        <div v-if="showToast" class="toast">{{ getToastTitle }}</div>
      </div>
      <!-- 工具栏 -->
      <div class="edit-toolbar">
        <div class="tool-group">
          <div class="tool-btn" :class="{ active: currentTool === 'brush' }" @click="setTool('brush')"
            :disabled="isLoading">
            <div class="img-box"><img :src="brushIcon" alt="" /></div>
          </div>
          <!-- <div class="tool-btn" :class="{ active: currentTool === 'eraser' }" @click="setTool('eraser')"
            :disabled="isLoading">
            <div class="img-box">
              <img :src="easerIcon" alt="" />
            </div>
          </div> -->
        </div>

        <!-- 画笔大小控制 -->
        <div class="brush-size-control">
          <span class="size-label">尺寸</span>
          <div class="size-slider-container">
            <input type="range" class="size-slider" :value="brushSize" @input="updateBrushSize" min="5" max="50"
              step="1" :disabled="isLoading" />
            <!-- <div class="size-value">{{ brushSize }}</div> -->
          </div>
        </div>
        <!-- 历史记录控制 -->
        <div class="history-control">
          <div class="tool-btn" :class="{ 'can-action': canUndo, 'cannot-action': !canUndo }" @click="undo"
            :disabled="isLoading || !canUndo">
            <div class="img-box">
              <img :src="preIcon" alt="" />
            </div>
          </div>
          <div class="tool-btn" :class="{ 'can-action': canRedo, 'cannot-action': !canRedo }" @click="redo"
            :disabled="isLoading || !canRedo">
            <div class="img-box">
              <img :src="nextIcon" alt="" />
            </div>
          </div>
        </div>
        <div class="edit-actions">
          <div class="action-btn" :class="{ 'disabled': isClearDisabled }" @click="clearCanvas"
            :disabled="isClearDisabled">清空</div>
        </div>
      </div>
    </div>

    <!-- 下一步弹窗 -->
    <div v-if="showNextStepModal" class="next-step-modal" @click="closeNextStepModal">
      <div class="modal-bottom" @click.stop>
        <!-- 这里可以添加弹窗内容 -->
        <div class="input-container">
          <textarea v-model="inputText" class="text-input" placeholder="描述你想重绘的内容" rows="1"></textarea>
          <div class="send-btn" :class="{ disabled: isSendDisabled }" @click="handleSend">
            <img :src="isSendDisabled ? sendDisabled : chatSendBtn" alt="发送" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'ImageEditor',
})
import { ref, nextTick, onUnmounted, watch, computed } from 'vue'
import * as fabric from 'fabric'
import { EraserBrush } from '@erase2d/fabric'
import { ossClient } from '@/components/TUIKit/utils/tool'
import closeIcon from '@/assets/images/img-edit/close.png'
import nextIcon from '@/assets/images/img-edit/next.png'
import preIcon from '@/assets/images/img-edit/pre.png'
import brushIcon from '@/assets/images/img-edit/brush.png'
import easerIcon from '@/assets/images/img-edit/easer.png'
import saveIcon from '@/assets/images/img-edit/success.png'
import chatSendBtn from '@/assets/images/chat-send-btn.png'
import sendDisabled from '@/assets/images/send-disabled.png'
import { sendCustomMessage } from '@/utils/customIM'
import useChatStore from '@/store/modules/chat'
import { ElMessage } from 'element-plus'

// 使用store状态
const chatStore = useChatStore()

// 从store中获取状态
const visible = computed(() => chatStore.imageEditor.visible)
const imageUrl = computed(() => chatStore.imageEditor.imageUrl)
const type = computed(() => chatStore.imageEditor.type)
const currentTool = computed(() => chatStore.imageEditor.currentTool)
const brushSize = computed(() => chatStore.imageEditor.brushSize)
const isLoading = computed(() => chatStore.imageEditor.isLoading)
const showPreview = computed(() => chatStore.imageEditor.showPreview)
const showToast = computed(() => chatStore.imageEditor.showToast)
const showNextStepModal = computed(() => chatStore.imageEditor.showNextStepModal)
const inputText = computed({
  get: () => chatStore.imageEditor.inputText,
  set: (value: string) => chatStore.setImageEditorInputText(value),
})
const uploadUrl = computed(() => chatStore.imageEditor.uploadUrl)
const canUndo = computed(() => chatStore.imageEditor.canUndo)
const canRedo = computed(() => chatStore.imageEditor.canRedo)

// 本地状态（不需要在store中管理的）
const editCanvas = ref<HTMLCanvasElement>()
let hidePreviewTimer: number | null = null // 隐藏预览的定时器
const hideToastTimer = ref<number | null>(null) // 隐藏Toast的定时器
// 撤销和重做的历史记录
const historyStack = ref<fabric.Object[]>([]) // 操作历史栈
const redoStack = ref<fabric.Object[]>([]) // 重做栈
// 防止重复点击发送按钮
const isSending = ref(false)

// 计算属性 - 画笔预览样式
const brushPreviewStyle = computed(() => ({
  width: `${brushSize.value}px`,
  height: `${brushSize.value}px`,
  transform: 'translate(-50%, -50%)',
  transition: 'all 0.2s ease-out',
}))

// Fabric.js 相关变量
let fabricCanvas: fabric.Canvas | null = null
let originalImage: fabric.Image | null = null
let renderRequestId: number | null = null // 用于 requestAnimationFrame 优化
// 移除onMounted中的Toast显示，改为在画布初始化完成后显示
// Toast相关函数
const showToastMessage = () => {
  console.log('显示Toast提示')
  chatStore.setImageEditorShowToast(true)

  // 清除之前的定时器
  if (hideToastTimer.value) {
    clearTimeout(hideToastTimer.value)
  }

  // 8秒后自动隐藏Toast
  hideToastTimer.value = window.setTimeout(() => {
    console.log('8秒后自动隐藏Toast - 开始执行')
    console.log('隐藏前 showToast.value:', showToast.value)
    chatStore.setImageEditorShowToast(false)
    console.log('隐藏后 showToast.value:', showToast.value)
    hideToastTimer.value = null
    console.log('Toast定时器已清除')
  }, 8000)

  console.log('Toast定时器已设置，8秒后自动隐藏')
}

const hideToastMessage = () => {
  console.log('手动隐藏Toast')
  chatStore.setImageEditorShowToast(false)

  // 清除定时器
  if (hideToastTimer.value) {
    clearTimeout(hideToastTimer.value)
    hideToastTimer.value = null
  }
}
const getToastTitle = computed(() => {
  const currentType = type.value
  if (currentType === 'edit') {
    return '涂抹你想要消除的区域'
  } else if (currentType === 'cutouts') {
    return '涂抹你想要重绘的区域'
  } else {
    return ''
  }
})
const getTitle = computed(() => {
  const currentType = type.value
  if (currentType === 'edit') {
    return '消除'
  } else if (currentType === 'cutouts') {
    return '局部重绘'
  } else {
    return ''
  }
})
const getSaveTitle = computed(() => {
  const currentType = type.value
  if (currentType === 'edit') {
    return '完成'
  } else if (currentType === 'cutouts') {
    return '下一步'
  } else {
    return ''
  }
})

// 关闭下一步弹窗
const closeNextStepModal = () => {
  chatStore.setImageEditorShowNextStepModal(false)
}

// 计算发送按钮是否禁用
const isSendDisabled = computed(() => {
  return inputText.value.trim() === '' || isSending.value
})

// 发送消息
const handleSend = async () => {
  if (isSendDisabled.value) return

  // 设置发送状态，防止重复点击
  isSending.value = true

  try {
    console.log('发送消息:', inputText.value)
    // 这里可以添加发送逻辑

    // 发送后清空输入框并关闭弹窗
    const payload = {
      data: {
        businessID: 'ai_custom_msg',
        content: {
          name: 'cmd_msg',
          data: {
            text: '重绘选区：' + inputText.value,
            referenceList: [
              {
                type: 'image',
                data: chatStore.imageEditor.drawingUrl, // 添加绘制内容图
              },
            ],
            hideDataList: [
              {
                type: 'image',
                data: uploadUrl?.value,
              },
            ],
            cmd: '',
          },
        },
      },
    }
    console.log('🚀 ~ handleSend ~ payload:', payload)
    await sendCustomMessage(payload)
    closeNextStepModal()
    chatStore.setImageEditorInputText('')
    // 关闭所有图片相关弹窗
    chatStore.closeAllImageDialogs()
  } catch (error) {
    console.error('发送消息失败:', error)
  } finally {
    // 无论成功失败，都重置发送状态
    isSending.value = false
  }
}
// 监听 visible 变化，初始化画布
watch(
  () => visible.value,
  newVisible => {
    if (newVisible && imageUrl.value) {
      nextTick(() => {
        initFabricCanvas()
      })
    } else if (!newVisible && fabricCanvas) {
      // 弹窗关闭时清空历史记录，确保下次打开时是全新状态
      clearCanvasHistory()

      // 清理画布
      fabricCanvas.dispose()
      fabricCanvas = null
      originalImage = null
    }
  }
)

// 初始化 Fabric.js 画布
const initFabricCanvas = () => {
  if (!editCanvas.value || !imageUrl.value) return

  chatStore.setImageEditorLoading(true)

  // 创建 Fabric.js 画布
  fabricCanvas = new fabric.Canvas(editCanvas.value, {
    isDrawingMode: false,
    selection: false,
    preserveObjectStacking: true,
  })

  // 优化图片加载性能
  console.log('开始加载图片:', imageUrl.value)

  // 加载原始图像
  fabric.FabricImage.fromURL(imageUrl.value, {
    crossOrigin: 'anonymous',
  })
    .then((img: any) => {
      if (!fabricCanvas) {
        chatStore.setImageEditorLoading(false)

        return
      }

      console.log('图片加载成功，尺寸:', img.width, 'x', img.height)
      originalImage = img

      // 设置画布尺寸以适应图像 - 按比例撑满容器并居中显示
      const container = editCanvas.value?.parentElement?.parentElement
      console.log('🚀 ~ .then ~ container:', container)
      if (!container) {
        console.error('无法获取画布容器(.edit-canvas-container)')
        chatStore.setImageEditorLoading(false)
        return
      }

      // 获取.edit-canvas-container的实际尺寸作为最大可用空间
      const containerWidth = container.clientWidth
      const containerHeight = container.clientHeight

      console.log('容器(.edit-canvas-container)尺寸:', containerWidth, 'x', containerHeight)
      console.log('原始图片尺寸:', img.width, 'x', img.height)

      // 计算图片和容器的宽高比
      const imgRatio = img.width! / img.height!
      const containerRatio = containerWidth / containerHeight

      // 根据宽高比关系选择合适的缩放方式，确保图片完全显示在容器内
      let scale
      if (imgRatio > containerRatio) {
        // 图片宽高比大于容器宽高比，按宽度撑满容器
        scale = containerWidth / img.width!
        console.log('图片较宽，按宽度撑满容器')
      } else {
        // 图片宽高比小于等于容器宽高比，按高度撑满容器
        scale = containerHeight / img.height!
        console.log('图片较高，按高度撑满容器')
      }

      console.log('缩放比例:', scale.toFixed(3), '图片比例:', imgRatio.toFixed(2), '容器比例:', containerRatio.toFixed(2))

      // 计算缩放后的图片尺寸
      const scaledWidth = img.width! * scale
      const scaledHeight = img.height! * scale

      // 设置画布尺寸为缩放后的图片尺寸，保持图片原始比例
      fabricCanvas.setDimensions({
        width: scaledWidth,
        height: scaledHeight,
      })

      console.log('缩放后图片尺寸:', scaledWidth.toFixed(1), 'x', scaledHeight.toFixed(1))
      console.log('画布尺寸已设置为图片缩放尺寸，通过CSS在容器中居中显示')

      // 图片对象填满整个画布，位置设置为(0,0)
      img.set({
        left: 0,
        top: 0,
        scaleX: scale,
        scaleY: scale,
        selectable: false,
        evented: false,
        erasable: false, // 背景图像不可擦除
      })

      fabricCanvas.add(img)
      // 将图像移到最底层
      fabricCanvas.sendObjectToBack(img)
      fabricCanvas.renderAll()

      // 设置默认工具为画笔
      setTool('brush')

      // 加载完成
      chatStore.setImageEditorLoading(false)
      console.log('画布初始化完成')

      // 显示Toast提示
      showToastMessage()

      // 初始化滑块进度
      nextTick(() => {
        const slider = document.querySelector('.size-slider') as HTMLInputElement
        if (slider) {
          const progress = ((brushSize.value - 5) / (50 - 5)) * 100
          slider.style.setProperty('--progress', `${progress}%`)
        }
      })
    })
    .catch((error: any) => {
      console.error('图片加载失败:', error)
      chatStore.setImageEditorLoading(false)
      // 显示错误提示
      alert('图片加载失败，请重试')
    })
}

// 更新画笔大小 - 性能优化版本
const updateBrushSize = (event: Event) => {
  const target = event.target as HTMLInputElement
  const newSize = parseInt(target.value)

  // 避免不必要的更新
  if (newSize === brushSize.value) return

  chatStore.setImageEditorBrushSize(newSize)

  // 使用 requestAnimationFrame 优化 DOM 更新
  if (renderRequestId) {
    cancelAnimationFrame(renderRequestId)
  }

  renderRequestId = requestAnimationFrame(() => {
    // 更新滑块进度颜色
    const progress = ((brushSize.value - 5) / (50 - 5)) * 100
    target.style.setProperty('--progress', `${progress}%`)

    // 显示预览圆圈
    chatStore.setImageEditorShowPreview(true)

    // 清除之前的定时器
    if (hidePreviewTimer) {
      clearTimeout(hidePreviewTimer)
    }

    // 设置2秒后隐藏预览圆圈
    hidePreviewTimer = window.setTimeout(() => {
      chatStore.setImageEditorShowPreview(false)
      hidePreviewTimer = null
    }, 2000)

    // 更新当前工具的大小
    if (fabricCanvas && fabricCanvas.freeDrawingBrush) {
      fabricCanvas.freeDrawingBrush.width = brushSize.value
    }

    renderRequestId = null
  })
}

// 路径创建事件处理函数
const handlePathCreated = (e: any) => {
  if (e.path && currentTool.value === 'brush') {
    e.path.set({ erasable: true })
  }

  // 将创建的路径添加到历史栈
  if (e.path) {
    // 每次有新操作，清空重做栈
    redoStack.value = []

    // 不再使用克隆，而是存储对象的序列化表示
    const pathObj = {
      // 存储路径的序列化数据
      pathData: e.path.toObject(),
      // 标记为路径操作，方便后续处理
      isPathOperation: true,
    } as any

    // 将路径对象的序列化表示添加到历史栈
    historyStack.value.push(pathObj)
    console.log('操作已记录到历史栈', historyStack.value.length, '(已序列化)')
    console.log('路径数据示例:', JSON.stringify(pathObj.pathData).substring(0, 100) + '...')
    console.log('清空按钮状态更新: 可点击 (有', historyStack.value.length, '个操作)')

    // 更新store中的撤销重做状态
    chatStore.setImageEditorCanUndo(historyStack.value.length > 0)
    chatStore.setImageEditorCanRedo(redoStack.value.length > 0)
  }
}

// 从JSON状态恢复画布
const restoreCanvasState = (canvasState: any[]) => {
  if (!fabricCanvas || !originalImage) return

  try {
    // 清除除了背景图片以外的所有对象
    const objects = fabricCanvas.getObjects()
    for (let i = objects.length - 1; i >= 0; i--) {
      const obj = objects[i]
      if (obj !== originalImage) {
        fabricCanvas.remove(obj)
      }
    }

    // 从保存的状态中恢复对象
    if (canvasState && Array.isArray(canvasState)) {
      // 使用备用方法直接创建路径对象
      canvasState.forEach(objData => {
        try {
          // 直接创建路径对象
          if (objData.path) {
            const path = new fabric.Path(objData.path, objData)
            if (fabricCanvas) {
              fabricCanvas.add(path)
            }
          }
        } catch (innerErr) {
          console.error('创建路径对象失败:', innerErr)
        }
      })

      if (fabricCanvas) {
        fabricCanvas.renderAll()
        console.log(`画布状态已恢复 (尝试加载了 ${canvasState.length} 个对象)`)
      }
    } else {
      fabricCanvas.renderAll()
      console.log('画布状态已恢复 (无对象)')
    }
  } catch (error) {
    console.error('恢复画布状态失败:', error)
  }
}

// 监听橡皮擦提交事件
const setupEraserEvents = (eraser: EraserBrush) => {
  eraser.on('end', async (e: any) => {
    console.log('擦除结束')
    if (!e.defaultPrevented) {
      await eraser.commit(e.detail)

      // 橡皮擦操作也需要更新历史栈，以便清空按钮能正确响应
      const eraserObj = {
        eraserData: { type: 'eraser', timestamp: Date.now() },
        isEraserOperation: true,
      } as any

      // 清空重做栈
      redoStack.value = []

      // 将橡皮擦操作添加到历史栈
      historyStack.value.push(eraserObj)
      console.log('橡皮擦操作已记录到历史栈', historyStack.value.length)
      console.log('清空按钮状态更新: 可点击 (有', historyStack.value.length, '个操作)')

      // 更新store中的撤销重做状态
      chatStore.setImageEditorCanUndo(historyStack.value.length > 0)
      chatStore.setImageEditorCanRedo(redoStack.value.length > 0)
    }
  })
}

// 设置工具
const setTool = (tool: 'brush' | 'eraser') => {
  if (!fabricCanvas) return

  chatStore.setImageEditorCurrentTool(tool)
  fabricCanvas.isDrawingMode = true

  if (tool === 'brush') {
    // 设置画笔
    fabricCanvas.freeDrawingBrush = new fabric.PencilBrush(fabricCanvas)
    fabricCanvas.freeDrawingBrush.width = brushSize.value
    fabricCanvas.freeDrawingBrush.color = 'rgba(70, 119, 227, 0.5)'
  } else if (tool === 'eraser') {
    // 设置橡皮擦
    const eraser = new EraserBrush(fabricCanvas)
    eraser.width = brushSize.value

    // 设置橡皮擦事件
    setupEraserEvents(eraser)

    fabricCanvas.freeDrawingBrush = eraser
  }

  // 监听路径创建事件 - 使用现代事件处理方式
  fabricCanvas.off('path:created', handlePathCreated)
  fabricCanvas.on('path:created', handlePathCreated)
}

// 检查是否有绘制内容的函数
const hasDrawnContent = (): boolean => {
  if (!fabricCanvas) return false

  // 检查画布上是否有路径对象（用户绘制的内容）
  const drawnPaths = fabricCanvas.getObjects().filter(obj =>
    obj.type === 'path' && obj !== originalImage
  )

  return drawnPaths.length > 0
}

// 保存编辑结果（黑色背景，画笔区域为白色）- 性能优化版本
const saveEdit = async () => {
  if (!fabricCanvas || !originalImage || isLoading.value || isSaveDisabled.value) return

  // 如果是局部重绘模式，检查是否有涂抹内容
  if (type.value === 'cutouts') {
    const hasContent = historyStack.value.length > 0

    if (!hasContent) {
      // 没有涂抹内容，显示提示并返回
      ElMessage.warning('请先在图片上涂抹需要重绘的区域')
      return
    }

    // 有涂抹内容才显示下一步弹窗
    chatStore.setImageEditorShowNextStepModal(true)
  }

  try {
    // 关闭Toast提示
    hideToastMessage()

    // 设置保存状态，防止用户操作
    chatStore.setImageEditorLoading(true)

    // 临时隐藏背景图片
    originalImage.set({ visible: false })

    // 保存原始设置
    const originalBgColor = fabricCanvas.backgroundColor
    const originalPaths: any[] = []

    // 收集所有绘制的路径并保存原始颜色
    fabricCanvas.getObjects().forEach((obj: any) => {
      if (obj.type === 'path') {
        originalPaths.push({
          obj: obj,
          originalStroke: obj.stroke,
        })
        // 只将画笔笔迹（stroke）设置为白色
        obj.set({
          stroke: 'white',
        })
      }
    })

    // 设置画布背景为黑色
    fabricCanvas.backgroundColor = 'black'

    // 使用 requestAnimationFrame 优化渲染
    requestAnimationFrame(async () => {
      if (!fabricCanvas) return

      fabricCanvas.renderAll()

      // 导出黑色背景、白色画笔的图片 (掩码图)
      const maskDataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      })

      // 存储掩码图的 blob 对象
      const maskResponse = await fetch(maskDataURL)
      const maskBlob = await maskResponse.blob()
      const maskFileName = `mask-${Date.now()}.png`
      const maskFile = new File([maskBlob], maskFileName, { type: 'image/png' })

      try {
        // 上传掩码图到阿里云
        const maskUploadedUrl = await ossClient().upload(maskFile)
        console.log('掩码图上传成功，URL:', maskUploadedUrl)
        chatStore.setImageEditorUploadUrl(maskUploadedUrl)

        // 现在导出只包含原始图片的绘制图（不包含画笔内容）
        // 首先恢复原始图像显示
        originalImage!.set({ visible: true })

        // 临时隐藏所有绘制的路径，只保留原始图片
        const allPaths: any[] = []
        fabricCanvas.getObjects().forEach((obj: any) => {
          if (obj.type === 'path') {
            allPaths.push(obj)
            obj.set({ visible: false }) // 隐藏画笔绘制内容
          }
        })

        // 恢复画布背景色
        fabricCanvas.backgroundColor = originalBgColor
        fabricCanvas.renderAll()

        // 导出只包含原始图片的绘制图
        const drawingDataURL = fabricCanvas.toDataURL({
          format: 'png',
          quality: 1,
          multiplier: 1,
        })

        // 恢复所有路径的可见性，以便用户继续编辑
        allPaths.forEach(path => {
          path.set({ visible: true })
        })
        originalPaths.forEach(({ obj, originalStroke }) => {
          obj.set({
            stroke: originalStroke,
          })
        })
        fabricCanvas.renderAll()

        // 转换为 blob 对象
        const drawingResponse = await fetch(drawingDataURL)
        const drawingBlob = await drawingResponse.blob()
        const drawingFileName = `drawing-${Date.now()}.png`
        const drawingFile = new File([drawingBlob], drawingFileName, { type: 'image/png' })

        // 上传完整图像到阿里云
        const drawingUploadedUrl = await ossClient().upload(drawingFile)
        console.log('绘制图上传成功，URL:', drawingUploadedUrl)
        chatStore.setImageEditorDrawingUrl(drawingUploadedUrl)

        // 恢复状态
        chatStore.setImageEditorLoading(false)

        if (type.value === 'edit') {
          const payload = {
            data: {
              businessID: 'ai_custom_msg',
              content: {
                name: 'cmd_msg',
                data: {
                  text: '消除所选区域',
                  referenceList: [
                    {
                      type: 'image',
                      data: chatStore.imageEditor.drawingUrl, // 添加绘制图
                    },
                  ],
                  hideDataList: [
                    {
                      type: 'image',
                      data: maskUploadedUrl,
                    },
                  ],
                  cmd: '',
                },
              },
            },
          }
          await sendCustomMessage(payload)
          // 关闭所有图片相关弹窗
          chatStore.closeAllImageDialogs()
        }
      } catch (uploadError) {
        console.error('上传失败:', uploadError)
        // 上传失败时恢复状态并显示错误
        chatStore.setImageEditorLoading(false)
        // 可以在这里添加错误提示
        alert('上传失败，请重试')
      }
    })
  } catch (error) {
    console.error('保存操作失败:', error)
    chatStore.setImageEditorLoading(false)
    alert('保存失败，请重试')
  }
}

// 清空画板内容
const clearCanvas = () => {
  if (!fabricCanvas || !originalImage || isLoading.value || isClearDisabled.value) return

  try {
    // 清除除了背景图片以外的所有对象
    const objects = fabricCanvas.getObjects()
    for (let i = objects.length - 1; i >= 0; i--) {
      const obj = objects[i]
      if (obj !== originalImage) {
        fabricCanvas.remove(obj)
      }
    }

    // 清空历史记录栈和重做栈
    historyStack.value = []
    redoStack.value = []

    // 更新store中的撤销重做状态
    chatStore.setImageEditorCanUndo(false)
    chatStore.setImageEditorCanRedo(false)

    // 重新渲染画布
    fabricCanvas.renderAll()

    console.log('画板已清空')
    console.log('清空按钮状态更新: 禁用 (历史栈长度:', historyStack.value.length, ')')
  } catch (error) {
    console.error('清空画板失败:', error)
  }
}

// 清空画板历史记录
const clearCanvasHistory = () => {
  try {
    // 清空历史记录栈和重做栈
    historyStack.value = []
    redoStack.value = []

    // 更新store中的撤销重做状态
    chatStore.setImageEditorCanUndo(false)
    chatStore.setImageEditorCanRedo(false)

    console.log('画板历史记录已清空')
  } catch (error) {
    console.error('清空画板历史记录失败:', error)
  }
}

// 取消编辑
const cancelEdit = () => {
  // 关闭Toast提示
  hideToastMessage()

  // 清空画板历史记录，确保下次打开时是全新状态
  clearCanvasHistory()

  // 关闭所有图片相关弹窗
  chatStore.closeAllImageDialogs()
}

// 实现撤销功能 (上一步)
const undo = () => {
  if (!fabricCanvas || historyStack.value.length === 0 || isLoading.value) return

  console.log('---- 开始撤销操作 ----')
  console.log('当前画布对象数量:', fabricCanvas.getObjects().length)
  console.log('历史栈大小:', historyStack.value.length)

  // 从历史栈中取出最后一个操作
  const lastOperation = historyStack.value.pop()
  if (!lastOperation) return

  try {
    // 将该操作放入重做栈
    redoStack.value.push(lastOperation)

    // 是普通路径操作，直接从画布移除
    const objects = fabricCanvas.getObjects()
    let found = false

    // 从后往前查找非背景图片的路径对象
    for (let i = objects.length - 1; i >= 0; i--) {
      const obj = objects[i]
      if (obj !== originalImage && obj.type === 'path') {
        console.log('找到要移除的路径对象:', i)
        fabricCanvas.remove(obj)
        found = true
        break
      }
    }

    if (!found) {
      console.warn('未找到要撤销的路径对象')
    }

    fabricCanvas.renderAll()

    console.log('撤销完成，历史栈:', historyStack.value.length, '重做栈:', redoStack.value.length)
    console.log('撤销后画布对象数量:', fabricCanvas.getObjects().length)
    console.log('---- 撤销操作结束 ----')

    // 更新store中的撤销重做状态
    chatStore.setImageEditorCanUndo(historyStack.value.length > 0)
    chatStore.setImageEditorCanRedo(redoStack.value.length > 0)
  } catch (error) {
    console.error('撤销操作失败:', error)
  }
}

// 实现重做功能 (下一步)
const redo = () => {
  if (!fabricCanvas || redoStack.value.length === 0 || isLoading.value) return

  console.log('---- 开始重做操作 ----')
  console.log('当前画布对象数量:', fabricCanvas.getObjects().length)
  console.log('历史栈大小:', historyStack.value.length, '重做栈大小:', redoStack.value.length)

  // 从重做栈中取出最后一个操作
  const lastRedoOperation = redoStack.value.pop()
  if (!lastRedoOperation) return

  try {
    // 将该操作放回历史栈
    historyStack.value.push(lastRedoOperation)

    // 路径对象的重做 - 从序列化数据创建新的路径对象
    try {
      // 从序列化数据创建新的路径对象
      const pathData = (lastRedoOperation as any).pathData
      const newPath = new fabric.Path(pathData.path, pathData)
      fabricCanvas.add(newPath)
      fabricCanvas.renderAll()
      console.log('成功添加重做的路径对象')
    } catch (error) {
      console.error('添加重做路径失败:', error)
    }

    console.log('重做完成，历史栈:', historyStack.value.length, '重做栈:', redoStack.value.length)
    console.log('重做后画布对象数量:', fabricCanvas.getObjects().length)
    console.log('---- 重做操作结束 ----')

    // 更新store中的撤销重做状态
    chatStore.setImageEditorCanUndo(historyStack.value.length > 0)
    chatStore.setImageEditorCanRedo(redoStack.value.length > 0)
  } catch (error) {
    console.error('重做操作失败:', error)
  }
}

// 添加键盘快捷键支持
const setupKeyboardShortcuts = () => {
  const handleKeyDown = (event: KeyboardEvent) => {
    // 只有在编辑器可见且不在加载状态时处理快捷键
    if (!visible.value || isLoading.value) return

    // ESC: 关闭弹窗
    if (event.key === 'Escape') {
      event.preventDefault()
      cancelEdit()
    }

    // Ctrl+Z: 撤销
    if (event.ctrlKey && event.key === 'z') {
      event.preventDefault()
      undo()
    }

    // Ctrl+Y: 重做
    if (event.ctrlKey && event.key === 'y') {
      event.preventDefault()
      redo()
    }
  }

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)

  // 返回清理函数
  return () => {
    window.removeEventListener('keydown', handleKeyDown)
  }
}

// 监听组件可见性变化，设置键盘快捷键
watch(
  () => visible.value,
  newVisible => {
    if (newVisible) {
      // 组件显示时添加键盘快捷键
      const cleanupKeyboardShortcuts = setupKeyboardShortcuts()

      // 在组件隐藏时清理键盘事件
      onUnmounted(cleanupKeyboardShortcuts)
    }
  },
  { immediate: true }
)

// 组件卸载时清理 - 完整的资源清理
onUnmounted(() => {
  // 清理 Fabric.js 画布
  if (fabricCanvas) {
    try {
      fabricCanvas.dispose()
    } catch (error) {
      console.error('清理画布失败:', error)
    }
    fabricCanvas = null
  }

  // 清理定时器
  if (hidePreviewTimer) {
    clearTimeout(hidePreviewTimer)
    hidePreviewTimer = null
  }

  if (hideToastTimer.value) {
    clearTimeout(hideToastTimer.value)
    hideToastTimer.value = null
  }

  // 清理 requestAnimationFrame
  if (renderRequestId) {
    cancelAnimationFrame(renderRequestId)
    renderRequestId = null
  }

  // 重置状态
  originalImage = null
  chatStore.setImageEditorLoading(false)
  chatStore.setImageEditorShowPreview(false)
})

// 添加计算属性判断保存按钮是否应该禁用
const isSaveDisabled = computed(() => {
  // 如果是局部重绘模式，且没有绘制内容，则禁用按钮
  if (type.value === 'cutouts') {
    const disabled = historyStack.value.length === 0
    console.log('完成/下一步按钮状态计算:', disabled ? '禁用' : '可点击', '(历史栈长度:', historyStack.value.length, ')')
    return disabled
  }
  // 其他模式（edit模式）也需要有内容才能点击
  const disabled = historyStack.value.length === 0
  console.log('完成按钮状态计算:', disabled ? '禁用' : '可点击', '(历史栈长度:', historyStack.value.length, ')')
  return disabled
})

// 添加计算属性判断清空按钮是否应该禁用
const isClearDisabled = computed(() => {
  // 基于历史栈判断是否有绘制内容，这样可以确保响应性
  const disabled = historyStack.value.length === 0
  console.log('清空按钮状态计算:', disabled ? '禁用' : '可点击', '(历史栈长度:', historyStack.value.length, ')')
  return disabled
})
</script>

<style scoped lang="scss">
.image-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000000;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-editor-container {
  width: 100%;
  height: 100%;
  background: #313131;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    height: 65px;
    // width: 100%;
    background: #000;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px;

    .close {
      height: 21px;
      width: 21px;

      img {
        width: 100%;
      }
    }

    .title {
      height: 18px;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      flex: 1;
      text-align: center;
      line-height: 18px;
    }

    .save {
      width: 87px;
      height: 32px;
      background: #2967f3;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      // 添加禁用状态样式
      &.disabled {
        background: rgba(98, 98, 98, 1);
        cursor: not-allowed;
      }

      .success {
        height: 16px;
        width: 16px;
        margin-right: 17px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
        }
      }

      span {
        height: 13px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #ffffff;
        line-height: 13px;
      }
    }
  }
}

.edit-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  // width: 100%;
  height: 107px;
  background: #000;
  padding: 0 25px;

  .tool-group {
    display: flex;
    flex-direction: row;

    .tool-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 42px;
      height: 42px;
      background: rgba(98, 98, 98, 1);
      border-radius: 5px;

      .img-box {
        width: 23px;
        height: 23px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
        }
      }

      &.active {
        background: #2967f3;
        border-color: #2967f3;
        box-shadow: 0 2px 8px rgba(41, 103, 243, 0.3);
      }
    }

    // .tool-btn:nth-child(1) {
    //   margin-right: 33px;
    // }
  }
}

.history-control {
  display: flex;

  .tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    background: #2967f3;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    .img-box {
      width: 23px;
      height: 23px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
      }
    }

    // 可点击状态 - 蓝色背景
    &.can-action {
      background: rgba(41, 103, 243, 1);
      cursor: pointer;
    }

    // 不可点击状态 - 灰色背景
    &.cannot-action {
      background: rgba(98, 98, 98, 1);
      cursor: not-allowed;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }

  .tool-btn:nth-child(1) {
    margin-right: 33px;
  }
}

.brush-size-control {
  display: flex;
  align-items: center;
  color: #fff;
  flex: 1;
  margin-left: 33px;

  .size-label {
    height: 16px;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #626262;
    display: inline-block;
    line-height: 16px;
  }

  .size-slider-container {
    display: flex;
    align-items: center;
    margin-left: 12px;
  }

  .size-slider {
    width: 225px;
    height: 8px;
    background: linear-gradient(to right, #2967f3 0%, #2967f3 var(--progress, 50%), rgba(255, 255, 255, 0.3) var(--progress, 50%), rgba(255, 255, 255, 0.3) 100%);
    border-radius: 4px;
    outline: none;
    appearance: none;
    cursor: pointer;
    position: relative;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 25px;
      height: 25px;
      background: #ffffff;
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
      border: 2px solid #2967f3;
      position: relative;
      z-index: 2;
    }

    &::-moz-range-thumb {
      width: 25px;
      height: 25px;
      background: #ffffff;
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid #2967f3;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    }
  }

  .size-value {
    min-width: 24px;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.edit-actions {
  display: flex;
  margin-left: 35px;

  .action-btn {
    padding: 13px 24px;
    background: #2967f3;
    /* 默认为蓝色 */
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;

    &:hover:not(:disabled):not(.disabled) {
      background: #1e4fd1;
      transform: translateY(-1px);
    }

    &:disabled,
    &.disabled {
      background: rgba(98, 98, 98, 1);
      /* 禁用状态为灰色 */
      cursor: not-allowed;
      transform: none;
    }

    &.primary {
      background: #2967f3;
      border-color: #2967f3;
      box-shadow: 0 2px 8px rgba(41, 103, 243, 0.3);

      &:hover:not(:disabled):not(.disabled) {
        background: #1e4fd1;
        box-shadow: 0 4px 12px rgba(41, 103, 243, 0.4);
      }
    }
  }
}

.edit-canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 防止画布超出容器 */

  .edit-canvas {
    display: block;
    /* 确保画布正确显示 */
  }
}

.brush-preview {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 4px dashed rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  will-change: transform, opacity;
  animation: pulse 2s ease-in-out infinite;
}

.toast {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: toast-fade-in 0.3s ease-out;
}

// Toast动画
@keyframes toast-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #2967f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    color: #fff;
    font-size: 14px;
    text-align: center;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 下一步弹窗样式
.next-step-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 10001;
  display: flex;
  justify-content: center;
  align-items: flex-end;

  .modal-bottom {
    width: 100%;
    height: 140px;
    background: #f2f5fc;
    border-radius: 15px 15px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;

    .input-container {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 718px;
      height: 60px;
      background: #ffffff;
      border-radius: 30px;

      .text-input {
        flex: 1;
        // width: 475px;
        border: none;
        background: #ffffff;
        min-height: 40px;
        /* 一行文字的高度 */
        line-height: 40px;
        /* 确保文字垂直居中 */
        resize: none;
        /* 禁用右下角调整大小手柄 */
        overflow-y: auto;
        /* 超出内容隐藏（但用户仍可输入多行） */
        overflow-x: hidden;
        font-size: 20px;
        margin-left: 20px;

        &::placeholder {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 19px;
          color: #909090;
        }
      }

      .text-input:focus {
        outline: none;
        box-shadow: none;
      }

      .send-btn {
        width: 40px;
        height: 40px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-right: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        &.disabled {
          cursor: not-allowed;
        }

        &:not(.disabled):hover {
          transform: scale(1.05);
          transition: transform 0.2s ease;
        }
      }
    }
  }
}
</style>
