<template>
  <div v-if="dialogVisible" class="fullscreen-overlay">
    <div class="fullscreen-video-wrapper">
      <AiMusic :video="videoSrc" :poster="poster" :message="message" :action="action" @exitFullscreen="handleClose" />
      <div class="fullscreen-video-container">
        <video ref="fullscreenVideo" class="video-js vjs-default-skin vjs-big-play-centered vjs-fluid" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'FullscreenVideo',
}
</script>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import type Player from 'video.js/dist/types/player'
import AiMusic from '../VideoPlayer/AiMusic.vue'
import useChatStore from '@/store/modules/chat'

const chatStore = useChatStore()

let fullscreenPlayer: Player | null = null

const dialogVisible = ref(false)
const fullscreenVideo = ref<HTMLVideoElement | null>(null)
let originalOverflow = ''

// 从store获取视频相关属性
const videoSrc = ref('')
const poster = ref('')
const message = ref({})

// 监听store中的全屏状态
watch(
  () => chatStore.fullscreen,
  newValue => {
    dialogVisible.value = newValue
    if (newValue) {
      disableInteractions()
      // 重置全屏播放器的播放时间
      if (fullscreenPlayer) {
        fullscreenPlayer.currentTime(0)
      }
    } else {
      enableInteractions()
    }
  }
)

// 监听store中的视频源变化
watch(
  () => chatStore.videoSrc,
  newValue => {
    videoSrc.value = newValue
  }
)

// 监听store中的海报变化
watch(
  () => chatStore.videoPoster,
  newValue => {
    poster.value = newValue
  }
)

// 监听store中的消息变化
watch(
  () => chatStore.videoMessage,
  newValue => {
    message.value = newValue
  }
)

const lockScroll = () => {
  originalOverflow = document.body.style.overflow
  document.body.style.overflow = 'hidden'
}

const unlockScroll = () => {
  document.body.style.overflow = originalOverflow
}

// 在打开全屏时禁用所有交互
const disableInteractions = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.touchAction = 'none'
  document.body.style.userSelect = 'none'
  document.body.style.webkitUserSelect = 'none'
  document.body.style.pointerEvents = 'none'
}

// 在关闭全屏时恢复交互
const enableInteractions = () => {
  document.body.style.overflow = originalOverflow
  document.body.style.touchAction = ''
  document.body.style.userSelect = ''
  document.body.style.webkitUserSelect = ''
  document.body.style.pointerEvents = ''
}

const handleClose = () => {
  console.log('handleClose')
  chatStore.setFullscreen(false)
  enableInteractions()
  if (fullscreenPlayer) {
    fullscreenPlayer.pause()
    // Remove progress synchronization
    chatStore.setFullscreenVideoPlaying(false)
  }
}

const action = (type: string, data: any) => {
  console.log('action', type)
  switch (type) {
    case 'music':
      if (data) {
        fullscreenPlayer?.pause()
        chatStore.setFullscreenVideoPlaying(false)
      }
      break
    case 'merge':
      // 播放器退出全屏
      fullscreenPlayer?.exitFullscreen()
      fullscreenPlayer?.pause()
      chatStore.setFullscreenVideoPlaying(false)
      handleClose()
      break
    default:
      break
  }
}

// 初始化全屏播放器
const initializeFullscreenPlayer = () => {
  if (!fullscreenVideo.value || !videoSrc.value) return

  fullscreenPlayer = videojs(fullscreenVideo.value, {
    controls: true,
    autoplay: false, // 确保不自动播放
    fluid: true,
    bigPlayButton: true,
    controlBar: {
      pictureInPictureToggle: false,
      fullscreenToggle: false,
      volumePanel: {
        inline: true,
      },
    },
    sources: [{ src: videoSrc.value, type: 'video/mp4' }],
  })

  // 添加大播放按钮居中的CSS类
  fullscreenPlayer.addClass('vjs-big-play-centered')

  // 移除之前的播放状态同步
  // if (chatStore.fullscreenVideoPlaying) {
  //   fullscreenPlayer.play()
  // }

  setupFullscreenPlayerEvents()
}

// 设置全屏播放器事件监听
const setupFullscreenPlayerEvents = () => {
  if (!fullscreenPlayer) return

  // 监听全屏播放器的播放/暂停事件
  fullscreenPlayer.on('play', () => {
    chatStore.setFullscreenVideoPlaying(true)

    // 隐藏全屏播放器的大播放按钮
    const bigPlayButton = fullscreenPlayer.el().querySelector('.vjs-big-play-button')
    if (bigPlayButton) {
      ;(bigPlayButton as HTMLElement).style.display = 'none'
    }
  })

  fullscreenPlayer.on('pause', () => {
    chatStore.setFullscreenVideoPlaying(false)
  })

  // Remove timeupdate event listener

  // 禁用双击全屏
  fullscreenPlayer.off('dblclick')
}

// 监听dialogVisible变化，初始化全屏播放器
watch(
  () => dialogVisible.value,
  newValue => {
    if (newValue) {
      nextTick(() => {
        initializeFullscreenPlayer()
      })
    }
  }
)

onBeforeUnmount(() => {
  enableInteractions()

  if (fullscreenPlayer) {
    try {
      fullscreenPlayer.dispose()
    } catch (e) {
      console.warn('Error disposing fullscreen player:', e)
    }
    fullscreenPlayer = null
  }
})
</script>

<style lang="scss" scoped>
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  pointer-events: auto !important;
  isolation: isolate;

  /* * {
    touch-action: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: auto !important;
  } */
}

.fullscreen-video-wrapper {
  background: black;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  display: flex;
  flex-direction: column;
}

.fullscreen-video-container {
  width: 100vw;
  flex: 1;
  object-fit: contain;

  :deep(.video-js) {
    width: 100%;
    height: 100%;
    pointer-events: auto;

    &.vjs-fluid {
      padding-top: 0 !important;
      height: 100% !important;
    }

    // 确保视频控件可以正常交互
    .vjs-control-bar {
      pointer-events: auto !important;
    }

    // 隐藏画中画和全屏按钮
    .vjs-picture-in-picture-control,
    .vjs-fullscreen-control {
      display: none !important;
    }
  }
}
</style>
