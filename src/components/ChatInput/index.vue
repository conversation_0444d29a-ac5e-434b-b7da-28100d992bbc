<template>
  <div class="ai-video" :class="{ 'slide-down': isClosing }" @click="handleClose">
    <div class="content-box" @click.stop>
      <div class="content-area">
        <div class="close-btn" @click="handleClose">
          <img src="@/assets/images/close.png" alt="close" />
        </div>
        <div class="content-view-text">{{ title }}</div>
        <div v-if="chatStore.chooseTool === 'images_2_video_hs'" class="content-view">
          <SwiperUpload :maxLen="1" @change="urls => change(urls, 1)" />
        </div>
        <div v-if="chatStore.chooseTool === 'images_2_video'" class="content-view">
          <SwiperUpload :maxLen="1" @change="urls => change(urls, 1)" />
        </div>
        <div v-if="chatStore.chooseTool === 'refer_2_video'" class="content-view">
          <SwiperUpload :maxLen="3" @change="urls => change(urls, 3)" />
        </div>
      </div>
      <div class="chat-input-container slide-up">
        <div class="custom-message-input-toolbar-box">
          <CustomMessageInputToolbar />
        </div>
        <div class="input-wrapper">
          <div class="audio-wrapper">
            <AudioRecorder @recordingComplete="handleRecordingComplete" />
          </div>
          <div class="input-edit-wrapper">
            <CustomMessageInput v-model="inputText" ref="customMessageInputRef" />
          </div>
          <div class="send-wrapper">
            <ChatSendBtn @send="handleSend" :disabled="disabledSend" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Camera, Position } from '@element-plus/icons-vue'
import { ref, watch, computed, defineAsyncComponent } from 'vue'
import { storeToRefs } from 'pinia'
import { sendCustomMessage } from '../../utils/customIM'
// 使用defineAsyncComponent动态导入组件
const SwiperUpload = defineAsyncComponent(() => import('../SwiperUpload/index.vue'))
const AudioRecorder = defineAsyncComponent(() => import('../AudioRecorder/index.vue'))
const CustomMessageInput = defineAsyncComponent(() => import('../TUIKit/components/TUIChat/custom-message-input/index.vue'))
const ChatSendBtn = defineAsyncComponent(() => import('../TUIKit/components/TUIChat/chat-send-btn/index.vue'))
const CustomMessageInputToolbar = defineAsyncComponent(() => import('../TUIKit/components/TUIChat/custom-message-input-toolbar/index.vue'))
import useChatStore from '@/store/modules/chat'
const chatStore = useChatStore()
// const { sendStatus } = storeToRefs(chatStore)
const isClosing = ref(false)
const activeTabIndex = ref(0) // 默认选中"文生视频"选项卡
const imageUrls = ref<string[]>([])
const customMessageInputRef = ref<any>(null) // 使用any类型暂时绕过类型检查
const inputText = ref('')
console.log('🚀 ~ inputText:', inputText)

const disabledSend = computed(() => {
  // 根据不同工具类型设置不同的发送条件
  if (['images_2_video_hs', 'images_2_video', 'refer_2_video'].includes(chatStore.chooseTool)) {
    // 图生视频类工具：需要有图片或者文字
    return !(imageUrls.value.length > 0 || inputText.value.trim() !== '')
  } else if (['text_2_video_hs', 'text_2_video'].includes(chatStore.chooseTool)) {
    // 文生视频类工具：只需要文字
    return inputText.value.trim() === ''
  }
  // 默认情况：需要文字
  return inputText.value.trim() === ''
})

defineOptions({
  name: 'ChatInput',
})

const title = computed(() => {
  const tool = chatStore.toolList.find((item: { value: any }) => item.value === chatStore.chooseTool)
  return tool?.title
})

// 图片上传成功后的回调
const change = (urls: string[], num: number) => {
  console.log('SwiperUpload change:', urls, num)
  imageUrls.value = urls
}

async function handleClose() {
  isClosing.value = true
  setTimeout(() => {
    chatStore.setShowAiVideo(false)
    isClosing.value = false
  }, 300)
}

// 处理录音完成事件
function handleRecordingComplete(text: string) {
  console.log("🚀 ~ handleRecordingComplete ~ text:", text)
  // 这里可以处理录音完成后的逻辑
  console.log('录音完成:', text)
  inputText.value = inputText.value + text
}

async function handleSend() {
  const payload = {
    data: {
      businessID: 'ai_custom_msg',
      content: {
        name: chatStore.chooseTool, //text_2_video  images_2_video refer_2_video
        data: {
          text: inputText.value,
          images: imageUrls.value,
        },
      },
    },
  }
  console.log('🚀 ~ handleSend ~ payload:', payload)
  try {
    await sendCustomMessage(payload)
    // chatStore.setSendStatus(true)
    // 消息发送成功后清空输入框和选中的图片
    inputText.value = ''
    imageUrls.value = []

    handleClose()
  } catch (error) {
    throw error
  }
}

// watch(sendStatus, newVal => {
//   console.log('🚀 ~ newVal:', newVal)
//   if (newVal) {
//     handleClose()
//   }
// })
</script>

<style lang="scss" scoped>
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.ai-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #00000040;
  z-index: 100;
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.7);
    .el-loading-text {
      color: #409eff;
      font-size: 14px;
      margin-top: 8px;
    }
  }

  &.slide-down {
    animation: slideDown 0.4s cubic-bezier(0.33, 1, 0.68, 1) forwards;
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000;
  }
  .content-box {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background: #dee5f6;
    border-radius: 15px 15px 0px 0px;
    .content-area {
      // flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      overflow: auto;
      position: relative;
      .close-btn {
        position: absolute;
        top: 25px;
        right: 25px;
        width: 30px;
        height: 30px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .content-view-text {
        width: 100%;
        height: 79px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #000000;
        text-align: center;
        line-height: 79px;
      }
      .content-view {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        margin-bottom: 40px;
      }
    }

    .chat-input-container {
      margin-top: 20px;
      width: 100%;
      // background: #dee5f6;
      // border-radius: 15px 15px 0px 0px;
      .custom-message-input-toolbar-box {
        display: flex;
        padding: 0 25px;
      }
      .input-wrapper {
        display: flex;
        flex-direction: row;
        height: 60px;
        margin-bottom: 28px;
        padding: 0 25px;
        align-items: center;
        justify-content: space-around;
        margin-top: 27px;
        .audio-wrapper {
        }
      }
      .input-edit-wrapper {
        position: relative;
        flex: 1;
        width: 0;
        margin: 0 16px;
        height: 62px;
      }
    }
  }
}
</style>
