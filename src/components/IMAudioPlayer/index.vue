<!--
 * @Author: <PERSON> Assistant
 * @Date: 2023-05-22
 * @LastEditors: songjingyang <EMAIL>
 * @Description: 音频播放器组件，基于howler.js
-->
<template>
  <div class="audio-player">
    <div class="audio-player-container">
      <div class="audio-chat">
        <div class="audio-chat-bg">
          <img :src="audioPlayBgImg" alt="音频" v-if="isPlaying" />
          <img :src="audioPlayGifImg" alt="音频" class="audio-chat-gif" v-if="isPlaying" />
          <img :src="audioChatImg" alt="音频" class="audio-chat-img" v-if="!isPlaying" />
        </div>
      </div>
      <div class="audio-controls" @click.stop="togglePlay">
        <div class="audio-controls-play">
          <img :src="isPlaying ? audioPauseImg : audioPlayImg" alt="音频" />
        </div>
      </div>
      <div class="audio-player-progress-wrapper">
        <div class="audio-player-progress" @click.stop="seek" @mousedown="startDrag" ref="progressBarRef">
          <div class="audio-player-progress-inner" :style="{ width: `${progressPercent}%` }"></div>
          <div class="audio-player-progress-thumb" :style="{ left: `${progressPercent}%` }"></div>
        </div>
      </div>
      <div class="audio-player-time">
        <div class="audio-player-time-remaining">
          <span class="audio-player-time-remaining-time">{{ formatTime(duration - currentTime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, computed, defineComponent, onMounted, onUnmounted, watch } from 'vue'
import { Howl } from 'howler'
// 使用已有的媒体相关图标
import audioChatImg from '@/assets/images/audio-chat.png'
import audioPauseImg from '@/assets/images/audio-pause.png'
import audioPlayImg from '@/assets/images/audio-play.png'
import audioPlayGifImg from '@/assets/images/audio-play.gif'
import audioPlayBgImg from '@/assets/images/audio-play-bg.png'
import useChatStore from '@/store/modules/chat'

export default defineComponent({
  name: 'IMAudioPlayer',
  props: {
    src: {
      type: String,
      required: true,
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['play', 'pause', 'end', 'load'],
  setup(props, { emit }) {
    const sound = ref<Howl | null>(null)
    const isPlaying = ref(false)
    const duration = ref(0)
    const currentTime = ref(0)
    const progressPercent = ref(0)
    const soundId = ref<number | null>(null)
    const updateTimer = ref<number | null>(null)
    const progressBarRef = ref<HTMLElement | null>(null)
    const isDragging = ref(false)
    const chatStore = useChatStore()

    // 拖拽相关
    const startDrag = (event: MouseEvent) => {
      event.preventDefault()
      isDragging.value = true

      // 立即更新进度条位置
      updateProgressFromEvent(event)

      // 添加全局事件监听
      document.addEventListener('mousemove', handleDrag)
      document.addEventListener('mouseup', stopDrag)
    }

    const handleDrag = (event: MouseEvent) => {
      if (!isDragging.value) return
      updateProgressFromEvent(event)
    }

    const stopDrag = () => {
      isDragging.value = false
      document.removeEventListener('mousemove', handleDrag)
      document.removeEventListener('mouseup', stopDrag)
    }

    const updateProgressFromEvent = (event: MouseEvent) => {
      if (!progressBarRef.value || !sound.value || !duration.value) return

      const rect = progressBarRef.value.getBoundingClientRect()
      const offsetX = event.clientX - rect.left
      let percentage = offsetX / rect.width

      // 确保百分比在0-1之间
      percentage = Math.max(0, Math.min(1, percentage))
      const seekTime = percentage * duration.value

      // 更新UI
      currentTime.value = seekTime
      progressPercent.value = percentage * 100

      // 更新音频位置
      if (soundId.value !== null) {
        sound.value.seek(seekTime, soundId.value)
      } else {
        soundId.value = sound.value.play()
        sound.value.seek(seekTime, soundId.value)
        sound.value.pause(soundId.value)
      }
    }

    // 初始化音频
    const initSound = () => {
      if (sound.value) {
        sound.value.unload()
      }

      sound.value = new Howl({
        src: [props.src],
        html5: true, // 使用HTML5音频，适合流式处理大文件
        preload: true,
        onload: () => {
          duration.value = sound.value?.duration() || 0
          emit('load', { duration: duration.value })

          if (props.autoplay) {
            play()
          }
        },
        onplay: () => {
          isPlaying.value = true
          startUpdateTimer()
          emit('play')
        },
        onpause: () => {
          isPlaying.value = false
          stopUpdateTimer()
          emit('pause')
        },
        onstop: () => {
          isPlaying.value = false
          stopUpdateTimer()
          emit('pause')
        },
        onend: () => {
          isPlaying.value = false
          stopUpdateTimer()
          currentTime.value = 0
          progressPercent.value = 0
          emit('end')
        },
        onseek: () => {
          updateProgress()
        },
      })
    }

    // 开始播放
    const play = () => {
      if (!sound.value) return

      if (soundId.value === null) {
        soundId.value = sound.value.play()
      } else {
        sound.value.play(soundId.value)
      }
    }

    // 暂停播放
    const pause = () => {
      if (!sound.value || soundId.value === null) return
      sound.value.pause(soundId.value)
    }

    watch(
      () => chatStore.mute,
      newValue => {
        pause()
      }
    )

    // 切换播放状态
    const togglePlay = () => {
      if (isPlaying.value) {
        pause()
      } else {
        play()
      }
    }

    // 跳转到特定位置
    const seek = (event: MouseEvent) => {
      updateProgressFromEvent(event)
    }

    // 开始更新进度的定时器
    const startUpdateTimer = () => {
      updateTimer.value = window.setInterval(() => {
        if (!isDragging.value) {
          // 拖拽时不更新进度
          updateProgress()
        }
      }, 100) // 每100ms更新一次进度
    }

    // 停止更新进度的定时器
    const stopUpdateTimer = () => {
      if (updateTimer.value) {
        clearInterval(updateTimer.value)
        updateTimer.value = null
      }
    }

    // 更新进度
    const updateProgress = () => {
      if (!sound.value || soundId.value === null) return

      const seek = sound.value.seek() as number
      currentTime.value = seek
      progressPercent.value = (seek / duration.value) * 100
    }

    // 格式化时间，转换为 mm:ss 格式
    const formatTime = (seconds: number) => {
      if (isNaN(seconds) || !isFinite(seconds)) {
        return '00:00'
      }

      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)

      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }

    // 在组件卸载时清理资源
    onUnmounted(() => {
      stopUpdateTimer()
      stopDrag()
      if (sound.value) {
        sound.value.unload()
      }
    })

    // 监听src变化，重新初始化音频
    watch(
      () => props.src,
      newValue => {
        if (newValue) {
          initSound()
        }
      },
      { immediate: true }
    )

    return {
      isPlaying,
      duration,
      currentTime,
      progressPercent,
      progressBarRef,
      togglePlay,
      seek,
      startDrag,
      formatTime,
      audioPlayBgImg,
      audioPlayGifImg,
      audioChatImg,
      audioPauseImg,
      audioPlayImg,
    }
  },
})
</script>

<style lang="scss" scoped>
.audio-player {
  width: 299px;
  background-color: #ffffff;
  height: 58px;
  transition: all 0.3s ease;

  &-playing {
    background-color: #e6f1ff;
  }

  &-container {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    .audio-chat {
      width: 58px;
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
      .audio-chat-bg {
        width: 58px;
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        .audio-chat-gif {
          width: 35px;
          height: 25px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          // margin: 0 auto;
        }
        .audio-chat-img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .audio-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      height: 26px;
      width: 26px;
      margin-left: 18px;
      margin-right: 13px;
      cursor: pointer;
      .audio-controls-play {
        width: 26px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .audio-player-progress-wrapper {
      position: relative;
      width: 130px;
      height: 20px;
      display: flex;
      align-items: center;
    }

    .audio-player-progress {
      width: 130px;
      height: 3px;
      background: #dee5f6;
      border-radius: 2px;
      position: relative;
      cursor: pointer;

      &-inner {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background-color: #1677ff;
        border-radius: 2px;
      }

      &-thumb {
        position: absolute;
        top: 50%;
        width: 16px;
        height: 16px;
        background-color: #ffffff;

        border-radius: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
        z-index: 1;
        border-radius: 50%;
        border: 2px solid #086df7;
      }
    }
    .audio-player-time {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      margin-left: 10px;
      .audio-player-time-remaining {
        width: 50px;
        height: 20px;
        background-color: #ffffff;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        .audio-player-time-remaining-time {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 15px;
          color: #000000;
          line-height: 30px;
        }
      }
    }
  }
}
</style>
