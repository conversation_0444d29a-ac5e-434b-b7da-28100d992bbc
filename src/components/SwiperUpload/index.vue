<template>
  <div class="swiper-upload">
    <swiper :resistance-ratio="0" watch-slides-progress class="swiper" @swiper="onSwiper" @slideChange="onSlideChange" @setTranslate="onSetTranslate" @setTransition="onSetTransition">
      <swiper-slide class="slide" v-if="urls.length < maxLen">
        <div @click="onFileClick(-1)" class="slide-item" v-loading="loading">
          <div class="add">
            <img :src="uploadImg" alt="" />
            <div class="title">上传参考图</div>
            <div v-if="maxLen > 1" class="tips">上传一个或多个人物/物体/场景作为参考主体最多上传3张图片</div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide class="slide" v-for="(url, index) in urls" :key="url">
        <div @click="onFileClick(index)" class="slide-item">
          <img :src="url" alt="" />
          <div class="remove" @click="e => remove(e, index)">
            <img :src="trashImg" alt="" />
          </div>
        </div>
      </swiper-slide>
    </swiper>
    <input @change="fileChange" type="file" ref="fileInput" accept="image/*" hidden />
  </div>
</template>
<script lang="ts" setup>
import { ref, VNodeRef, watch } from 'vue'
import { Swiper, SwiperSlide } from '@msb-next/swiper/vue'
import '@msb-next/swiper/swiper.scss'
import uploadImg from '@/assets/images/SwiperUpload/upload.png'
import trashImg from '@/assets/images/SwiperUpload/trash.png'
import { base64ToFile, ossClient, resizeBase64Img } from '@/components/TUIKit/utils/tool'

defineOptions({
  name: 'SwiperUpload',
})

const props = defineProps({
  maxLen: Number,
})
const emit = defineEmits(['change'])
const { maxLen = 1 } = props
const fileInput = ref<VNodeRef | null>(null)
const urls = ref<string[]>([])
const changeIndex = ref(-1)
const loading = ref(false)

// swiper配置
const onSwiper = (swiper: any) => {
  onSetTranslate(swiper)
}

// 滑动结束
const onSlideChange = () => {}

// 设置位移
const onSetTranslate = (swiper: { slides: any; width: number }) => {
  const slides = swiper.slides
  const offsetAfter = swiper.width * 0.35 //每个slide的位移值
  for (let i = 0; i < slides.length; i++) {
    const slide = slides.eq(i)
    const slidedom: any = slides[i]
    const progress = slidedom.progress
    slide.transform('translate3d(' + progress * offsetAfter + 'px, 0, 0) scale(' + (1 - Math.abs(progress) / 10) + ')')
    slide.css('zIndex', (1 - Math.abs(progress) / 20) * 100)
  }
}

// 设置过渡
const onSetTransition = (swiper: { slides: { length: number; eq: (arg0: number) => any } }, transition: any) => {
  for (var i = 0; i < swiper.slides.length; i++) {
    const slide = swiper.slides.eq(i)
    slide.transition(transition)
  }
}

// 点击图片
const onFileClick = (index: number) => {
  changeIndex.value = index
  fileInput?.value?.click && fileInput?.value?.click()
}

const uploadFile = async (file: File) => {
  if (!file) {
    return
  }
  try {
    const base64 = await new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = res => {
        resolve(res.target?.result)
      }
    })

    const img = await resizeBase64Img(base64, 1024)

    const resFile = base64ToFile(img.base64, file.name)

    const url = await ossClient().upload(resFile)

    if (changeIndex.value !== -1) {
      urls.value[changeIndex.value] = url
      changeIndex.value = -1
    } else {
      urls.value = [url, ...urls.value]
    }
  } catch (e) {}
}

// 文件改变
const fileChange = async (e: any) => {
  loading.value = true
  const files = e.target.files
  const fileLen = files.length
  const len = changeIndex.value !== -1 ? 1 : Math.min(maxLen - urls.value.length, fileLen)
  for (let index = 0; index < len; index++) {
    if (files[index].size > 0) {
      await uploadFile(files[index])
    }
  }
  e.target.value = ''
  loading.value = false
}

// 删除图片
const remove = (e: { stopPropagation: () => void; preventDefault: () => void }, index: number) => {
  e.stopPropagation()
  e.preventDefault()
  urls.value.splice(index, 1)
}

watch([urls], () => {
  emit('change', urls.value)
})
</script>
<style lang="scss">
.swiper-upload {
  width: 100%;
  height: 100%;
  .swiper {
    height: calc(100vw * 0.82);
  }
  .slide {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .slide-item {
      overflow: hidden;
      border-radius: 20px;
      height: 100%;
      width: 65.34%;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      background: #f2f5fc;
      position: relative;
      img {
        object-fit: contain;
        width: 100%;
        height: 100%;
      }
      .remove {
        width: 58px;
        height: 58px;
        position: absolute;
        z-index: 10;
        right: 25px;
        bottom: 25px;
        border-radius: 100%;
        color: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .add {
      width: 100%;
      height: 100%;
      color: #000;
      font-size: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      flex-direction: column;
      img {
        width: 44px;
        height: 44px;
      }
      .title {
        width: 102px;
        height: 19px;

        font-weight: normal;
        font-size: 20px;
        color: #000000;
        margin-top: 19px;
        margin-bottom: 15px;
      }
      .tips {
        width: 238px;
        height: 30px;

        font-weight: normal;
        font-size: 12px;
        color: #909090;
        line-height: 18px;
      }
    }
  }
}
</style>
