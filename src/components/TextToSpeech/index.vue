<!--
 * @Author: AI助手
 * @Date: 2025-04-30 16:30:00
 * @Description: 文本转语音(TTS)组件
-->
<template>
  <div class="text-to-speech">
    <pre>{{ JSON.stringify(config, null, 2) }}</pre>
    <audio :src="audioUrl" controls />
    <div class="buttons">
      <el-button @click="startTTS">开始合成</el-button>
      <el-button @click="downloadAudio">下载音频</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getToken, buildFullUrl } from '@/api/aiChat'

defineOptions({
  name: 'TextToSpeech',
})

// 状态管理
const audioUrl = ref('')
const downloadCache = ref(new Uint8Array(0))
const isServerError = ref(false)
const config = ref({})

// 生成UUID
const generateUuid = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// 自定义LabTTS函数，模拟byted-ailab-speech-sdk库的功能
const LabTTS = () => {
  return {
    start({ debug, url, config, onStart, onMessage, onError, onClose }) {
      // 创建WebSocket连接
      const ws = new WebSocket(url)
      ws.binaryType = 'arraybuffer'

      // 连接建立时
      ws.onopen = () => {
        onStart?.()
        // 发送配置信息
        ws.send(JSON.stringify(config))
      }

      // 接收消息时
      ws.onmessage = (event) => {
        // 判断是否为二进制数据
        if (event.data instanceof ArrayBuffer) {
          onMessage?.(event.data)
          
          // 创建音频URL
          const blob = new Blob([event.data], { type: 'audio/mp3' })
          const url = URL.createObjectURL(blob)
          audioUrl.value = url
        } else {
          // 处理非二进制消息，如果有的话
          try {
            const jsonData = JSON.parse(event.data)
            console.log('TTS JSON响应:', jsonData)
          } catch (err) {
            console.warn('无法解析非二进制消息:', event.data)
          }
        }
      }

      // 错误处理
      ws.onerror = (event) => {
        console.error('TTS WebSocket错误:', event)
        isServerError.value = true
        onError?.(event)
      }

      // 连接关闭
      ws.onclose = () => {
        console.log('TTS WebSocket连接已关闭')
        onClose?.()
      }

      // 返回空URL，实际URL将在接收数据后设置
      return ''
    }
  }
}

// 开始文本转语音
const startTTS = async () => {
  try {
    audioUrl.value = ''
    downloadCache.value = new Uint8Array(0)
    
    const text_type = 'plain'
    const submitText = '你好啊，我是字节跳动的人工智能实验室语音合成技术负责人'
    const speaker = 'BV001_streaming'
    const auth: Record<string, string> = {}
    
    // tob通过query鉴权
    const appid = '6830638329'
    const accessKey = 'W24MLDIt7H6oa_wWlYTzqeImRS4odTo6'
    const cluster = ''
    
    // 获取token
    const tokenResponse = await getToken(appid, accessKey)
    // @ts-expect-error - 忽略类型错误，直接处理token
    const token = tokenResponse?.jwt_token || tokenResponse
    
    if (token) {
      auth.api_jwt = token
    }
    
    const url = 'wss://openspeech.bytedance.com/api/v1/tts/ws_binary'
    const serviceUrl = buildFullUrl(url, auth)
    
    // 创建TTS配置
    const ttsConfig = {
      app: {
        appid: appid,
        token: 'access_token',
        cluster: cluster,
      },
      user: {
        uid: 'byted sdk DEMO', // 业务自定义
      },
      audio: {
        encoding: 'mp3',
        rate: 24000,
        voice_type: speaker,
      },
      request: {
        reqid: generateUuid(),
        text: submitText,
        text_type,
        operation: 'submit',
      },
    }
    
    // 保存配置用于显示
    config.value = ttsConfig
    
    // 启动TTS处理
    const url_value = LabTTS().start({
      debug: true,
      url: serviceUrl,
      config: ttsConfig,
      onStart: () => {
        isServerError.value = false
        ElMessage.success('开始合成语音')
      },
      onMessage: (audioBuffer: ArrayBuffer) => {
        // 下载缓存音频二进制包
        const newDownloadCache = new Uint8Array(downloadCache.value.byteLength + audioBuffer.byteLength)
        newDownloadCache.set(downloadCache.value, 0)
        newDownloadCache.set(new Uint8Array(audioBuffer), downloadCache.value.byteLength)
        downloadCache.value = newDownloadCache
      },
      onError: (err) => {
        console.warn('TTS错误:', err)
        ElMessage.error('语音合成出错')
      },
      onClose: () => {
        console.log('TTS服务连接已关闭')
        // 可以自动下载，如果需要的话
        // downloadAudio()
      },
    })
    
    if (url_value) {
      audioUrl.value = url_value
    }
  } catch (error) {
    console.error('启动TTS失败:', error)
    ElMessage.error('启动语音合成失败')
  }
}

// 下载音频
const downloadAudio = () => {
  if (downloadCache.value.byteLength === 0) {
    ElMessage.warning('没有可下载的音频')
    return
  }
  
  const blob = new Blob([downloadCache.value], { type: 'audio/mp3' })
  const blobUrl = URL.createObjectURL(blob)
  const aTag = document.createElement('a')
  aTag.download = 'test.mp3'
  aTag.href = blobUrl
  aTag.click()
  URL.revokeObjectURL(blobUrl)
  ElMessage.success('音频下载成功')
}
</script>

<style lang="scss" scoped>
.text-to-speech {
  padding: 20px;
  
  pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow: auto;
    font-size: 12px;
    margin-bottom: 15px;
  }
  
  audio {
    width: 100%;
    margin-bottom: 15px;
  }
  
  .buttons {
    display: flex;
    gap: 10px;
  }
}
</style> 