<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-30 14:14:14
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-05-28 19:19:17
 * @FilePath: /miaobi-admin-magic-touch/src/components/AudioRecorder/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="audio-recorder">
    <div v-if="!isRecording" class="action-btn" @click.stop="startASR" />
    <div v-else class="audio-recording" @click.stop="stopASR">
      <img src="@/assets/images/audio-play.gif" alt="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getToken, buildFullUrl } from '@/api/aiChat'
import { LabASR } from 'byted-ailab-speech-sdk'

// 定义LabASR接口选项
interface LabASROptions {
  onMessage: (text: string, fullData: any) => void
  onStart: () => void
  onClose: () => void
  onError?: (error: any) => void
}

defineOptions({
  name: 'AudioRecorder',
})
const emit = defineEmits(['recordingComplete'])
const isRecording = ref(false)
const recognizedText = ref('')
const fullResponse = ref<Record<string, any>>({})
const recordStopping = ref(false)
const header = ref('')
const appid = ref('6830638329')
const accessKey = ref('W24MLDIt7H6oa_wWlYTzqeImRS4odTo6')
const recordError = ref('')
const recordingText = ref('')
// 增加previousText用于跟踪先前识别的文本
const previousText = ref('')
// 添加超时计时器引用
const timeoutRef = ref<number | null>(null)

// 生成UUID
const generateUuid = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// 创建ASR客户端
const asrClient = computed(() => {
  return LabASR({
    onMessage: async (data: string, fullData: any) => {
      const utterancesData = fullData?.result[0]?.utterances[0]?.definite ? data : ''
      emit('recordingComplete', utterancesData)
      console.log('完整语音识别结果:', data)
      console.log('完整响应数据:', fullData)
    },
    onStart() {
      header.value = '正在录音'
      recognizedText.value = ''
      previousText.value = '' // 重置previousText
      console.log('开始录音 - SDK回调')
      isRecording.value = true
    },
    onClose() {
      header.value = '录音结束'
      console.log('录音结束 - SDK回调')
      // 清除可能存在的超时
      if (timeoutRef.value !== null) {
        clearTimeout(timeoutRef.value)
        timeoutRef.value = null
      }
      isRecording.value = false
      recordStopping.value = false
    },
    // SDK实际上可能不支持onError参数接收错误对象
    onError() {
      console.error('录音错误 - SDK回调')
      // 清除可能存在的超时
      if (timeoutRef.value !== null) {
        clearTimeout(timeoutRef.value)
        timeoutRef.value = null
      }
      ElMessage.error('录音失败')
      isRecording.value = false
      recordStopping.value = false
    },
  })
})

// 开始录音并转文字
const startASR = async () => {
  if (isRecording.value || recordStopping.value) {
    console.log('已经在录音中，忽略此次请求')
    return
  }

  try {
    // 清除之前的错误和超时
    recordError.value = ''
    recordStopping.value = false
    if (timeoutRef.value !== null) {
      clearTimeout(timeoutRef.value)
      timeoutRef.value = null
    }

    console.log('开始语音识别流程...')

    const auth: Record<string, string> = {}
    // 获取token - 小模型
    console.log('正在获取token...')
    const tokenResponse = await getToken(appid.value, accessKey.value)
    console.log('获取token返回:', JSON.stringify(tokenResponse))

    // 处理多种可能的返回格式
    let token = ''

    // 尝试提取token，使用类型断言绕过TypeScript检查
    try {
      const responseAny = tokenResponse as any

      if (typeof responseAny === 'string') {
        token = responseAny
      } else if (responseAny && typeof responseAny === 'object') {
        // 尝试从不同的路径获取token
        if (responseAny.jwt_token) {
          token = responseAny.jwt_token
        } else if (responseAny.data?.jwt_token) {
          token = responseAny.data.jwt_token
        } else if (responseAny.data && typeof responseAny.data === 'string') {
          token = responseAny.data
        } else if (responseAny.token) {
          token = responseAny.token
        } else if (responseAny.access_token) {
          token = responseAny.access_token
        }
      }
    } catch (e) {
      console.error('解析token失败:', e)
    }

    if (!token) {
      console.error('无法获取有效的token, tokenResponse:', tokenResponse)
      throw new Error('无法获取有效的token')
    } else {
      console.log('成功获取token (长度):', token.length)
    }

    // 设置auth对象
    auth.api_jwt = token

    const fullUrl = buildFullUrl('wss://openspeech.bytedance.com/api/v2/asr', auth)
    console.log('构建WebSocket URL成功:', fullUrl.substring(0, 50) + '...')

    const cluster = 'volcengine_input_common'
    const workflowPunctuation = 'audio_in,resample,partition,vad,fe,decode,nlu_punctuate'

    // 准备参数
    const params = {
      url: fullUrl,
      config: {
        app: {
          appid: appid.value,
          token: token,
          cluster: cluster,
        },
        user: {
          uid: 'miaobi-admin-user', // 业务方用户自定义，方便问题排查
        },
        audio: {
          format: 'pcm',
          rate: 16000,
          bits: 16,
          channel: 1,
        },
        request: {
          reqid: generateUuid(),
          workflow: workflowPunctuation,
          sequence: 1,
          result_type: 'single',
          show_utterances: true,
        },
      },
    }
    console.log('🚀 ~ startASR ~ params:', params)

    console.log('连接WebSocket...')
    asrClient.value.connect(params)

    console.log('开始录音...')
    await asrClient.value.startRecord()

    ElMessage.success('开始录音')

    // 确保在开始新录音时重置previousText
    previousText.value = ''
  } catch (error: any) {
    console.error('开始录音失败:', error)
    console.error('错误堆栈:', error?.stack)
    const errorMsg = error?.message || '未知错误'
    ElMessage.error('开始录音失败: ' + errorMsg)
    recordError.value = '录音失败: ' + errorMsg
    recordStopping.value = false
    isRecording.value = false
  }
}

// 停止录音
const stopASR = () => {
  if (recordStopping.value || !isRecording.value) {
    console.log('录音已经停止或未开始，忽略此次请求')
    return
  }

  console.log('停止录音流程')
  recordStopping.value = true

  try {
    // 清除之前的错误和超时
    recordError.value = ''
    if (timeoutRef.value !== null) {
      clearTimeout(timeoutRef.value)
      timeoutRef.value = null
    }

    console.log('调用SDK停止录音方法...')
    // 停止录音
    asrClient.value.stopRecord()

    // 添加安全超时处理，确保UI状态最终会更新
    timeoutRef.value = window.setTimeout(() => {
      if (isRecording.value) {
        console.log('录音停止超时，强制更新UI状态')
        isRecording.value = false
        recordStopping.value = false
      }
      timeoutRef.value = null
    }, 3000)

    ElMessage.success('录音结束')
  } catch (error: any) {
    console.error('停止录音失败:', error)
    console.error('错误堆栈:', error?.stack)
    const errorMsg = error?.message || '未知错误'
    ElMessage.error('停止录音失败: ' + errorMsg)
    recordError.value = '停止录音失败: ' + errorMsg
    isRecording.value = false
    recordStopping.value = false
  }
}

// 当页面对于用户不可见时停止录音
onMounted(() => {
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (document.hidden && isRecording.value) {
      console.log('页面不可见，停止录音')
      stopASR()
    }
  })

  // 监听浏览器卸载事件，确保录音被停止
  window.addEventListener('beforeunload', () => {
    if (isRecording.value) {
      console.log('页面卸载，停止录音')
      stopASR()
    }
  })

  // 在组件挂载时检查麦克风权限
  checkMicrophonePermission()
})

// 检查麦克风权限
const checkMicrophonePermission = async () => {
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    // 立即释放资源
    console.log('🚀 ~ checkMicrophonePermission ~ stream:', stream)
    stream.getTracks().forEach(track => track.stop())
    console.log('麦克风权限已获取')
  } catch (error) {
    console.error('麦克风权限获取失败:', error)
    ElMessage.warning('请允许访问麦克风，否则无法使用录音功能')
  }
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    console.log('组件卸载，停止录音')
    stopASR()
  }

  // 清除可能存在的超时
  if (timeoutRef.value !== null) {
    clearTimeout(timeoutRef.value)
    timeoutRef.value = null
  }

  // 移除事件监听
  document.removeEventListener('visibilitychange', () => {})
  window.removeEventListener('beforeunload', () => {})
})
</script>

<style lang="scss" scoped>
.audio-recorder {
  display: inline-block;
  position: relative;
  width: 64px;
  height: 64px;
  .action-btn {
    height: 100%;
    width: 100%;
    background-image: url('../../assets/images/audio.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
  }
  .audio-recording {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/images/audio-btn-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 35px;
      height: 25px;
    }
  }
  .recognized-text {
    position: absolute;
    top: 100%;
    left: 0;
    width: 300px;
    margin-top: 10px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
    word-break: break-all;
  }
  .record-error {
    position: absolute;
    top: 100%;
    left: 0;
    width: 300px;
    margin-top: 10px;
    padding: 8px;
    background-color: #fef0f0;
    color: #f56c6c;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
    word-break: break-all;
    border: 1px solid #fde2e2;
  }
}
</style>
