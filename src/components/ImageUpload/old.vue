<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="''"
      list-type="picture-card"
      :auto-upload="true"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><plus /></el-icon>
      
      <!-- 自定义文件项内容 -->
      <template #file="{ file }">
        <div class="el-upload-list__item-thumbnail" v-if="isImage(file.fileType || getFileExtension(file.name))">
          <img :src="file.url" alt="" />
        </div>
        <div class="el-upload-list__item-thumbnail video-thumbnail" v-else-if="isVideo(file.fileType || getFileExtension(file.name))">
          <!-- <div class="video-icon-wrapper">
            <el-icon class="video-icon"><VideoPlay /></el-icon>
          </div> -->
          <img :src="`${file.url}?x-oss-process=video/snapshot,t_0,f_jpg,w_800,h_600,m_fast`" alt="" />
        </div>
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <el-icon>
              <ZoomIn v-if="isImage(file.fileType || getFileExtension(file.name))"/>
              <VideoPlay v-else-if="isVideo(file.fileType || getFileExtension(file.name))"/>
            </el-icon>
          </span>
          <span class="el-upload-list__item-delete" @click="handleDelete(file)">
            <el-icon><Delete /></el-icon>
          </span>
        </span>
      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize || videoFileSize">
        图片大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>，
        视频大小不超过 <b style="color: #f56c6c">{{ videoFileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <div class="el-upload__tip" v-if="customTip">
      {{ customTip }}
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="预览"
      width="600px"
      append-to-body
    >
      <img
        v-if="isImage(dialogFileType)"
        :src="dialogImageUrl"
        style="display: block; max-width: 300px; margin: 0 auto"
      />
      <video
        v-else-if="isVideo(dialogFileType)"
        :src="dialogImageUrl"
        controls
        style="display: block; max-width: 300px; margin: 0 auto"
      ></video>
    </el-dialog>
  </div>
</template>

<script setup>
import OSS from "ali-oss";
import { getToken } from "@/utils/auth";
import { isExternal } from "@/utils/validate";
import { getAliyun } from "@/api/common";
import { Delete, ZoomIn, Plus, VideoPlay } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 图片大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 视频大小限制(MB)
  videoFileSize: {
    type: Number,
    default: 50,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg', 'mp4', 'mov']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  customTip: {
    type: String,
    default: "",
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const dialogFileType = ref(""); // 新增：记录当前预览文件的类型
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传的图片服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const ossClient = ref(null);
const preUrl = ref("");
const cdnUrl = ref("");
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize || props.videoFileSize)
);

// 判断是否为图片
const isImage = (fileType) => {
  const imageTypes = ["png", "jpg", "jpeg", "gif", "bmp"];
  return imageTypes.some(type => fileType?.toLowerCase().includes(type));
};

// 判断是否为视频
const isVideo = (fileType) => {
  const videoTypes = ["mp4", "mov", "avi", "wmv", "flv"];
  return videoTypes.some(type => fileType?.toLowerCase().includes(type));
};

// 获取文件扩展名
const getFileExtension = (fileName) => {
  if (fileName && fileName.lastIndexOf(".") > -1) {
    return fileName.slice(fileName.lastIndexOf(".") + 1).toLowerCase();
  }
  return "";
};

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        if (typeof item === "string") {
          if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
            item = { name: baseUrl + item, url: baseUrl + item };
          } else {
            item = { name: item, url: item };
          }
        }
        // 添加文件类型属性
        if (item.name) {
          const extension = getFileExtension(item.name);
          item.fileType = extension;
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 上传前loading加载
const handleBeforeUpload = async (file) => {
  let isValidFile = false;
  let fileExtension = getFileExtension(file.name);
  const isVideoFile = isVideo(fileExtension);
  
  if (props.fileType.length) {
    isValidFile = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isValidFile = file.type.indexOf("image") > -1 || file.type.indexOf("video") > -1;
  }
  
  if (!isValidFile) {
    proxy.$modal.msgError(
      `文件格式不正确，请上传${props.fileType.join("/")}格式文件!`
    );
    return false;
  }
  
  if (file.name.includes(",")) {
    proxy.$modal.msgError("文件名不正确，不能包含英文逗号!");
    return false;
  }
  
  // 根据文件类型应用不同的大小限制
  const sizeLimit = isVideoFile ? props.videoFileSize : props.fileSize;
  const fileSize = file.size / 1024 / 1024;
  if (sizeLimit && fileSize > sizeLimit) {
    proxy.$modal.msgError(
      `上传${isVideoFile ? '视频' : '图片'}大小不能超过 ${sizeLimit} MB!`
    );
    return false;
  }
  
  proxy.$modal.loading(`正在上传${isVideoFile ? '视频' : '图片'}，请稍候...`);
  number.value++;
  try {
    // 生成OSS文件路径
    const fileName =
      preUrl.value + "/" + new Date().getTime() + "-" + file.name;
    // 执行上传
    const result = await ossClient?.value.put(fileName, file);
    // 手动触发成功回调
    handleUploadSuccess(
      {
        code: 200,
        fileName: cdnUrl.value + "/" + result.name,
        url: cdnUrl.value + "/" + result.name,
        fileType: fileExtension,
        isVideo: isVideoFile
      },
      file
    );

    return false; // 阻止 el-upload 默认上传
  } catch (error) {
    proxy.$modal.msgError("上传失败");
    proxy.$modal.closeLoading();
    return false;
  }
};

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    const fileExtension = getFileExtension(file.name);
    
    uploadList.value.push({ 
      name: res.fileName, 
      url: res.fileName,
      fileType: fileExtension
    });
    
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", listToString(fileList.value));
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", listToString(fileList.value));
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传文件失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogFileType.value = file.fileType || getFileExtension(file.name);
  dialogVisible.value = true;
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
// 获取OSS配置信息
const getOssPolicy = async () => {
  const res = await getAliyun();
  return res.data;
};

// 初始化OSS客户端
const initOssClient = async () => {
  const policy = await getOssPolicy();
  preUrl.value = policy?.preUrl;
  cdnUrl.value = policy?.cdnUrl;
  console.log({
    region: "oss-cn-hangzhou",
    authorizationV4: true,
    accessKeyId: policy?.credentials?.accessKeyId,
    accessKeySecret: policy?.credentials?.accessKeySecret,
    stsToken: policy?.credentials?.securityToken,
    bucket: policy?.bucketName,
  });
  ossClient.value = new OSS({
    region: "oss-cn-hangzhou",
    authorizationV4: true,
    accessKeyId: policy?.credentials?.accessKeyId,
    accessKeySecret: policy?.credentials?.accessKeySecret,
    stsToken: policy?.credentials?.securityToken,
    bucket: policy?.bucketName,
    refreshSTSToken: async () => {
      // 调用后端接口获取新的STS Token
      const res = await getAliyun();
      return {
        accessKeyId: policy?.credentials?.accessKeyId,
        accessKeySecret: policy?.credentials?.accessKeySecret,
        stsToken: policy?.credentials?.securityToken,
      };
    },
    refreshSTSTokenInterval: 30 * 60 * 1000, // 30分钟刷新一次
  });
};
initOssClient();
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}

// 视频预览相关样式
:deep(.el-dialog__body) {
  text-align: center;
  
  video {
    max-width: 100%;
    max-height: 70vh;
  }
}

// 视频缩略图样式
:deep(.video-thumbnail) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #2c3e50;
  
  .video-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    
    .video-icon {
      font-size: 24px;
      color: #fff;
    }
  }
}

:deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail) {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
