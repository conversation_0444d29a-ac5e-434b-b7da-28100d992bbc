<template>
  <div class="upload">
    <input ref="inputRef" style="display: none" title="视频" type="file" data-type="video" accept="video/*,image/*"
      @change="sendVideoInWeb" multiple limit="2" />
    <div v-if="loading" class="loading-container">
      <el-progress type="circle" :percentage="percentage" class="progress" :stroke-width="8" color="#0DF1FD" />
      <div v-if="totalFiles > 1" class="file-info">{{ currentFileIndex + 1 }}/{{ totalFiles }}</div>
    </div>
    <img v-else @click="onIconClick" :src="uploadImg" alt="" />
  </div>
</template>
<script lang="ts" setup>
import { getAliyun2 } from '@/api/common'
import uploadImg from '@/assets/images/ChatAiTool/add.png'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { blobToBase64, resizeBase64Img, base64ToFile, getVideoBase64, ossClient } from '@/components/TUIKit/utils/tool'
import { sendCustomMessage } from '@/utils/customIM'

const inputRef = ref()
const loading = ref(false)
const percentage = ref(0)
const _t = ref(0)
const totalFiles = ref(0)
const currentFileIndex = ref(0)

const onIconClick = () => {
  inputRef?.value?.click && inputRef?.value?.click()
}

const sendVideoInWeb = (e: any) => {
  if (e?.target?.files?.length <= 0) {
    return
  }
  if (e?.target?.files?.length > 2) {
    ElMessage.error('最多上传2个文件')
    e.target.value = ''
    return
  }
  // 过滤只保留图片和视频文件
  const filesArray = Array.from(e.target.files).filter((file: any) => {
    const isImage = file.type?.includes('image')
    const isVideo = file.type?.includes('video')
    return isImage || isVideo
  })

  if (filesArray.length === 0) {
    ElMessage.error('请选择图片或视频文件')
    e.target.value = ''
    return
  }

  // 设置文件总数和重置当前文件索引
  totalFiles.value = filesArray.length
  currentFileIndex.value = 0

  // 开始处理第一个文件
  processNextFile(filesArray)

  e.target.value = ''
}

const processNextFile = async (files: any[]) => {
  if (currentFileIndex.value < files.length) {
    const file = files[currentFileIndex.value]
    // 显示当前处理的文件索引
    console.log(`处理第 ${currentFileIndex.value + 1} 个文件，共 ${totalFiles.value} 个`)

    try {
      // 处理当前文件
      await sendVideoMessage(file)

      // 处理完成后，增加索引并处理下一个
      currentFileIndex.value++

      // 给UI一点时间更新
      setTimeout(() => {
        processNextFile(files)
      }, 300)
    } catch (error) {
      console.error('文件处理失败:', error)
      ElMessage.error(`第 ${currentFileIndex.value + 1} 个文件处理失败，将继续处理下一个文件`)

      // 处理失败也增加索引并继续
      currentFileIndex.value++

      // 继续处理下一个文件
      setTimeout(() => {
        processNextFile(files)
      }, 300)
    }
  } else {
    // 所有文件处理完成
    ElMessage.success(`所有文件上传完成，共 ${totalFiles.value} 个`)

    // 延迟重置状态，让用户看到最后一个文件的进度条达到100%
    setTimeout(() => {
      // 重置状态
      totalFiles.value = 0
      currentFileIndex.value = 0
      loading.value = false
      percentage.value = 0
    }, 1000)
  }
}

const sendVideoMessage = async (file: File) => {
  console.log('处理文件', file)
  const isVideo = file.type?.includes('video')
  const size = file.size
  if (size <= 0) {
    ElMessage.error('资源大小不能为0')
    return
  }
  if (size >= 20 * 1024 * 1024) {
    ElMessage.error('资源大小不能超过20M')
    return
  }
  loading.value = true
  _t.value && clearInterval(_t.value)
  percentage.value = 0
  _t.value = window.setInterval(() => {
    percentage.value += 1
    if (percentage.value >= 99) {
      percentage.value = 99
    }
    // 每秒250KB
  }, size / 25600)
  try {
    let processedFile = file
    let img
    if (!isVideo) {
      const base64 = await blobToBase64(file)
      img = await resizeBase64Img(base64, 1000, false)
      processedFile = base64ToFile(img.base64, file.name)
    }

    // 使用OSS客户端上传文件
    const url = await ossClient().upload(processedFile)
    console.log('url', url)

    if (url) {
      if (isVideo) {
        const name = 'cover.png'
        const { base64, width, height } = await getVideoBase64(url)
        console.log('getVideoBase64', base64, width, height)
        const coverFile = base64ToFile(base64, name)

        // 上传视频封面
        const coverurl = await ossClient().upload(coverFile)
        console.log('coverurl', coverurl)
        await sendCustomMessage({
          data: {
            businessID: 'ai_custom_msg',
            content: {
              name: 'video',
              data: [
                {
                  url,
                  cover: coverurl,
                  width,
                  height,
                },
              ],
            },
          },
        })
      } else {
        await sendCustomMessage({
          data: {
            businessID: 'ai_custom_msg',
            content: {
              name: 'image',
              data: [
                {
                  url,
                  width: img?.width,
                  height: img?.height,
                },
              ],
            },
          },
        })
      }
    }
  } catch (e) {
    console.log('e', e)
    percentage.value = 100
    _t.value && clearInterval(_t.value)
    // 不在这里重置 loading，由 processNextFile 统一管理
    throw e
  }
  percentage.value = 100
  _t.value && clearInterval(_t.value)
  // 不在这里重置 loading，由 processNextFile 统一管理
}
</script>
<style lang="scss" scoped>
.upload {
  width: 64px;
  height: 64px;
  position: relative;

  .progress {
    width: 64px;
    height: 64px;
    --el-fill-color-light: #e1e1e1;

    :deep(.el-progress-circle) {
      width: 64px !important;
      height: 64px !important;
    }
  }

  .loading-container {
    position: relative;
    width: 100%;
    height: 100%;

    .file-info {
      position: absolute;
      bottom: -20px;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 12px;
      color: #606266;
    }
  }

  img {
    width: 100%;
    height: 100%;
    transform: rotate(0deg);
    /* 悬停时旋转 45 度 */
    transition: transform 0.25s ease;
    /* 添加过渡效果 */
  }

  img.rotate {
    transform: rotate(45deg);
    /* 悬停时旋转 45 度 */
  }
}

.tool-container {
  opacity: 0;
  position: absolute;
  width: 100%;
}
</style>
