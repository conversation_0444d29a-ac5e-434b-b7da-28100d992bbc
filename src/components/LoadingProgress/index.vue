<!--
 * @Author: liu <NAME_EMAIL>
 * @Date: 2025-04-08 16:48:59
 * @LastEditors: liuleilei <EMAIL>
 * @LastEditTime: 2025-04-15 17:41:12
 * @FilePath: /miaobi-admin-magic-touch/src/components/IMImagePreview/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="loading-progress-box" v-if="isShow">
    <div class="progress-box">
      <h3>{{ title || '图片导入中' }}</h3>
      <h4>{{ Math.round(progressValue) }}%</h4>
      <div class="progress-bar">
        <div class="progress" :style="{ width: `${progressValue}%` }"></div>
      </div>
      <div class="cancel" @click="handleCancel">取消</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'

const emit = defineEmits(['ok', 'cancel'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  show: {
    type: Boolean,
    required: true,
  },
  time: {
    type: Number,
    default: 10,
  },
})
// // 清理进度条定时器
// if (progressTimer.value) {
//   clearInterval(progressTimer.value)
// }
// 添加进度条相关变量
const isShow = ref(false)
const progressValue = ref(0)
const progressTimer = ref<number | null>(null)

// 开始进度条
const startProgress = () => {
  isShow.value = true
  progressValue.value = 0
  // 清除已有的定时器
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }
  console.log('progressValue.value', progressValue.value)

  // 每100毫秒更新进度
  progressTimer.value = window.setInterval(() => {
    if (progressValue.value < 99) {
      // 非线性增长，开始快，后面慢
      const increment = (100 - progressValue.value) / (props.time * 10)
      progressValue.value += increment
    } else {
      // 到达99%后停止增长，等待实际完成
      clearInterval(progressTimer.value as number)
    }
  }, 100)
}

// 完成进度条
const completeProgress = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }
  progressValue.value = 100
  setTimeout(() => {
    isShow.value = false
    emit('ok')
  }, 300)
}

watch(
  () => props.show,
  newVal => {
    console.log('newVal', newVal)
    if (newVal) {
      startProgress()
    } else {
      completeProgress()
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

const handleCancel = () => {
  emit('cancel')
}

defineExpose({})
</script>

<style lang="scss" scoped>
.loading-progress-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  .progress-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 400px;
    height: 205px;
    background: #ffffff;
    border-radius: 15px;
    h3 {
      font-size: 20px;
      color: #313131;
      margin: 20px 0 30px;
    }
    h4 {
      font-weight: 600;
      font-size: 20px;
      color: #2266ff;
      margin: 0;
      margin-bottom: 14px;
    }
    .progress-bar {
      width: 171px;
      height: 12px;
      background: #ccc;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 30px;
      .progress {
        height: 100%;
        background-color: #409eff;
        border-radius: 20px;
        transition: width 0.3s ease;
      }
    }
    .cancel {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 140px;
      height: 35px;
      background: #2266ff;
      border-radius: 18px;
      color: #fff;
      cursor: pointer;
      &:hover {
        opacity: 0.9;
      }
    }
  }
}
</style>
