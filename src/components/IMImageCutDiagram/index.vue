<template>
  <div class="image-preview">
    <LoadingProgress title="导入图片中" :show="showProgress" :time="18" @ok="handleOkProgress" @cancel="handleCancelProgress" />

    <div class="image-preview-top">
      <div class="image-preview-back">
        <div class="image-preview-title" @click="handleBack()">
          <img :src="back" width="20px" height="20px" alt="" />
          AI抠图
        </div>
        <div class="image-preview-tips" v-if="showMainCanvas">点击或描出想要抠出的图像边缘，必须为封闭区域</div>
        <div class="image-preview-tips" v-else>抠图编辑，可以使用橡皮擦工具</div>
      </div>

      <div class="image-preview-btn">
        <div v-if="showMainCanvas" :class="selectedImages.length ? 'ai-btn' : 'ai-btn disable'" key="ai-draw" @click="handleMerge">
          <img :src="cutouts" width="20px" height="14px" alt="" />
          <span>抠图</span>
        </div>

        <div v-if="!showMainCanvas" class="ai-btn" key="ai-reset" @click="handleReset">
          <img :src="reset" width="14px" height="14px" alt="" />
          <span>重置</span>
        </div>
        <div v-if="!showMainCanvas" class="ai-btn" key="ai-save" @click="handleSave">
          <img :src="save" width="14px" height="14px" alt="" />
          <span>保存</span>
        </div>
      </div>
    </div>

    <div class="canvas-container" ref="canvasBoxRef" :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
      <img v-if="showBgImageSrc" class="image-preview-img" :src="showBgImageSrc" />
      <div class="canvas-loading" v-if="imageLoading">
        <img :src="imageLoadingImg" alt="" />
      </div>

      <!-- 使用独立组件替代内联canvas -->
      <SelectCanvas
        v-if="showMainCanvas"
        ref="selectCanvasRef"
        :width="canvasWidth"
        :height="canvasHeight"
        :image-list="imageList"
        :mask-images="maskImages"
        :selected-images="selectedImages"
        :ratio-info="ratioInfo"
        @update:selected-images="selectedImages = $event"
        @save-selection="saveSelectionForUndo"
      />

      <EraserCanvas v-else ref="eraserCanvasRef" :width="canvasWidth" :height="canvasHeight" :image-url="mergedImageUrl" :eraser-size="eraserSize" @save="handleCanvasSave" />
    </div>

    <!-- 橡皮擦工具栏 -->
    <div class="canvas-tools">
      <div class="tools-box">
        <div class="tool left" @click="handleUndo" :class="{ active: showMainCanvas ? canUndoSelection : eraserCanvasRef && eraserCanvasRef.canUndo() }"></div>
        <div class="tool right" @click="handleRedo" :class="{ active: showMainCanvas ? canRedoSelection : eraserCanvasRef && eraserCanvasRef.canRedo() }"></div>
      </div>
      <div class="eraser-box" v-if="!showMainCanvas">
        <div class="eraser-size">
          <span>大小:</span>
          <input type="range" min="5" max="100" v-model="eraserSize" />
          <span>{{ eraserSize }}px</span>
        </div>

        <div class="eraser-cursor">
          <div :style="{ width: eraserSize + 'px', height: eraserSize + 'px' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose, onBeforeUnmount } from 'vue'
import save from '@/assets/images/ChatAiTool/save-image.png'
import back from '@/assets/images/ChatAiTool/back_h.png'
import cutouts from '@/assets/images/ChatAiTool/cutouts.png'
import reset from '@/assets/images/ChatAiTool/reset.png'
import imageLoadingImg from '@/assets/images/ChatAiTool/image-loading.gif'
import axios from 'axios'

// 导入组件
import SelectCanvas from './SelectCanvas.vue'
import EraserCanvas from './EraserCanvas.vue'
// @ts-ignore 忽略导入错误
import LoadingProgress from '../LoadingProgress/index.vue'
import { getImageCurDiagram, saveMergeDiagram } from '@/api/aiChat/index'
import { ElMessage } from 'element-plus'

import { isValidUrl } from '@/utils/validate'
import useChatStore from '@/store/modules/chat'

interface ImageItem {
  id: string
  x: number
  y: number
  width: number
  height: number
  src: string
  contours?: any[]
}

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  fit: {
    type: String,
    default: 'cover',
  },
})
const emit = defineEmits(['draw', 'save', 'close'])

const imageRef = ref<HTMLImageElement>()
const canvasBoxRef = ref<HTMLElement | null>(null)
const selectCanvasRef = ref<InstanceType<typeof SelectCanvas> | null>(null)
const eraserCanvasRef = ref<InstanceType<typeof EraserCanvas> | null>(null)
const canvasWidth = ref(document.body.clientWidth)
const canvasHeight = ref(document.body.clientWidth) // 设置画布高度
const selectedImages = ref<ImageItem[]>([])
const imageLoading = ref(false)
const showMainCanvas = ref(true)
const showBgImageSrc = ref('')

// 橡皮擦相关状态
const eraserSize = ref(30)

// JSON 文件
const splitImgJSON = ref<string>('')
const mergedImageUrl = ref('')
const ratioInfo = ref({
  x: 0,
  y: 0,
})

// 存储图片信息和位置
const imageList = ref<ImageItem[]>([])
const maskImages = ref(new Map<string, HTMLImageElement>())

// 选择图片历史记录
const selectedImagesHistory = ref<ImageItem[][]>([])
const selectedImagesRedoHistory = ref<ImageItem[][]>([])
const canUndoSelection = ref(false)
const canRedoSelection = ref(false)

const showProgress = ref(true)

const chatStore = useChatStore()

/**
 * 加载遮罩图片
 */
const loadMaskImage = async (id: string, imagePath: string) => {
  if (maskImages.value.has(id)) {
    return maskImages.value.get(id)
  }

  return new Promise<HTMLImageElement>(resolve => {
    const img = new Image()
    img.crossOrigin = 'Anonymous'
    img.src = imagePath
    img.onload = () => {
      maskImages.value.set(id, img)
      resolve(img)
    }
  })
}

const getImageSplit = async (imageUrl: string) => {
  // 开始显示进度条
  showProgress.value = true
  initCanvas(imageUrl)
  const { data } = await getImageCurDiagram({ imageUrl })
  if (typeof data === 'string' || !isValidUrl(data?.url)) {
    showProgress.value = false
    imageLoading.value = false
    ElMessage.error('无法读取数据')
    return
  }

  const { url, image_url } = data
  await initCanvas(image_url)
  splitImgJSON.value = url
  // 获取JSON数据
  axios.get(url).then(result => {
    // 完成进度条
    showProgress.value = false
    console.log('🚀 ~ axios.get ~ json:', result)
    // const sortArr = result?.data?.sort((a: any, b: any) => a.area + b.area)

    // 处理图片信息
    imageList.value = result?.data?.map((item: any) => {
      const { bbox, id, image_path, contours } = item
      const [x, y, width, height] = bbox ?? []
      const { x: ratioX, y: ratioY } = ratioInfo.value

      const data: ImageItem = {
        x: x / ratioX,
        y: y / ratioY,
        width: width / ratioX,
        height: height / ratioY,
        id,
        contours: contours ? contours : [],
        src: splitImgJSON.value.replace('masks_info.json', image_path),
      }
      return data
    })

    // 预加载所有图片
    Promise.all(imageList.value.map(item => loadMaskImage(item.id, item.src))).then(() => {
      if (selectCanvasRef.value) {
        selectCanvasRef.value.drawAllMasks()
      }
    })
  })
}

const initCanvas = (imgUrl: string) => {
  const image = new Image()
  image.crossOrigin = 'Anonymous'
  image.src = imgUrl
  return new Promise<boolean>(resolve => {
    image.onload = () => {
      imageRef.value = image
      if (canvasBoxRef.value) {
        const { clientWidth, clientHeight } = canvasBoxRef.value
        // 根据图片宽高比和容器大小设置canvas尺寸
        const imgRatio = image.width / image.height
        const containerRatio = clientWidth / clientHeight
        if (imgRatio > containerRatio) {
          canvasWidth.value = clientWidth
          canvasHeight.value = clientWidth / imgRatio
        } else {
          canvasHeight.value = clientHeight
          canvasWidth.value = clientHeight * imgRatio
        }
        ratioInfo.value.x = image.width / canvasWidth.value
        ratioInfo.value.y = image.height / canvasHeight.value
        // canvasBoxRef.value!.style.backgroundImage = `url(${imgUrl})`
        showBgImageSrc.value = imgUrl
        resolve(true)
      }
    }
  })
}

// 保存选择状态用于撤销功能
const saveSelectionForUndo = () => {
  // 清空重做历史
  selectedImagesRedoHistory.value = []

  // 复制当前选择状态
  const currentSelection = [...selectedImages.value]

  // 添加到历史记录
  selectedImagesHistory.value.push(currentSelection)

  // 如果历史记录过长，则删除最早的记录
  if (selectedImagesHistory.value.length > 50) {
    selectedImagesHistory.value.shift()
  }

  // 更新撤销状态
  canUndoSelection.value = selectedImagesHistory.value.length > 0
  canRedoSelection.value = selectedImagesRedoHistory.value.length > 0
}

// 撤销功能
const handleUndo = () => {
  if (showMainCanvas.value) {
    // 处理画布选择撤销
    if (selectedImagesHistory.value.length > 0) {
      // 保存当前状态到重做历史
      selectedImagesRedoHistory.value.push([...selectedImages.value])

      // 恢复上一个状态
      const previousState = selectedImagesHistory.value.pop()
      if (previousState) {
        selectedImages.value = [...previousState]
      }

      // 更新状态
      canUndoSelection.value = selectedImagesHistory.value.length > 0
      canRedoSelection.value = true
    }
  } else {
    // 处理橡皮擦撤销
    if (eraserCanvasRef.value) {
      eraserCanvasRef.value.handleUndo()
    }
  }
}

// 重做功能
const handleRedo = () => {
  if (showMainCanvas.value) {
    // 处理画布选择重做
    if (selectedImagesRedoHistory.value.length > 0) {
      // 保存当前状态到撤销历史
      selectedImagesHistory.value.push([...selectedImages.value])

      // 恢复下一个状态
      const nextState = selectedImagesRedoHistory.value.pop()
      if (nextState) {
        selectedImages.value = [...nextState]
      }

      // 更新状态
      canUndoSelection.value = true
      canRedoSelection.value = selectedImagesRedoHistory.value.length > 0
    }
  } else {
    // 处理橡皮擦重做
    if (eraserCanvasRef.value) {
      eraserCanvasRef.value.handleRedo()
    }
  }
}

// 修改 handleMerge 方法，将返回的 URL 加载到 eraserCanvasRef
const handleMerge = () => {
  if (!selectedImages.value.length) {
    return
  }
  // 开始进度条
  showProgress.value = true
  const ids = selectedImages.value.map(item => item.id)

  saveMergeDiagram({
    jsonFile: splitImgJSON.value,
    ids,
  })
    .then((res: any) => {
      const { url } = res?.data
      if (!isValidUrl(url)) {
        showProgress.value = false

        ElMessage.error('无法读取数据')
        return
      }

      console.log('合并图片成功，URL:', url)

      // 切换到橡皮擦模式
      showMainCanvas.value = false
      // showBgImageSrc.value = ''
      mergedImageUrl.value = url

      // 完成进度条
      showProgress.value = false
    })
    .catch((error: any) => {
      console.error('合并图片失败:', error)
      // 出错时结束进度条
      showProgress.value = false
    })
}

// 处理从EraserCanvas传过来的保存事件
const handleCanvasSave = (file: File) => {
  emit('save', file)
  closeDialog()
}
const handleBack = () => {
  if (showMainCanvas.value) {
    closeDialog()
  } else {
    showMainCanvas.value = true
    // showBgImageSrc.value = props.src
  }
}

// 修改 handleSave 方法，保存橡皮擦后的图片
const handleSave = () => {
  if (eraserCanvasRef.value) {
    eraserCanvasRef.value.saveImage()
  }
}

const closeDialog = () => {
  chatStore.setAiCutoutsShow(false)
  chatStore.setAiCutoutsSrc('')
  emit('close')
}

const handleOkProgress = () => {
  if (showMainCanvas.value) {
    imageLoading.value = true
  }
  setTimeout(() => {
    imageLoading.value = false
  }, 4100)
}

// 取消进度条处理
const handleCancelProgress = () => {
  showProgress.value = false
  // 取消正在进行的请求，这里可以添加取消API请求的逻辑
  // ...

  closeDialog()
}

// 监听窗口大小变化，调整canvas尺寸
const handleResize = () => {
  if (imageRef.value && canvasBoxRef.value) {
    const { clientWidth, clientHeight } = canvasBoxRef.value

    // 根据图片宽高比和容器大小设置canvas尺寸
    const imgRatio = imageRef.value.width / imageRef.value.height
    const containerRatio = clientWidth / clientHeight

    if (imgRatio > containerRatio) {
      canvasWidth.value = clientWidth
      canvasHeight.value = clientWidth / imgRatio
    } else {
      canvasHeight.value = clientHeight
      canvasWidth.value = clientHeight * imgRatio
    }

    ratioInfo.value.x = imageRef.value.width / canvasWidth.value
    ratioInfo.value.y = imageRef.value.height / canvasHeight.value
  }
}

// 添加处理重置的方法
const handleReset = () => {
  if (eraserCanvasRef.value) {
    eraserCanvasRef.value.resetCanvas()
  }
}

onMounted(() => {
  // 获取图片分割数据
  getImageSplit(props.src)

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)

  // 调试信息
  console.log('IMImageCutDiagram组件已挂载', {
    src: props.src,
    canvasWidth: canvasWidth.value,
    canvasHeight: canvasHeight.value,
  })
})

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  closeDialog,
})
</script>

<style lang="scss" scoped>
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  .image-preview-top {
    position: absolute;
    top: 0;
    left: 0px;
    width: 100%;
    height: 75px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    font-weight: normal;
    font-size: 14px;
    color: #000000;
    padding: 0 23px;
  }
  .image-preview-back {
    display: flex;
    align-items: center;
    .image-preview-title {
      display: flex;
      align-items: center;
      margin-right: 8px;
      font-size: 18px;
      img {
        margin-right: 18px;
      }
    }
    .image-preview-tips {
      font-size: 16px;
      color: #7d7d7d;
      font-weight: 500;
    }
  }

  .image-preview-btn {
    display: flex;
    align-items: center;

    .ai-btn {
      width: 87px;
      height: 32px;
      background: #2967f3;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 10px;
      color: #fff;
      & + .ai-btn {
        margin-left: 25px;
      }
      &.disable {
        background: #898989;
      }
      img {
        display: block;
        margin-right: 16px;
      }
    }
  }
}
.canvas-loading {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 888;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.canvas-container {
  position: relative;
  background-size: cover;
  font-size: 0;

  .image-preview-img {
    margin-top: 1px;
    width: calc(100% - 1px);
    height: calc(100% - 2px);
    object-fit: cover;
  }
}
.canvas-tools {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  width: 100%;
  height: 150px;
  flex-direction: column;
  .tools-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 150px;
    height: 50px;
    .tool {
      width: 45px;
      height: 45px;
      background: #898989;
      border-radius: 50%;
      background-size: 20px;
      background-position: center;
      background-repeat: no-repeat;
      /* // &:active {
      //   background-color: #2967f3;
      // } */
      &.left {
        background-image: url('@/assets/images/ChatAiTool/ai-left.png');
      }
      &.right {
        background-image: url('@/assets/images/ChatAiTool/ai-right.png');
      }
      &.active {
        background-color: #2967f3;
      }
    }
  }
  .eraser-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 50px;
    .eraser-size {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 400px;
      height: 50px;
      color: #000;
      span {
        width: 40px;
        text-align: center;
      }
      input[type='range'] {
        -webkit-appearance: none;
        width: 250px;
        height: 3px;
        outline: none;
        /* background: #fff; */
        background: #898989;
        margin: 0 20px;
      }
      input[type='range']::-webkit-slider-thumb {
        -webkit-appearance: none;
        position: relative;
        width: 25px;
        height: 25px;
        background: #2266ff;
        border-radius: 50%;
        transition: 0.2s;
        border: 2px solid #898989;
      }
    }
    .eraser-cursor {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100px;
      height: 100px;
      div {
        border-radius: 50%;
        border: 1px dashed #000;
      }
    }
  }
}
</style>
