<template>
  <canvas ref="mainCanvasRef" :width="width" :height="height"></canvas>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineProps, defineEmits, nextTick } from 'vue'
import { isPC } from '@/utils/device'
interface ImageItem {
  id: string
  x: number
  y: number
  width: number
  height: number
  src: string
  contours?: any[]
}

interface Point {
  x: number
  y: number
}

const props = defineProps({
  width: {
    type: Number,
    required: true,
  },
  height: {
    type: Number,
    required: true,
  },
  imageList: {
    type: Array as () => ImageItem[],
    required: true,
  },
  maskImages: {
    type: Object as () => Map<string, HTMLImageElement>,
    required: true,
  },
  selectedImages: {
    type: Array as () => ImageItem[],
    required: true,
  },
  ratioInfo: {
    type: Object as () => { x: number; y: number },
    required: true,
  },
})

const emit = defineEmits(['update:selectedImages', 'saveSelection'])

const mainCanvasRef = ref<HTMLCanvasElement | null>(null)
const isDrawing = ref(false)
const drawPoints = ref<Point[]>([])
const pathClosed = ref(false)
const startPoint = ref<Point | null>(null)
const minDistanceToClose = 30 // 闭合路径的最小距离（像素）
const selectionMode = ref<'touch' | 'draw'>('touch') // 选择模式：触摸点击或绘制框选

// 降低拖动触发阈值，提高灵敏度
const moveDistanceThreshold = 5 // 从10像素降低到5像素

// 修改触摸操作相关逻辑
const handleCanvasTouch = (event: TouchEvent | MouseEvent) => {
  event.preventDefault()

  const canvas = mainCanvasRef.value
  if (!canvas) return

  const touch = isPC ? event : event.touches[0]
  if (!touch) return

  // 获取触摸坐标
  const rect = canvas.getBoundingClientRect()
  const point = {
    x: Math.floor(touch.clientX - rect.left),
    y: Math.floor(touch.clientY - rect.top),
  }

  // 记录触摸开始时间和位置，用于区分点击和拖动
  const touchStartTime = new Date().getTime()
  const touchStartPosition = { ...point }

  // 标记为可能的点击操作
  let isPossibleClick = true

  // 立即开始新的绘制路径
  isDrawing.value = true
  pathClosed.value = false
  drawPoints.value = []
  startPoint.value = { ...point }
  drawPoints.value.push({ ...point })

  // 创建一个一次性的触摸移动监听器，用于确定是拖动还是点击
  const initialMoveHandler = (moveEvent: TouchEvent) => {
    moveEvent.preventDefault()

    const moveTouch = isPC ? moveEvent : moveEvent.touches[0]
    if (!moveTouch) return

    const movePoint = {
      x: Math.floor(moveTouch.clientX - rect.left),
      y: Math.floor(moveTouch.clientY - rect.top),
    }

    // 计算移动距离
    const moveDistance = Math.sqrt(Math.pow(movePoint.x - touchStartPosition.x, 2) + Math.pow(movePoint.y - touchStartPosition.y, 2))

    // 如果移动距离超过阈值，认为是拖动操作（降低阈值提高灵敏度）
    if (moveDistance > moveDistanceThreshold) {
      isPossibleClick = false
      selectionMode.value = 'draw'

      // 移除临时监听器，由正常的touchmove处理
      canvas.removeEventListener(isPC ? 'mousemove' : 'touchmove', initialMoveHandler as any)
    }
  }

  // 添加临时监听器，立即捕获任何移动
  canvas.addEventListener(isPC ? 'mousemove' : 'touchmove', initialMoveHandler as any, { passive: false, once: true })

  // 添加结束监听
  const endHandler = (endEvent: TouchEvent) => {
    endEvent.preventDefault()

    // 移除临时监听器
    canvas.removeEventListener(isPC ? 'mousemove' : 'touchmove', initialMoveHandler as any)
    canvas.removeEventListener(isPC ? 'mouseup' : 'touchend', endHandler as any)

    // 获取触摸结束时间
    const touchEndTime = new Date().getTime()
    const touchDuration = touchEndTime - touchStartTime

    // 如果仍然可能是点击且持续时间短，处理为点击操作
    if (isPossibleClick && touchDuration < 250) {
      // 从300ms降到250ms，提高响应速度
      // 重置绘制状态
      isDrawing.value = false
      drawPoints.value = []

      // 查找触摸的图片
      const clickedImage = findTopmostHitMask(touchStartPosition)

      if (clickedImage) {
        // 保存历史以便撤销
        emit('saveSelection')

        // 更新选中状态
        const newSelectedImages = [...props.selectedImages]
        const index = newSelectedImages.findIndex(item => item.id === clickedImage.id)

        if (index === -1) {
          newSelectedImages.push(clickedImage)
        } else {
          newSelectedImages.splice(index, 1)
        }

        emit('update:selectedImages', newSelectedImages)

        // 重绘画布
        drawAllMasks()
      }
    }
  }

  // 添加触摸结束监听
  canvas.addEventListener(isPC ? 'mouseup' : 'touchend', endHandler as any, { passive: false, once: true })

  // 初始绘制
  drawAllMasks(true)
}

// 修改触摸移动处理逻辑，增加灵敏度
const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault()

  if (!isDrawing.value) return

  // 如果用户开始移动，自动切换到绘制模式
  selectionMode.value = 'draw'

  const canvas = mainCanvasRef.value
  if (!canvas) return

  const touch = isPC ? event : event.touches[0]
  if (!touch) return

  // 获取触摸坐标
  const rect = canvas.getBoundingClientRect()
  const point = {
    x: Math.floor(touch.clientX - rect.left),
    y: Math.floor(touch.clientY - rect.top),
  }

  // 添加点到路径，增加取样频率
  const lastPoint = drawPoints.value.length > 0 ? drawPoints.value[drawPoints.value.length - 1] : null
  if (!lastPoint || Math.abs(point.x - lastPoint.x) > 1 || Math.abs(point.y - lastPoint.y) > 1) {
    drawPoints.value.push({ ...point })
  }

  // 检查是否接近起点，如果是则闭合路径
  if (startPoint.value && drawPoints.value.length > 5) {
    // 从10减少到5个点，更快判定闭合
    const distance = Math.sqrt(Math.pow(point.x - startPoint.value.x, 2) + Math.pow(point.y - startPoint.value.y, 2))

    pathClosed.value = distance < minDistanceToClose
  }

  // 重绘画布
  drawAllMasks(true)
}

// 修改触摸结束处理逻辑，让更短的路径也能被识别
const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()

  if (!isDrawing.value) return

  // // *** 1. 如果是绘制模式且有足够的点，尝试完成框选 //不需要闭合 也可选择
  // if (drawPoints.value.length > 3 && startPoint.value) {
  //   // 从3个点降低到2个点
  //   // 如果路径足够长但未闭合，强制闭合
  //   if (!pathClosed.value && drawPoints.value.length > 10) {
  //     // 从10个点降低到5个点
  //     drawPoints.value.push({ ...startPoint.value })
  //   }
  //   // 确定路径内的图像
  //   selectImagesInPath()
  // }

  // 2.如果路径闭合，则确定路径内的图像 ***
  if (pathClosed.value) {
    selectImagesInPath()
  }

  // 重置绘制状态
  isDrawing.value = false
  selectionMode.value = 'touch'
  drawPoints.value = []
  pathClosed.value = false
  startPoint.value = null

  // 重绘画布
  nextTick(() => {
    drawAllMasks()
  })
}

// 检查点是否在多边形内部
const isPointInPolygon = (point: Point, polygon: Point[]): boolean => {
  if (polygon.length < 3) return false

  let inside = false
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x
    const yi = polygon[i].y
    const xj = polygon[j].x
    const yj = polygon[j].y

    const intersect = yi > point.y !== yj > point.y && point.x < ((xj - xi) * (point.y - yi)) / (yj - yi) + xi

    if (intersect) inside = !inside
  }

  return inside
}

// 判断图像是否在路径内部
const isImageInPath = (item: ImageItem, path: Point[]): boolean => {
  // 检查图像的四个角点是否在路径内
  const corners = [
    { x: item.x, y: item.y }, // 左上
    { x: item.x + item.width, y: item.y }, // 右上
    { x: item.x, y: item.y + item.height }, // 左下
    { x: item.x + item.width, y: item.y + item.height }, // 右下
  ]

  // 如果图像的中心点在路径内，也认为图像在路径内
  const center = {
    x: item.x + item.width / 2,
    y: item.y + item.height / 2,
  }

  // 检查中心点是否在路径内，或者至少有两个角在路径内
  const centerInPath = isPointInPolygon(center, path)

  if (centerInPath) return true

  // 计算在路径内的角点数量
  const cornersInPath = corners.filter(corner => isPointInPolygon(corner, path)).length

  return cornersInPath >= 2 // 至少两个角在路径内认为图像被选中
}

// 选择路径内的所有图像
const selectImagesInPath = () => {
  if (drawPoints.value.length < 3) return

  // 保存历史以便撤销
  emit('saveSelection')

  // 找出路径内的所有图像
  const imagesInPath = props.imageList.filter(item => isImageInPath(item, drawPoints.value))

  if (imagesInPath.length === 0) return

  // 更新选中状态
  const newSelectedImages = [...props.selectedImages]

  // 对于路径内的每个图像，如果未选中则添加到选中列表
  imagesInPath.forEach(image => {
    if (!newSelectedImages.some(selected => selected.id === image.id)) {
      newSelectedImages.push(image)
    }
  })

  // 更新选中状态
  emit('update:selectedImages', newSelectedImages)
}

// 查找最上层的命中遮罩
const findTopmostHitMask = (point: { x: number; y: number }) => {
  // 从后向前遍历，找到第一个命中的遮罩
  for (let i = props.imageList.length - 1; i >= 0; i--) {
    const item = props.imageList[i]

    // 检查点是否在边界框内
    const isInBox = point.x >= item.x && point.x <= item.x + item.width && point.y >= item.y && point.y <= item.y + item.height

    if (isInBox) {
      // 获取图片
      const img = props.maskImages.get(item.id)
      if (img) {
        // 创建临时画布检查点击位置是否透明
        const tempCanvas = document.createElement('canvas')
        tempCanvas.width = item.width
        tempCanvas.height = item.height

        const tempCtx = tempCanvas.getContext('2d')
        if (tempCtx) {
          tempCtx.drawImage(img, 0, 0, item.width, item.height)

          // 获取相对于item的坐标
          const relX = point.x - item.x
          const relY = point.y - item.y

          // 检查像素是否透明
          const pixelData = tempCtx.getImageData(relX, relY, 1, 1).data
          if (pixelData[3] > 0) {
            return item
          }
        }
      }
    }
  }

  return null
}

// 绘制框选路径
const drawSelectionPath = (ctx: CanvasRenderingContext2D) => {
  if (drawPoints.value.length < 2) return

  ctx.save()

  // 设置路径样式
  ctx.strokeStyle = pathClosed.value ? '#22c55e' : '#3b82f6'
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  // 绘制路径
  ctx.beginPath()
  ctx.moveTo(drawPoints.value[0].x, drawPoints.value[0].y)

  for (let i = 1; i < drawPoints.value.length; i++) {
    ctx.lineTo(drawPoints.value[i].x, drawPoints.value[i].y)
  }

  // 如果路径闭合，则填充半透明颜色
  if (pathClosed.value) {
    ctx.closePath()
    ctx.fillStyle = 'rgba(34, 197, 94, 0.2)'
    ctx.fill()
  }

  ctx.stroke()

  // 去除虚线
  // // 如果用户绘制了足够的点但未闭合，显示提示线段
  // if (!pathClosed.value && drawPoints.value.length > 10 && startPoint.value) {
  //   const lastPoint = drawPoints.value[drawPoints.value.length - 1]

  //   ctx.beginPath()
  //   ctx.setLineDash([5, 5]) // 虚线
  //   ctx.moveTo(lastPoint.x, lastPoint.y)
  //   ctx.lineTo(startPoint.value.x, startPoint.value.y)
  //   ctx.stroke()
  //   ctx.setLineDash([]) // 重置虚线

  //   // 绘制起点标记
  //   ctx.beginPath()
  //   ctx.arc(startPoint.value.x, startPoint.value.y, 8, 0, Math.PI * 2)
  //   ctx.fillStyle = '#3b82f6'
  //   ctx.fill()
  // }

  // ctx.restore()
}

// 绘制所有遮罩
const drawAllMasks = (keepDrawing = false) => {
  const canvas = mainCanvasRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, props.width, props.height)

  // 先绘制所有掩码
  props.imageList.forEach(item => {
    const img = props.maskImages.get(item.id)
    if (img) {
      ctx.drawImage(img, item.x, item.y, item.width, item.height)
    }
  })

  // 绘制选中的图片
  if (props.selectedImages.length > 0) {
    // 绘制半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, canvas.width, canvas.height + 10) // TODO: 灰色画布 多加点高度 有白边

    // 绘制选中的掩码
    props.selectedImages.forEach(item => {
      const img = props.maskImages.get(item.id)
      if (img) {
        ctx.drawImage(img, item.x, item.y, item.width, item.height)

        // 绘制轮廓线
        drawContours(ctx, item)
      }
    })
  }

  // 如果处于绘制模式，绘制选择路径
  if (keepDrawing && isDrawing.value && drawPoints.value.length > 0) {
    drawSelectionPath(ctx)
  }
}

// 绘制轮廓线
const drawContours = (ctx: CanvasRenderingContext2D, item: ImageItem) => {
  if (!item.contours || !item.contours.length) {
    console.log('无轮廓数据:', item.id)
    return
  }

  // 保存当前状态
  ctx.save()

  ctx.strokeStyle = 'RGBA(34, 75, 173, .5)'
  ctx.shadowColor = 'RGBA(34, 75, 173, .1)'
  ctx.shadowBlur = 5 // 阴影模糊程度
  ctx.lineWidth = 8

  // 获取原始坐标与画布坐标的比例
  const { x: ratioX, y: ratioY } = props.ratioInfo

  try {
    if (item.contours && item.contours.length > 0) {
      // 遍历所有轮廓
      item.contours.forEach(contour => {
        if (contour.length > 0) {
          // 开始绘制路径
          ctx.beginPath()

          // 移动到轮廓的第一个点
          const firstPoint = contour[0][0]
          ctx.moveTo(firstPoint[0] / ratioX, firstPoint[1] / ratioY)

          // 绘制轮廓线
          for (let i = 1; i < contour.length; i++) {
            const point = contour[i][0]
            ctx.lineTo(point[0] / ratioX, point[1] / ratioY)
          }

          // 闭合路径
          ctx.closePath()

          // 绘制轮廓
          ctx.stroke()
        }
      })
    }
  } catch (error) {
    console.error('绘制轮廓线出错:', error, item.contours)
  }

  // 恢复绘图状态
  ctx.restore()
}

// 监听图片列表、选中图片变化
watch(
  [() => props.imageList, () => props.selectedImages, () => props.maskImages],
  () => {
    drawAllMasks()
  },
  { deep: true }
)

// 监听画布尺寸变化
watch([() => props.width, () => props.height], () => {
  // 重置绘制状态
  isDrawing.value = false
  drawPoints.value = []
  pathClosed.value = false
  startPoint.value = null

  // 重绘画布
  nextTick(() => {
    drawAllMasks()
  })
})

onMounted(() => {
  // 初始化绘制
  const canvas = mainCanvasRef.value
  console.log('🚀 ~ onMounted ~ canvas:', canvas, isPC)
  // @touchstart="handleCanvasTouch" @touchmove="handleTouchMove" @touchend="handleTouchEnd"
  if (canvas) {
    canvas?.addEventListener(isPC ? 'mousedown' : 'touchstart', handleCanvasTouch as any)
    canvas?.addEventListener(isPC ? 'mousemove' : 'touchmove', handleTouchMove as any)
    canvas?.addEventListener(isPC ? 'mouseup' : 'touchend', handleTouchEnd as any)
  }

  drawAllMasks()
})

onUnmounted(() => {
  const canvas = mainCanvasRef.value
  if (canvas) {
    canvas?.removeEventListener(isPC ? 'mousedown' : 'touchstart', handleCanvasTouch as any)
    canvas?.removeEventListener(isPC ? 'mousemove' : 'touchmove', handleTouchMove as any)
    canvas?.removeEventListener(isPC ? 'mouseup' : 'touchend', handleTouchEnd as any)
  }
})

defineExpose({
  drawAllMasks,
})
</script>

<style scoped>
canvas {
  position: absolute;
  top: 0;
  left: 0;
  touch-action: none; /* 防止浏览器默认的触摸行为干扰绘制 */
}
</style>
