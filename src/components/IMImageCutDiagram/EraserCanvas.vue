<template>
  <canvas ref="eraserCanvasRef" :width="width" :height="height" style="background-color: #fff"></canvas>

  <div v-if="showCursor" class="cursor" :style="{ left: cursorPosition.x + 'px', top: cursorPosition.y + 'px', width: eraserSize + 'px', height: eraserSize + 'px' }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { isPC } from '@/utils/device'
// 橡皮擦历史记录接口
interface EraserAction {
  x: number
  y: number
  size: number
  imageData: ImageData
}

const props = defineProps({
  width: {
    type: Number,
    required: true,
  },
  height: {
    type: Number,
    required: true,
  },
  imageUrl: {
    type: String,
    default: '',
  },
  eraserSize: {
    type: Number,
    default: 25,
  },
})

const emit = defineEmits(['save'])

const eraserCanvasRef = ref<HTMLCanvasElement | null>(null)
const isErasing = ref(false)
const showCursor = ref(false)
const cursorPosition = ref({ x: -51, y: -50 })
const lastPoint = ref<{ x: number; y: number } | null>(null)
const imageLoading = ref(false)
const eraserHistory = ref<EraserAction[]>([])
const eraserRedoHistory = ref<EraserAction[]>([])
const initialImageData = ref<ImageData | null>(null)

// 初始化橡皮擦功能
const initEraser = () => {
  console.log('初始化橡皮擦功能')
  if (!eraserCanvasRef.value) {
    console.error('eraserCanvasRef不存在')
    return
  }

  // 触摸事件已通过Vue的@事件绑定，不需要在这里添加

  // 立即显示自定义光标
  showCursor.value = true

  console.log('橡皮擦功能初始化完成')
}

// 处理触摸移动
const handleTouchMove = (event: TouchEvent | MouseEvent) => {
  event.preventDefault() // 阻止默认行为，防止滚动

  if (!eraserCanvasRef.value) return

  const touch = isPC ? event : event.touches[0]
  if (!touch) return

  const rect = eraserCanvasRef.value.getBoundingClientRect()

  // 更新光标位置
  cursorPosition.value = {
    x: touch.clientX - rect.left - props.eraserSize / 2,
    y: touch.clientY - rect.top - props.eraserSize / 2,
  }

  // 如果正在擦除，则执行擦除操作
  if (isErasing.value) {
    const x = touch.clientX - rect.left
    const y = touch.clientY - rect.top

    // 使用线条连接上一个点和当前点，以实现平滑的擦除效果
    if (lastPoint.value) {
      eraseLineBetweenPoints(lastPoint.value, { x, y })
    } else {
      eraseAt(x, y)
    }

    lastPoint.value = { x, y }
  }
}

// 开始擦除
const handleTouchStart = (event: MouseEvent | TouchEvent) => {
  event.preventDefault()

  if (!eraserCanvasRef.value) return

  const touch = isPC ? event : event.touches[0]
  if (!touch) return

  // 在开始绘制前保存整个画布状态
  saveStateForUndo()

  isErasing.value = true
  const rect = eraserCanvasRef.value.getBoundingClientRect()
  const x = touch.clientX - rect.left
  const y = touch.clientY - rect.top

  // 执行擦除
  eraseAt(x, y)
  lastPoint.value = { x, y }

  // 为移动端显示光标
  cursorPosition.value = {
    x: x - props.eraserSize / 2,
    y: y - props.eraserSize / 2,
  }
}

// 结束擦除
const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()
  isErasing.value = false
  lastPoint.value = null
}

// 触摸取消
const handleTouchCancel = (event: TouchEvent) => {
  event.preventDefault()
  isErasing.value = false
  lastPoint.value = null
}

// 保存当前状态用于撤销功能
const saveStateForUndo = () => {
  if (!eraserCanvasRef.value) return

  const ctx = eraserCanvasRef.value.getContext('2d')
  if (!ctx) return

  // 清空重做历史
  eraserRedoHistory.value = []

  // 保存整个画布的状态，而不仅仅是局部区域
  const imageData = ctx.getImageData(0, 0, eraserCanvasRef.value.width, eraserCanvasRef.value.height)

  // 添加到历史记录
  eraserHistory.value.push({
    x: 0,
    y: 0,
    size: 0, // 不再需要大小参数
    imageData,
  })

  // 如果历史记录过长，则删除最早的记录
  if (eraserHistory.value.length > 20) {
    // 减少历史记录数量以避免内存问题
    eraserHistory.value.shift()
  }
}

// 在指定位置擦除
const eraseAt = (x: number, y: number) => {
  if (!eraserCanvasRef.value) return

  const ctx = eraserCanvasRef.value.getContext('2d')
  if (!ctx) return

  // 设置合成操作为正常绘制，使用白色画笔
  ctx.globalCompositeOperation = 'source-over'
  ctx.fillStyle = '#ffffff'

  // 增加不透明度，使效果更明显
  ctx.globalAlpha = 1.0

  // 绘制一个圆形作为白色画笔
  ctx.beginPath()
  ctx.arc(x, y, props.eraserSize / 2, 0, Math.PI * 2)
  ctx.fill()
}

// 在两点之间擦除（用于平滑擦除）
const eraseLineBetweenPoints = (p1: { x: number; y: number }, p2: { x: number; y: number }) => {
  if (!eraserCanvasRef.value) return

  const ctx = eraserCanvasRef.value.getContext('2d')
  if (!ctx) return

  // 设置合成操作为正常绘制，使用白色画笔
  ctx.globalCompositeOperation = 'source-over'
  ctx.fillStyle = '#ffffff'

  // 增加不透明度，使效果更明显
  ctx.globalAlpha = 1.0

  // 计算两点之间的距离
  const dx = p2.x - p1.x
  const dy = p2.y - p1.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  // 在两点之间创建多个小圆，以实现平滑的线条效果
  const steps = Math.max(Math.floor(distance), 1)
  for (let i = 0; i <= steps; i++) {
    const ratio = i / steps
    const x = p1.x + dx * ratio
    const y = p1.y + dy * ratio

    ctx.beginPath()
    ctx.arc(x, y, props.eraserSize / 2, 0, Math.PI * 2)
    ctx.fill()
  }
}

// 撤销操作
const handleUndo = () => {
  if (eraserHistory.value.length === 0) return

  const ctx = eraserCanvasRef.value?.getContext('2d')
  if (!ctx) return

  // 获取最近的擦除操作
  const lastAction = eraserHistory.value.pop()
  if (!lastAction) return

  // 在撤销前保存当前状态到重做历史
  const currentData = ctx.getImageData(0, 0, eraserCanvasRef.value!.width, eraserCanvasRef.value!.height)

  eraserRedoHistory.value.push({
    x: 0,
    y: 0,
    size: 0,
    imageData: currentData,
  })

  // 恢复整个画布状态
  ctx.putImageData(lastAction.imageData, lastAction.x, lastAction.y)
}

// 重做操作
const handleRedo = () => {
  if (eraserRedoHistory.value.length === 0) return

  const ctx = eraserCanvasRef.value?.getContext('2d')
  if (!ctx) return

  // 获取最近的重做操作
  const redoAction = eraserRedoHistory.value.pop()
  if (!redoAction) return

  // 在重做前保存当前状态到撤销历史
  const currentData = ctx.getImageData(0, 0, eraserCanvasRef.value!.width, eraserCanvasRef.value!.height)

  eraserHistory.value.push({
    x: 0,
    y: 0,
    size: 0,
    imageData: currentData,
  })

  // 应用重做操作，恢复整个画布
  ctx.putImageData(redoAction.imageData, redoAction.x, redoAction.y)
}

// 加载图片到画布
const loadImage = (url: string) => {
  console.log('开始加载图片到橡皮擦画布:', url)
  if (!eraserCanvasRef.value) {
    console.error('eraserCanvasRef不存在')
    return
  }

  // 设置加载状态
  imageLoading.value = true

  const canvas = eraserCanvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    console.error('无法获取画布上下文')
    imageLoading.value = false
    return
  }

  const img = new Image()
  img.crossOrigin = 'Anonymous'
  img.src = url

  img.onload = () => {
    console.log('图片加载成功:', img.width, 'x', img.height)

    // 清除画布
    ctx.clearRect(0, 0, props.width, props.height)

    // 重置画笔绘制设置
    ctx.globalCompositeOperation = 'source-over'
    ctx.fillStyle = '#ffffff'
    ctx.globalAlpha = 1.0

    // 计算图像尺寸以适应画布
    const imgRatio = img.width / img.height
    const canvasRatio = canvas.width / canvas.height

    let drawWidth, drawHeight, offsetX, offsetY

    if (imgRatio > canvasRatio) {
      // 图像宽度适应画布
      drawWidth = canvas.width
      drawHeight = canvas.width / imgRatio
      offsetX = 0
      offsetY = (canvas.height - drawHeight) / 2
    } else {
      // 图像高度适应画布
      drawHeight = canvas.height
      drawWidth = canvas.height * imgRatio
      offsetX = (canvas.width - drawWidth) / 2
      offsetY = 0
    }

    console.log('绘制图像参数:', { drawWidth, drawHeight, offsetX, offsetY })

    // 绘制图像
    ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight)

    // 保存初始状态的图像数据
    initialImageData.value = ctx.getImageData(0, 0, canvas.width, canvas.height)

    // 清空历史记录
    eraserHistory.value = []
    eraserRedoHistory.value = []

    // 保存初始状态到历史记录
    saveStateForUndo()

    // 初始化橡皮擦功能
    initEraser()

    // 隐藏加载中状态
    imageLoading.value = false
  }

  img.onerror = (event: Event | string) => {
    console.error('图片加载失败:', url, event)
    ElMessage.error('图片加载失败')
    imageLoading.value = false
  }
}

// 保存处理后的图片
const saveImage = () => {
  if (eraserCanvasRef.value) {
    console.log('从画布保存图片')
    // 直接从画布获取图像数据
    eraserCanvasRef.value.toBlob(blob => {
      if (blob) {
        console.log('成功创建图片Blob:', blob.size, '字节')
        const file = new File([blob], 'edited-image.png', { type: 'image/png' })
        emit('save', file)
      } else {
        console.error('无法创建图片Blob')
        ElMessage.error('无法保存图片')
      }
    }, 'image/png')
  }
}

// 添加重置功能
const resetCanvas = () => {
  if (!eraserCanvasRef.value || !initialImageData.value) return

  const ctx = eraserCanvasRef.value.getContext('2d')
  if (!ctx) return

  // 保存当前状态用于撤销
  saveStateForUndo()

  // 恢复到初始状态
  ctx.putImageData(initialImageData.value, 0, 0)

  // 添加一个确认提示
  ElMessage.success('画布已重置')
}

// 监听imageUrl变化
// watch(
//   () => props.imageUrl,
//   newUrl => {
//     if (newUrl) {
//       loadImage(newUrl)
//     }
//   },
//   { immediate: true }
// )

onMounted(() => {
  // initEraser()
  // @touchstart="handleTouchStart"
  //   @touchmove="handleTouchMove"
  //   @touchend="handleTouchEnd"
  //   @touchcancel="handleTouchCancel"
  const canvas = eraserCanvasRef.value
  // console.log('🚀 ~ onMounted ~ canvas:', canvas, isPC)
  if (canvas) {
    canvas?.addEventListener(isPC ? 'mousedown' : 'touchstart', handleTouchStart as any)
    canvas?.addEventListener(isPC ? 'mousemove' : 'touchmove', handleTouchMove as any)
    canvas?.addEventListener(isPC ? 'mouseup' : 'touchend', handleTouchEnd as any)
    canvas?.addEventListener(isPC ? 'mouseleave' : 'touchcancel', handleTouchCancel as any)
  }
  loadImage(props.imageUrl)
})

onUnmounted(() => {
  const canvas = eraserCanvasRef.value
  if (canvas) {
    canvas?.removeEventListener(isPC ? 'mousedown' : 'touchstart', handleTouchStart as any)
    canvas?.removeEventListener(isPC ? 'mousemove' : 'touchmove', handleTouchMove as any)
    canvas?.removeEventListener(isPC ? 'mouseup' : 'touchend', handleTouchEnd as any)
    canvas?.removeEventListener(isPC ? 'mouseleave' : 'touchcancel', handleTouchCancel as any)
  }
})

defineExpose({
  handleUndo,
  handleRedo,
  saveImage,
  resetCanvas,
  canUndo: () => eraserHistory.value.length > 0,
  canRedo: () => eraserRedoHistory.value.length > 0,
})
</script>

<style scoped>
canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.cursor {
  position: absolute;
  top: 0;
  left: 10;
  pointer-events: none;
  z-index: 100;
  border-radius: 50%;
  border: 1px dashed #333;
  pointer-events: none;
}
</style>

<script lang="ts">
export default {
  name: 'EraserCanvas',
}
</script>
