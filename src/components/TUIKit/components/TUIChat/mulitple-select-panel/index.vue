<template>
  <div :class="{
    'mulitple-select-panel': true,
    // 'mulitple-select-panel-mobile': isMobile,
  }">
    <div class="forward-button" @click="deleteMessage">
      <img src="@/assets/images/select-bottom/delete.png" alt="" class="forward-button-icon" />
      <span :class="{
        'forward-button-text': true,
        'forward-button-text-mobile': isMobile,
      }">
        <!-- {{ TUITranslateService.t('TUIChat.逐条转发') }} -->
        删除
      </span>
    </div>
    <div class="forward-button" @click="saveMessage">
      <img src="@/assets/images/select-bottom/save.png" alt="" class="forward-button-icon" />
      <span :class="{
        'forward-button-text': true,
        'forward-button-text-mobile': isMobile,
      }">
        <!-- {{ TUITranslateService.t('TUIChat.合并转发') }} -->
        保存
      </span>
    </div>
    <div class="forward-button" @click="quoteMessage">
      <img src="@/assets/images/select-bottom/quote.png" alt="" class="forward-button-icon" />
      <span :class="{
        'forward-button-text': true,
        'forward-button-text-mobile': isMobile,
      }">
        <!-- {{ TUITranslateService.t('TUIChat.取消') }} -->
        引用
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from '../../../adapter-vue'
import { TUITranslateService } from '@tencentcloud/chat-uikit-engine'
import Icon from '../../common/Icon.vue'
import ForwardEachIcon from '../../../assets/icon/forward-each.svg'
import ForwardMergeIcon from '../../../assets/icon/forward-merge.svg'
import AddIcon from '../../../assets/icon/add-circle.svg'
import { isMobile } from '../../../utils/env'

interface IEmits {
  (key: 'deleteMessage'): void
  (key: 'saveMessage'): void
  (key: 'quoteMessage'): void
}

const emits = defineEmits<IEmits>()

const iconSize = ref(isMobile ? '25px' : '30px')
function saveMessage() {
  emits('saveMessage')
}

function deleteMessage() {
  emits('deleteMessage')
}

function quoteMessage() {
  emits('quoteMessage')
}
</script>

<style lang="scss" scoped>
:not(not) {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-width: 0;
}

.mulitple-select-panel {
  // height: 196px;
  height: 100px;
  border-top: 1px solid #ebebeb;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  background-color: #ebf0f6;

  &-mobile {
    height: 64px;
    padding-bottom: 15px;
    flex-direction: row;
    align-items: flex-end;
  }
}

.forward-button {
  justify-content: center;
  align-items: center;

  &-icon {
    width: 32px;
    height: 32px;
  }

  &-text {
    padding-top: 15px;
    font-size: 15px;

    &-mobile {
      margin-top: 2px;
    }
  }

  .cancel-button-icon {
    transform: rotate(45deg);
  }
}
</style>
