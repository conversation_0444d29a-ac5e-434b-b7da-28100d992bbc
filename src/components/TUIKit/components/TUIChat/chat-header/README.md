# Chat Header 组件使用说明

## 功能概述

Chat Header 组件支持两种显示模式：
1. **默认模式** - 显示 `chat-header-left`
2. **历史模式** - 显示 `chat-header-history`

## 使用方法

### 基本用法

```vue
<template>
  <ChatHeader 
    ref="chatHeaderRef"
    @openHistoryChat="handleOpenHistoryChat"
    @exitHistoryMode="handleExitHistoryMode"
  />
</template>

<script setup>
import { ref } from 'vue'
import ChatHeader from './chat-header/index.vue'

const chatHeaderRef = ref()

// 打开历史对话弹窗
const handleOpenHistoryChat = () => {
  // 显示历史对话弹窗
}

// 处理退出历史模式
const handleExitHistoryMode = () => {
  // 退出历史模式的逻辑
}

// 进入历史模式（通常在选择历史对话后调用）
const enterHistoryMode = (historyName) => {
  chatHeaderRef.value?.enterHistoryMode(historyName)
}
</script>
```

### 方法说明

#### `enterHistoryMode(historyName?: string)`
- **功能**: 进入历史模式，显示历史对话头部
- **参数**: 
  - `historyName` (可选): 历史对话的名称，会显示在头部
- **调用时机**: 当用户选择某个历史对话时

#### `exitHistoryMode()`
- **功能**: 退出历史模式，返回默认头部
- **调用时机**: 当用户点击返回按钮时

### 事件说明

#### `@openHistoryChat`
- **触发时机**: 用户点击"历史对话"按钮时
- **用途**: 打开历史对话弹窗

#### `@exitHistoryMode`
- **触发时机**: 用户在历史模式下点击返回按钮时
- **用途**: 处理退出历史模式的逻辑

## 状态管理

组件内部维护以下状态：
- `isHistoryMode`: 是否处于历史模式
- `selectedHistoryName`: 当前选中的历史对话名称

## 样式说明

- `.chat-header-left`: 默认模式的头部样式
- `.chat-header-history`: 历史模式的头部样式
- 两种模式通过 `v-if` 条件渲染，确保同时只显示一种头部

## 完整流程示例

1. 用户点击"历史对话"按钮
2. 触发 `@openHistoryChat` 事件
3. 父组件显示历史对话弹窗
4. 用户选择某个历史对话
5. 父组件调用 `chatHeaderRef.value.enterHistoryMode(historyName)`
6. 头部切换到历史模式，显示选中的对话名称
7. 用户点击返回按钮
8. 触发 `@exitHistoryMode` 事件
9. 头部自动切换回默认模式
