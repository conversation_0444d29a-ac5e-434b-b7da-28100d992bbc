<template>
  <div class="message-input-wrapper">
    <div :class="['message-input-container', !isPC && 'message-input-container-h5']">
      <!-- <div class="audio-recorder-wrapper">
        <AudioRecorder @recordingComplete="handleRecordingComplete" />
      </div> -->
      <div class="message-input-container-h5-center">
        <ChatAiTool v-if="chatStore.showAiTool" />
        <MessageInputEditor
          ref="editor"
          :placeholder="props.placeholder"
          :isMuted="props.isMuted"
          :muteText="props.muteText"
          :enableInput="props.enableInput"
          :enableAt="props.enableAt"
          :enableTyping="props.enableTyping"
          :enableDragUpload="props.enableDragUpload"
          @sendMessage="sendMessage"
          @onTyping="onTyping"
          @change="change"
          @onAt="onAt"
          @inserPound="inserPound"
          @emitData="emitData"
        />
        <!-- <div @click="handleAssistant" :class="['assistant-btn', chatStore.assistantInputShow && 'assistant-btn-active']" v-if="chatStore.tool === '/文生图'">
          <img :src="chatStore.assistantInputShow ? assistantActive : assistant" alt="" />
          <span>模版</span>
        </div> -->

        <MessageInputButton @sendMessage="sendMessage" :inputEmpty="inputEmpty" />
      </div>
      <div class="message-input-container-h5-left">
        <UploadFile />
      </div>
    </div>
    <MessageInputQuote />
  </div>
</template>

<script setup lang="ts">
import { TUIStore, StoreName, IConversationModel } from '@tencentcloud/chat-uikit-engine'
import { ref } from '../../../adapter-vue'
import MessageInputEditor from './message-input-editor.vue'
import MessageInputButton from './message-input-button.vue'
import MessageInputQuote from './message-input-quote/index.vue'
import { sendMessages, sendTyping } from '../utils/sendMessage'
import { transformTextWithEmojiNamesToKeys } from '../emoji-config'
import { isPC, isH5 } from '../../../utils/env'
import useChatStore from '@/store/modules/chat'
import AudioRecorder from '@/components/AudioRecorder/index.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import ChatAiTool from '@/components/ChatAiTool/index.vue'
import { sendCustomMessage } from '@/utils/customIM'
import { ElMessage } from 'element-plus'
import { parseQuery } from '@/components/TUIKit/utils/tool'
import { TUIChatEngine } from '@tencentcloud/chat-uikit-engine'
import assistant from '@/assets/images/assistant-input.png'
import assistantActive from '@/assets/images/assistant-input-active.png'
const chatStore = useChatStore()

const props = defineProps({
  placeholder: {
    type: String,
    default: 'this is placeholder',
  },
  isMuted: {
    type: Boolean,
    default: true,
  },
  muteText: {
    type: String,
    default: '',
  },
  enableInput: {
    type: Boolean,
    default: true,
  },
  enableAt: {
    type: Boolean,
    default: true,
  },
  enableDragUpload: {
    type: Boolean,
    default: true,
  },
  enableTyping: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['sendMessage', 'resetReplyOrReference', 'onTyping'])
const editor = ref<InstanceType<typeof MessageInputEditor>>()
const messageInputAtRef = ref<InstanceType<typeof MessageInputAt>>()
const currentConversation = ref<IConversationModel>()
const inputEmpty = ref<boolean>(true)
const buildversion = ref(parseInt(parseQuery(navigator.userAgent).buildversion))
const toolTemplateRef = ref()
const inputContent = ref<string>('')
TUIStore.watch(StoreName.CONV, {
  currentConversation: (conversation: IConversationModel) => {
    currentConversation.value = conversation
  },
})
const emitData = (isEmpty: boolean, content: string) => {
  console.log('🚀 ~ emitData ~ isEmpty:', isEmpty)
  console.log('🚀 ~ emitData ~ content:', content)
  handleTemplateEmpty(isEmpty, content)
}
const onTyping = (inputContentEmpty: boolean, inputBlur: boolean) => {
  sendTyping(inputContentEmpty, inputBlur)
}

const onAt = (show: boolean) => {
  messageInputAtRef.value?.toggleAtList(show)
}

// /号键调起AI工具
const inserPound = () => {
  chatStore.setShowAiTool(true)
  // editor.value?.resetEditor()
}

// 输入框内容变化
const change = (text: string) => {
  // 删除零宽字符
  console.log('chatStore.tool', `A${chatStore.tool}A`, `A${text.trim()}A`)
  let isInTool = false
  chatStore.tools.forEach((tool: any) => {
    if (tool.functionName === text.trim()) {
      isInTool = true
    }
  })

  // 检查是否在工具模式，如果在工具模式，使用tool-template的判空逻辑
  if (chatStore.tool) {
    // 工具模式下，输入框为空的逻辑由tool-template组件的templateEmpty事件处理
    inputEmpty.value = isInTool
  } else {
    // 非工具模式，使用原来的判空逻辑
    inputEmpty.value = text.trim() === '' || isInTool
  }
}

const sendMessage = async () => {
  let cmd = ''
  let text = inputContent.value
  console.log('🚀 ~ sendMessage ~ text:', text)

  // 判断是否使用工具模板
  if (chatStore.tool) {
    // 如果有选中的工具，使用tool-template组件获取内容
    cmd = chatStore.tool
    // 使用editor中的getTemplateContent方法获取工具模板内容
    if (editor.value) {
      // 使用getTemplateContent方法获取完整的模板内容（包括下拉框选择的值）
      text = editor.value.getTemplateContent()
      // 从完整内容中移除工具命令前缀
      text = text.replace(chatStore.tool, '').trim()
    } else {
      text = inputContent.value.replace(chatStore.tool, '').trim()
    }
  } else {
    // 如果没有选中工具，使用原来的逻辑
    text = inputContent.value
    cmd = ''
  }
  if (chatStore.quoteList.length || cmd) {
    // if (cmd === '/视频合成' || cmd === '/视频拼接') {
    //   // 判断是否为视频或者视频加音频
    //   const isAllVideo = chatStore.quoteList.every(item => item.type === 'video')
    //   const isVideoOrAudio = chatStore.quoteList.every(item => item.type === 'video' || item.type === 'audio')

    //   if (!(isAllVideo || isVideoOrAudio)) {
    //     ElMessage.warning('该功能仅支持使用视频或音频素材哦~')
    //     return
    //   }
    // }
    await sendCustomMessage({
      data: {
        businessID: 'ai_custom_msg',
        content: {
          name: 'cmd_msg',
          data: {
            text,
            referenceList: chatStore.quoteList,
            cmd,
          },
        },
      },
    })
    chatStore.setQuoteList([])
    chatStore.setTool('')
  } else {
    // await sendMessages(editorContentList, currentConversation.value)
    // 添加空值检查，确保会话存在
    console.log('🚀 ~ sendMessage ~ currentConversation.value:', currentConversation.value)
    if (currentConversation.value) {
      // await sendMessages([{ payload: { text }, type: 'text' }], currentConversation.value)
      await sendCustomMessage({
        data: {
          businessID: 'ai_custom_msg',
          content: {
            name: 'cmd_msg',
            data: {
              text,
            },
          },
        },
      })
    }
  }

  emit('sendMessage')
  // editor.value?.resetEditor()

  // 重置编辑器模板
  resetEditorTemplate()

  inputEmpty.value = true
  // chatStore.setSendStatus(false)
}

// 处理tool-template组件的空状态变化
const handleTemplateEmpty = (isEmpty: boolean, content: string) => {
  console.log('🚀 ~ handleTemplateEmpty ~ isEmpty:', isEmpty)
  console.log('🚀 ~ handleTemplateEmpty ~ content:', content)
  // 工具模式下，根据tool-template组件的状态更新输入框是否为空
  inputEmpty.value = isEmpty
  inputContent.value = content
}

const insertEmoji = (emoji: any) => {
  editor.value?.addEmoji(emoji)
}

const onAtListOpen = () => {
  if (isH5) {
    editor.value?.blur()
  }
}

// const reEdit = (content: any) => {
//   // editor.value?.resetEditor()
//   // editor.value?.setEditorContent(content)
// }
const handleRecordingComplete = (text: string) => {
  console.log('🚀 ~ handleRecordingComplete ~ text:', text)
  if (!text || !editor.value) return

  // 使用addRecordingToTemplate方法添加文本
  editor.value.addRecordingToTemplate(text)

  // 更新输入内容状态
  inputContent.value = text
  inputEmpty.value = false
}

// 重置编辑器模板状态的方法
const resetEditorTemplate = () => {
  if (editor.value) {
    console.log('🚀 ~ resetEditorTemplate ~ editor:', editor)
    editor.value.resetToolTemplate()
    inputEmpty.value = true
  }
}
const handleAssistant = () => {
  chatStore.setAssistantInputShow(!chatStore.assistantInputShow)
}
defineExpose({
  insertEmoji,
  // reEdit,
  handleTemplateEmpty,
  resetEditorTemplate,
  handleAssistant,
})
</script>

<style scoped lang="scss">
@import '../../../assets/styles/common';

.message-input-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.message-input-container {
  box-sizing: border-box;
  flex: 1;
  display: flex;
  align-items: start;
  flex-direction: column;
  border: none;
  overflow: visible;
}

.message-input-container-h5 {
  display: flex;
  flex-flow: row nowrap;
  align-items: flex-end;
}

.message-input-container-h5-left {
  margin: 0 16px 0 0;
}

.message-input-container-h5-center {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  // width: 100%;
  height: 100%;
  min-height: 64px;
  border-radius: 32px;
  border: 2px solid transparent;
  background-image: linear-gradient(#fff, #fff), linear-gradient(to right, #4facfe, #00f2fe);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  padding: 0 10px;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  position: relative;
  margin: 0 16px;

  // margin: 0 8px;
  .assistant-btn {
    width: 87px;
    height: 38px;
    border-radius: 5px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #98a9d1;

    img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }

    span {
      font-size: 19px;
    }
  }

  .assistant-btn-active {
    background: #2266ff;
    color: #fff;
  }
}

.audio-recorder-wrapper {
  width: 64px;
  height: 64px;
  margin-left: 16px;
}
</style>
