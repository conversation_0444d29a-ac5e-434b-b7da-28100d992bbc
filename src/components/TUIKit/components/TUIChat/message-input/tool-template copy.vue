<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-06 15:54:25
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-05-09 12:27:34
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/message-input/tool-template.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%
-->


<template>
  <div class="message-input-editor-area-new" contenteditable="true" @blur="handleInputBoxBlur" @focus="handleInputBoxFocus" @input="handleInputBoxInput" @keyup="handleKeyUp" ref="toolTemplateRef">
    <span class="text-span">我想创作一首歌曲，用</span>
    <span @click="handleSelectAILyrics" class="select-box">
      <div class="select-text-container" contenteditable="false">
        <span class="select-text select-text-color">{{ findSelectedOption(modalDataAILyrics, selectedAILyric) }}</span>
        <span :class="['select-text-arrow', showAILyrics ? 'select-text-arrow-up' : 'select-text-arrow-down']"></span>
      </div>
    </span>
    <span class="input-box" :contenteditable="contentEditable" @click="handleInputBoxClick" ref="inputBoxRef"> </span>
    <span class="text-span">。这首歌是</span>
    <span @click="handleSelectMusicStyle" class="select-box">
      <div class="select-text-container" contenteditable="false">
        <span class="select-text select-text-color">{{ findSelectedOption(modalDataMusicStyle, selectedMusicStyle) }}</span>
        <span :class="['select-text-arrow', showMusicStyle ? 'select-text-arrow-up' : 'select-text-arrow-down']"></span>
      </div>
    </span>
    <span class="text-span">音乐风格，传达</span>
    <span @click="handleSelectEmotion" class="select-box">
      <div class="select-text-container" contenteditable="false">
        <span class="select-text select-text-color">{{ findSelectedOption(modalDataEmotion, selectedEmotion) }}</span>
        <span :class="['select-text-arrow', showEmotion ? 'select-text-arrow-up' : 'select-text-arrow-down']"></span>
      </div>
    </span>
    <span class="text-span">的情绪，使用</span>
    <span @click="handleSelectVoice" class="select-box">
      <div class="select-text-container" contenteditable="false">
        <span class="select-text select-text-color">{{ findSelectedOption(modalDataVoice, selectedVoice) }}</span>
        <span :class="['select-text-arrow', showVoice ? 'select-text-arrow-up' : 'select-text-arrow-down']"></span>
      </div>
    </span>
    <span class="text-span">音色</span>
  </div>
  <Teleport to="body">
    <div class="modal" ref="modalRef" v-show="showModal">
      <ul class="option-list">
        <li v-for="item in modalData" :key="item.value" class="option-item" :class="{ 'option-item-selected': item.is_selected }" @click="handleSelectItem(item)">
          <span>{{ item.show_name }}</span>
          <span v-if="item.is_selected" class="check-icon">✓</span>
        </li>
      </ul>
    </div>
  </Teleport>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick, watch, watchEffect, defineEmits } from 'vue'

// 定义需要向父组件发送的事件
const emit = defineEmits(['templateEmpty'])

// 为每个选择框创建独立的状态
const showAILyrics = ref(false)
const showMusicStyle = ref(false)
const showEmotion = ref(false)
const showVoice = ref(false)

// 添加placeholder变量
const inputPlaceholder = ref('请输入文案')

// 当前选中的值
const selectedAILyric = ref('AI_lyric')
const selectedMusicStyle = ref('Pop')
const selectedEmotion = ref('Happy')
const selectedVoice = ref('Female')

// 控制modal显示的状态
const showModal = ref(false)
const modalReady = ref(false) // 新增状态，控制modal是否准备好显示
const modalRef = ref<HTMLElement | null>(null)
const editorText = ref('[描述歌词要表达的主题]')
const modalData = ref<{ show_name: string; value: string; is_default: boolean; is_selected?: boolean; after_key?: string }[]>([])

// 引用DOM元素
const inputBoxRef = ref<HTMLElement | null>(null)
const inputTextContentRef = ref<HTMLElement | null>(null)
const toolTemplateRef = ref<HTMLElement | null>(null)
const contentEditable = ref(false)
const configList = ref([
  {
    id: 1,
    type: 'text',
    text: '我想创作一首歌曲，用',
  },
  {
    id: 2,
    type: 'select',
    text: '',
    list: [
      {
        show_name: 'AI 帮我写歌词',
        value: 'AI_lyric',
        is_default: true,
      },
      {
        show_name: '自定义歌词',
        value: 'custome_lyric',
        is_default: false,
      },
    ],
  },
  {
    id: 3,
    type: 'input',
    text: '',
    placeholder: '[描述歌词要表达的主题]',
  },
  {
    id: 4,
    type: 'text',
    text: '。这首歌是',
  },
  {
    id: 5,
    type: 'select',
    text: '',
    list: [
      {
        show_name: '流行',
        value: 'Pop',
        is_default: true,
      },
      {
        show_name: '嘻哈',
        value: 'Hip Hop/Rap',
        is_default: false,
      },
      {
        show_name: '国风',
        value: 'Chinese Style',
        is_default: false,
      },
      {
        show_name: 'DJ',
        value: 'DJ',
        is_default: false,
      },
      {
        show_name: '摇滚',
        value: 'Rock',
        is_default: false,
      },
      {
        show_name: '民谣',
        value: 'Folk',
        is_default: false,
      },
      {
        show_name: 'R&B',
        value: 'R&B/Soul',
        is_default: false,
      },
      {
        show_name: '雷鬼',
        value: 'Reggae',
        is_default: false,
      },
      {
        show_name: '朋克',
        value: 'Punk',
        is_default: false,
      },
      {
        show_name: '电音',
        value: 'Electronic',
        is_default: false,
      },
      {
        show_name: '爵士',
        value: 'Jazz',
        is_default: false,
      },
    ],
  },
  {
    id: 6,
    type: 'text',
    text: '音乐风格，传达',
  },
  {
    id: 7,
    type: 'select',
    text: '',
    list: [
      {
        show_name: '快乐',
        value: 'Happy',
        is_default: true,
      },
      {
        show_name: '放松',
        value: 'Chill',
        is_default: false,
      },
      {
        show_name: '活力',
        value: 'Dynamic/Energetic',
        is_default: false,
      },
      {
        show_name: '兴奋',
        value: 'Excited',
        is_default: false,
      },
      {
        show_name: '忧郁',
        value: 'Sentimental/Melancholic/Lonely',
        is_default: false,
      },
      {
        show_name: '鼓舞',
        value: 'Inspirational/Hopeful',
        is_default: false,
      },
      {
        show_name: '伤感',
        value: 'Sorrow/Sad',
        is_default: false,
      },
      {
        show_name: '怀旧',
        value: 'Nostalgic/Memory',
        is_default: false,
      },
      {
        show_name: '浪漫',
        value: 'Romantic',
        is_default: false,
      },
    ],
  },
  {
    id: 8,
    type: 'text',
    text: '的情绪，使用',
  },
  {
    id: 9,
    type: 'select',
    text: '',
    list: [
      {
        show_name: '男声',
        value: 'Male',
        is_default: false,
      },
      {
        show_name: '女声',
        value: 'Female',
        is_default: true,
      },
    ],
  },
  {
    id: 10,
    type: 'text',
    text: '音色',
  },
])
// 处理输入框点击
const handleInputBoxClick = (e: MouseEvent) => {
  // 阻止冒泡，避免点击事件传递到父元素
  e.stopPropagation()
  // 将contentEditable设置为true，允许编辑
  contentEditable.value = true
}

// 预定义选项数据，避免重复创建
const modalDataAILyrics = [
  {
    show_name: 'AI 帮我写歌词',
    value: 'AI_lyric',
    is_default: true,
    after_key: 'musicGenaration_AI_lyric',
  },
  {
    show_name: '自定义歌词',
    value: 'custome_lyric',
    is_default: false,
    after_key: 'musicGenaration_custom_lyric',
  },
]

const modalDataMusicStyle = [
  {
    show_name: '流行',
    value: 'Pop',
    is_default: true,
  },
  {
    show_name: '嘻哈',
    value: 'Hip Hop/Rap',
    is_default: false,
  },
  {
    show_name: '国风',
    value: 'Chinese Style',
    is_default: false,
  },
  {
    show_name: 'DJ',
    value: 'DJ',
    is_default: false,
  },
  {
    show_name: '摇滚',
    value: 'Rock',
    is_default: false,
  },
  {
    show_name: '民谣',
    value: 'Folk',
    is_default: false,
  },
  {
    show_name: 'R&B',
    value: 'R&B/Soul',
    is_default: false,
  },
  {
    show_name: '雷鬼',
    value: 'Reggae',
    is_default: false,
  },
  {
    show_name: '朋克',
    value: 'Punk',
    is_default: false,
  },
  {
    show_name: '电音',
    value: 'Electronic',
    is_default: false,
  },
  {
    show_name: '爵士',
    value: 'Jazz',
    is_default: false,
  },
]

const modalDataEmotion = [
  {
    show_name: '快乐',
    value: 'Happy',
    is_default: true,
  },
  {
    show_name: '放松',
    value: 'Chill',
    is_default: false,
  },
  {
    show_name: '活力',
    value: 'Dynamic/Energetic',
    is_default: false,
  },
  {
    show_name: '兴奋',
    value: 'Excited',
    is_default: false,
  },
  {
    show_name: '忧郁',
    value: 'Sentimental/Melancholic/Lonely',
    is_default: false,
  },
  {
    show_name: '鼓舞',
    value: 'Inspirational/Hopeful',
    is_default: false,
  },
  {
    show_name: '伤感',
    value: 'Sorrow/Sad',
    is_default: false,
  },
  {
    show_name: '怀旧',
    value: 'Nostalgic/Memory',
    is_default: false,
  },
  {
    show_name: '浪漫',
    value: 'Romantic',
    is_default: false,
  },
]

const modalDataVoice = [
  {
    show_name: '男声',
    value: 'Male',
    is_default: false,
  },
  {
    show_name: '女声',
    value: 'Female',
    is_default: true,
  },
]

// 查找选中的选项显示名称
const findSelectedOption = (options: any[], selectedValue: string) => {
  const option = options.find(item => item.value === selectedValue)
  return option ? option.show_name : options.find(item => item.is_default)?.show_name || options[0].show_name
}

// 处理选项点击
const handleSelectItem = (item: any) => {
  // 更新当前激活的下拉菜单的选中项
  if (showAILyrics.value) {
    selectedAILyric.value = item.value
    modalData.value.forEach(i => (i.is_selected = i.value === item.value))
  } else if (showMusicStyle.value) {
    selectedMusicStyle.value = item.value
    modalData.value.forEach(i => (i.is_selected = i.value === item.value))
  } else if (showEmotion.value) {
    selectedEmotion.value = item.value
    modalData.value.forEach(i => (i.is_selected = i.value === item.value))
  } else if (showVoice.value) {
    selectedVoice.value = item.value
    modalData.value.forEach(i => (i.is_selected = i.value === item.value))
  }

  // 关闭弹窗
  showModal.value = false
  showAILyrics.value = false
  showMusicStyle.value = false
  showEmotion.value = false
  showVoice.value = false
}

// 通用的处理函数，用于设置modal位置和内容
const handleSelectOption = async (e: MouseEvent, stateRef: any, content: string) => {
  // 获取点击元素的位置信息
  const target = e.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  console.log('🚀 ~ handleSelectOption ~ rect:', rect)

  // 如果当前点击的选项已经是打开状态，则关闭modal
  if (stateRef.value) {
    modalReady.value = false // 先标记为未准备好
    showModal.value = false
    stateRef.value = false
    return
  }

  // 重置所有状态
  showAILyrics.value = false
  showMusicStyle.value = false
  showEmotion.value = false
  showVoice.value = false

  // 设置当前点击的状态为true
  stateRef.value = true

  // 先隐藏modal并设置内容
  modalReady.value = false
  showModal.value = false

  // 等待DOM更新
  await nextTick()

  // 确保modal元素存在
  if (modalRef.value) {
    // 先设置为不可见但存在于DOM中，以便获取尺寸
    showModal.value = true
    modalReady.value = false

    // 等待DOM更新
    await nextTick()

    // 获取实际尺寸
    const actualWidth = modalRef.value.offsetWidth
    const actualHeight = modalRef.value.offsetHeight

    // 计算水平居中位置
    const left = rect.left + rect.width / 2 - actualWidth / 2

    // 计算垂直位置，优先显示在元素上方
    let top = rect.top - actualHeight - 10 // 元素顶部 - modal高度 - 10px间距

    // 检查是否有足够空间在上方显示，如果没有则显示在下方
    if (top < 10) {
      top = rect.bottom + 10 // 元素底部 + 10px间距
      modalRef.value.classList.remove('arrow-bottom')
      modalRef.value.classList.add('arrow-top')
    } else {
      modalRef.value.classList.remove('arrow-top')
      modalRef.value.classList.add('arrow-bottom')
    }

    // 确保不超出屏幕边界
    const viewportWidth = window.innerWidth
    const adjustedLeft = Math.max(10, Math.min(viewportWidth - actualWidth - 10, left))

    // 设置modal的样式
    modalRef.value.style.left = `${adjustedLeft}px`
    modalRef.value.style.top = `${top}px`

    // 位置设置完成后，标记modal为准备好显示
    modalReady.value = true
  }
}

// 为每个选择框创建独立的处理函数
const handleSelectAILyrics = (e: MouseEvent) => {
  modalData.value = modalDataAILyrics.map(item => ({
    ...item,
    is_selected: item.value === selectedAILyric.value,
  }))
  handleSelectOption(e, showAILyrics, 'AI 歌词选项')
}

const handleSelectMusicStyle = (e: MouseEvent) => {
  modalData.value = modalDataMusicStyle.map(item => ({
    ...item,
    is_selected: item.value === selectedMusicStyle.value,
  }))
  handleSelectOption(e, showMusicStyle, '音乐风格选项')
}

const handleSelectEmotion = (e: MouseEvent) => {
  modalData.value = modalDataEmotion.map(item => ({
    ...item,
    is_selected: item.value === selectedEmotion.value,
  }))
  handleSelectOption(e, showEmotion, '情绪选项')
}

const handleSelectVoice = (e: MouseEvent) => {
  modalData.value = modalDataVoice.map(item => ({
    ...item,
    is_selected: item.value === selectedVoice.value,
  }))
  handleSelectOption(e, showVoice, '音色选项')
}

const handleInputBoxBlur = () => {
  console.log('🚀 ~ handleInputBoxBlur ~ inputBoxRef.value:', inputBoxRef.value)
  // 失去焦点时，将contentEditable设置为false
  contentEditable.value = false
}

const handleInputBoxFocus = () => {
  console.log('🚀 ~ handleInputBoxFocus ~ inputBoxRef.value:', inputBoxRef.value)
  // 获得焦点时，将contentEditable设置为true
  contentEditable.value = true
}

const handleInputBoxInput = () => {
  console.log('🚀 ~ handleInputBoxInput ~ inputBoxRef.value:', inputTextContentRef.value?.innerHTML !== '&nbsp;')
  if (inputTextContentRef.value?.innerHTML !== '&nbsp;') {
    const placeholder = inputBoxRef.value?.querySelector('.placeholder-text') as HTMLElement
    placeholder.style.display = 'none'
  } else {
    const placeholder = inputBoxRef.value?.querySelector('.placeholder-text') as HTMLElement
    placeholder.style.display = 'flex'
    // 当内容被删除为空时，设置contentEditable为false
    contentEditable.value = false
  }

  // 检查整个输入区域是否为空
  checkIfContentEmpty()
}

// 检查内容是否为空并触发事件
const checkIfContentEmpty = () => {
  if (!toolTemplateRef.value) return

  // 获取原始HTML内容
  const content = toolTemplateRef.value.innerHTML

  // 检查内容是否为空或只包含<br>标签
  const isEmpty = !content || content.trim() === '' || /^<span[^>]*><br><\/span>$/.test(content.trim()) || (toolTemplateRef.value.textContent?.trim() || '') === ''

  // 处理<br>元素情况
  if (content.includes('<br>')) {
    // 删除<br>元素以确保内容真正为空
    const brElements = toolTemplateRef.value.querySelectorAll('br')
    brElements.forEach(brElement => {
      brElement.remove()
    })

    // 如果包含<br>且判断为空，则触发空内容事件
    if (isEmpty) {
      emit('templateEmpty', true)
    }
  }

  // 输出调试信息
  console.log('🚀 ~ checkIfContentEmpty ~ content:', content)
  console.log('🚀 ~ checkIfContentEmpty ~ isEmpty:', isEmpty)

  // 如果为空，触发事件通知父组件
  if (isEmpty) {
    emit('templateEmpty', true)
  }
}

// 处理键盘事件
const handleKeyUp = (e: KeyboardEvent) => {
  // 当按下删除键或退格键时，检查内容是否为空
  if (e.key === 'Delete' || e.key === 'Backspace') {
    checkIfContentEmpty()
  }
}

// 组件挂载后检查内容
onMounted(() => {
  // 初始化时检查内容是否为空
  nextTick(() => {
    checkIfContentEmpty()
  })
})
</script>
<style lang="scss" scoped>
.message-input-editor-area-new {
  display: flex;
  overflow: auto;
  user-select: text;
  hyphens: auto;
  word-wrap: break-word;
  word-break: break-word;
  flex-wrap: wrap;
  align-items: center;
  line-height: 1.5;
  font-size: 16px;
  gap: 2px;

  /* 适用于 WebKit 浏览器（如 Chrome、Safari） */

  &::-webkit-scrollbar {
    display: none;
  }
  /* 适用于 Firefox */
  scrollbar-width: none;

  .text-span {
    display: inline-flex;
    align-items: center;
    height: 24px;
  }

  .select-box {
    display: inline-flex;
    align-items: center;
    height: 24px;
    cursor: pointer; // 添加指针样式
    .select-text-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 4px;
      border-radius: 4px;
      // background-color: #f0f0f0;
      width: fit-content;
      height: 24px;
    }
  }
  .input-box {
    min-width: 40px; // 减小最小宽度，使其可以随内容变化
    max-width: 100%; // 允许最大宽度为100%以便于换行
    width: auto; // 根据内容自动调整宽度
    border-radius: 10px;
    word-break: break-word;
    color: #0057ff;
    background-color: rgba(0, 87, 255, 0.06);
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    min-height: 24px;
    box-sizing: border-box;
    position: relative;
    cursor: text; // 添加文本光标
    transition: background-color 0.2s ease; // 添加过渡效果
    white-space: pre-wrap; // 保留空格并允许换行
    flex-grow: 1; // 允许元素在需要时增长
    flex-shrink: 1; // 允许元素在需要时收缩

    &:hover {
      background-color: rgba(0, 87, 255, 0.1); // 鼠标悬停时加深背景色
    }

    &:focus,
    &:focus-within {
      outline: 1px solid rgba(0, 87, 255, 0.3); // 获得焦点时添加轮廓
      background-color: rgba(0, 87, 255, 0.08); // 获得焦点时加深背景色
    }

    &.has-content::after {
      content: none; // 有内容时不显示占位符
    }

    &:empty::after {
      content: attr(data-placeholder); // 使用属性值作为占位符文本
      display: inline-block;
      color: #999;
      opacity: 0.7;
      pointer-events: none; // 确保不会捕获鼠标事件
    }
  }
}

.select-text-color {
  color: #0057ff;
  font-weight: 600;
}
.select-text-arrow {
  // 三角形
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 6px solid #0057ff;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  margin-left: 4px;
  margin-right: 4px;
}
.select-text-arrow-up {
  animation: rotateUp 0.3s ease-in-out forwards;
}
.select-text-arrow-down {
  animation: rotateDown 0.3s ease-in-out forwards;
}
@keyframes rotateUp {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}
@keyframes rotateDown {
  from {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(0deg);
  }
}
// 添加fade过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.modal {
  position: absolute;
  height: auto; /* 修改为自适应内容高度 */
  max-height: 300px; /* 保留最大高度限制，防止内容过多时溢出 */
  width: fit-content; /* 自适应内容宽度 */
  min-width: 100px; /* 设置最小宽度 */
  max-width: 300px; /* 保留最大宽度限制 */
  background: #ffffff;
  z-index: 9999;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0; /* 添加内边距，使内容不会紧贴边缘 */
  overflow-y: auto; /* 当内容超过max-height时显示滚动条 */

  .option-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;

    &:hover {
      background-color: #f5f7fa;
    }

    &-selected {
      color: #0057ff;
      font-weight: 500;
    }

    .check-icon {
      color: #0057ff;
      font-weight: bold;
    }
  }

  // 箭头样式根据实际显示位置动态添加
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
  }

  // 当显示在元素上方时，箭头朝下
  &.arrow-bottom::after {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-top: 8px solid #ffffff;
    border-bottom: none;
  }

  // 当显示在元素下方时，箭头朝上
  &.arrow-top::after {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom: 8px solid #ffffff;
    border-top: none;
  }
}
</style>