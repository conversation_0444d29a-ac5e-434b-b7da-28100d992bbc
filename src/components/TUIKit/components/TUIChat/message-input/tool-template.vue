<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-06 15:54:25
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-24 14:43:08
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/message-input/tool-template.vue
 * @Description: TinyMCE编辑器组件，支持内联模式、不可编辑元素和点击监听
-->

<template>
  <div class="tool-template" ref="editorContainer">
    <div ref="editorRef" class="tinymce-editor"></div>
    <teleport to=".chat-container">
      <div v-if="showDropdown" ref="dropdownRef" class="teleport-dropdown-menu" :style="dropdownStyle">
        <div v-for="(item, index) in dropdownOptions" :key="index"
          :class="['dropdown-item', { 'is-header': item.isHeader }, { 'is-selected': item.value === selectedValue }]"
          @click="!item.isHeader && handleDropdownItemClick(item)">
          <span class="select-index">{{ item.index ? `${item.index} ` : '' }}</span>
          <span class="select-label">{{ item.label }}</span>
          <img class="icon-check" v-if="item.value === selectedValue && !item.isHeader"
            src="@/assets/images/icon-check.png" alt="check" />
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineExpose, reactive, computed, nextTick, watch } from 'vue'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver'
import 'tinymce/models/dom'
import 'tinymce/icons/default'
import contentUiCss from 'tinymce/skins/ui/oxide/content.inline.css?inline'
import toolTemplateCss from './tool-template.css?inline'
import { Check as ElIconCheck } from '@element-plus/icons-vue'
import useChatStore from '@/store/modules/chat'
// 移除错误的导入，直接定义接口类型
interface ISpecialCourseConfig {
  icon?: string // 图标URL
  tips?: string // 提示文字
  functionName?: string // 功能名称，如 "/文生图"
  functionJson?: Array<{
    text: string // 文本内容
    type: 'text' | 'select' | 'input' // 类型：文本或选择器或输入框
    list?: Array<{
      show_name: string // 显示名称
      is_default: string // 是否默认选中，"true"/"false"
      value: string // 实际值
    }> // 当type为select时的选项列表
  }> // 功能配置的JSON数组
}
const editorRef = ref<HTMLElement | null>(null)
const editorContainer = ref<HTMLElement | null>(null)
const editor = ref<any>(null)
const content = ref('')
const editorId = `tinymce-editor-${Date.now()}`
const hasToolElement = ref(true) // 添加状态追踪tool元素是否存在
const chatStore = useChatStore()

console.log('🚀 ~ chatStore:', chatStore)
// 下拉框相关状态
const emits = defineEmits(['templateData', 'sendMessage'])
const showDropdown = ref(false)
const dropdownRef = ref<HTMLElement | null>(null)
const currentElement = ref<HTMLElement | null>(null)
const dropdownType = ref('')
const selectedValue = ref('')
const dropdownStyle = reactive({
  bottom: '0px',
  left: '0px',
  opacity: '1',
  transition: 'opacity 0.15s',
})
console.log('🚀 ~ dropdownStyle:', dropdownStyle)

// 下拉框选项配置
const dropdownOptions = computed(() => {
  // 从元素获取配置信息
  if (currentElement.value) {
    const configJson = currentElement.value.getAttribute('data-config')
    if (configJson) {
      try {
        const config = JSON.parse(configJson)
        if (config && Array.isArray(config.list)) {
          return config.list.map((item: any, index: number) => ({
            label: item.show_name,
            value: item.value,
            index: index + 1, // 添加序号，从1开始
            isDefault: item.is_default === 'true',
          }))
        }
      } catch (e) {
        console.error('解析下拉框配置出错:', e)
      }
    }
  }
  return []
})

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  if (showDropdown.value && dropdownRef.value && !dropdownRef.value.contains(event.target as Node) && currentElement.value !== event.target) {
    showDropdown.value = false

    // 移除之前选中元素的active类
    if (currentElement.value) {
      currentElement.value.classList.remove('active')
    }
  }
}

// 处理下拉项点击
const handleDropdownItemClick = (item: { label: string; value: string }) => {
  if (currentElement.value) {
    // 获取当前元素的数据值
    const currentValue = currentElement.value.getAttribute('data-value') || ''

    // 如果点击的是已选中的项目，则取消选中，恢复默认项
    if (item.value === currentValue) {
      // 从配置中获取原始的config.text
      const configJson = currentElement.value.getAttribute('data-config')
      if (configJson) {
        try {
          const config = JSON.parse(configJson)
          // 恢复为原始的config.text
          currentElement.value.textContent = config.text
          // 清空数据值，表示未选中
          currentElement.value.setAttribute('data-value', '')
          selectedValue.value = ''
        } catch (e) {
          console.error('解析配置出错:', e)
        }
      }
    } else {
      // 选中新选项
      currentElement.value.textContent = item.label
      currentElement.value.setAttribute('data-value', item.value)
      selectedValue.value = item.value
    }

    showDropdown.value = false

    // 移除激活状态类
    currentElement.value.classList.remove('active')

    // 更新编辑器内容
    if (editor.value) {
      // 触发原生DOM变更事件，确保编辑器能检测到内容变化
      editor.value.fire('input')
      editor.value.fire('change')
      // 重新获取内容
      content.value = editor.value.getContent()

      // 使用自定义方法获取包含输入框内容的文本
      const text = getTextContentWithInputs()

      // 触发模板数据更新
      if (chatStore.tool) {
        const formatText = text.replace(chatStore.tool, '').trim()
        emits('templateData', false, formatText)
      } else {
        emits('templateData', text ? false : true, text)
      }
    }

    // 在所有更新操作完成后，将焦点重新设置到编辑器
    nextTick(() => {
      if (editor.value) {
        editor.value.focus()
      }
    })
  }
}

// 显示下拉框
const showDropdownMenu = (element: HTMLElement) => {
  // 设置当前操作的元素
  currentElement.value = element
  // 获取当前元素的data-value，如果有值则表示已选中
  selectedValue.value = element.getAttribute('data-value') || ''

  // 根据元素的data-type决定显示哪种下拉框
  const type = element.getAttribute('data-type') || ''
  dropdownType.value = type

  // 只有配置了选项的类型才显示下拉框
  if (dropdownOptions.value.length > 0) {
    // 先隐藏下拉框，避免位置计算前闪烁
    dropdownStyle.opacity = '0'
    showDropdown.value = true

    // 添加激活状态类
    element.classList.add('active')

    // 下一个事件循环中计算位置
    nextTick(() => {
      if (element && dropdownRef.value) {
        const elementRect = element.getBoundingClientRect()
        const dropdownRect = dropdownRef.value.getBoundingClientRect()

        // 计算下拉框位置 - 在元素上方居中
        let leftPosition = elementRect.left + elementRect.width / 2 - dropdownRect.width / 2

        // 确保不超出屏幕左侧
        leftPosition = Math.max(8, leftPosition)
        // 确保不超出屏幕右侧
        leftPosition = Math.min(leftPosition, window.innerWidth - dropdownRect.width - 8)

        // 计算bottom位置 - 从元素顶部向上
        const chatContainer = document.querySelector('.chat-container')
        let bottomPosition = 0

        if (chatContainer) {
          const containerRect = chatContainer.getBoundingClientRect()
          bottomPosition = containerRect.bottom - elementRect.top + 8
        }

        dropdownStyle.left = `${leftPosition}px`
        dropdownStyle.bottom = `${bottomPosition}px`

        // 设置完位置后显示
        dropdownStyle.opacity = '1'
        dropdownStyle.transition = 'opacity 0.15s'
      }
    })
  }
}

// 检查tool元素是否存在
const checkToolElementExists = () => {
  if (editor.value) {
    const toolElements = editor.value.dom.select('.non-editable.tool')
    const exists = toolElements.length > 0

    // 如果状态变化了才触发事件
    if (exists !== hasToolElement.value) {
      hasToolElement.value = exists

      if (!exists) {
        // tool元素被删除，触发自定义事件
        console.log('tool元素被删除')
        const event = new CustomEvent('tool-deleted', {
          detail: { timestamp: Date.now() },
        })
        editorContainer.value?.dispatchEvent(event)
        // 重置编辑器
        editor.value.setContent('')
        chatStore.setTool('')
        // 可以在这里添加其他操作，例如重置编辑器、显示警告等
        // 如果需要恢复tool元素，可以在这里调用恢复函数
      }
    }
  }
}

// 初始化编辑器
onMounted(() => {
  if (editorRef.value) {
    editorRef.value.id = editorId

    tinymce.init({
      selector: `#${editorId}`,
      inline: true,
      menubar: false,
      toolbar: false,
      content_css: '/tinymce/content.css',
      language_url: '/tinymce/langs/zh_CN.js',
      language: 'zh_CN',
      noneditable_class: 'non-editable',
      browser_spellcheck: true,
      contextmenu: false,
      branding: false,
      elementpath: false,
      statusbar: false,
      forced_root_block: '', // 强制将段落包装在特定块元素中，空字符串表示禁用
      placeholder: '请输入需求～',
      content_style: `
          ${contentUiCss}
          ${toolTemplateCss},
          body { 
            line-height: 1.5; 
            color: #333333;
            caret-color: #000000; 
          }
          p { 
            line-height: 1.5;  
            margin: 0 !important; 
          }
          span { 
            line-height: 1.5; 
            margin: 0 !important; 
            display: inline-block; 
            vertical-align: middle; 
          }
          * { 
            vertical-align: middle; 
          }
          
        `,
      event_root: '.tinymce-editor',
      setup: setupEditor => {
        editor.value = setupEditor
        setupEditor.on('init', () => {
          console.log('编辑器初始化完成')
          // 初始化所有真实输入框
          initRealInputs()
          // 初始化检查textarea背景
          checkAndSetTextareaBackground()
        })

        // 监听编辑器内容变化
        setupEditor.on('input', e => {
          content.value = setupEditor.getContent()
          console.log('🚀 ~ onMounted ~ setupEditor.getContent():', setupEditor.getContent())
          // 检查tool元素是否被删除
          checkToolElementExists()
          // 检查并设置textarea背景
          checkAndSetTextareaBackground()

          // 使用自定义方法获取包含输入框内容的文本
          const text = getTextContentWithInputs()
          console.log('🚀 ~ onMounted ~ text:', text)

          if (text.trim() === '/' && !chatStore?.specialCourse) {
            chatStore.showAiTool = true
          } else if (text.trim() === '' && chatStore.showAiTool) {
            // 当文本为空且AI工具面板当前显示时，关闭AI工具面板
            chatStore.showAiTool = false
          }

          // 使用新的isContentActuallyEmpty函数来更准确地判断内容是否为空
          if (chatStore.tool) {
            const formatText = text.replace(chatStore.tool, '').trim()
            console.log('格式化后的文本:', formatText)

            // 发送模板数据，包含input值，使用更准确的空内容检测
            emits('templateData', isContentActuallyEmpty(formatText), formatText)
          } else {
            emits('templateData', isContentActuallyEmpty(text), text)
          }
        })

        // 监听键盘事件
        setupEditor.on('keydown', e => {
          // 如果是Enter键并且没有按住Shift键
          if (e.keyCode === 13 && !e.shiftKey) {
            e.preventDefault() // 阻止默认的换行行为

            // 获取文本内容
            const text = getTextContentWithInputs()
            console.log('🚀 ~ onMounted ~ text:', text)

            // 如果有内容才发送
            if (text.trim()) {
              // 触发发送消息事件
              emits('sendMessage', text)

              // 清空编辑器内容
              setTimeout(() => {
                clearContent()
              }, 0)
            }

            return false
          }

          // 如果是退格键(8)或删除键(46)
          if (e.keyCode === 8 || e.keyCode === 46) {
            // 获取当前选择位置和内容长度
            const selection = setupEditor.selection.getSel()
            const contentBeforeDelete = setupEditor.getContent()

            // 延迟检查，以确保DOM已更新
            setTimeout(() => {
              // 检查内容是否改变
              const contentAfterDelete = setupEditor.getContent()
              const contentChanged = contentBeforeDelete !== contentAfterDelete

              // 检查tool元素状态
              checkToolElementExists()

              // 如果内容变化了，触发input事件确保UI状态更新
              if (contentChanged) {
                // 获取当前文本内容
                content.value = contentAfterDelete

                // 检查删除后可能的空内容情况
                const textAfterDelete = getTextContentWithInputs()

                // 通知父组件内容变更，使用更准确的空内容检测
                if (chatStore.tool) {
                  const formatText = textAfterDelete.replace(chatStore.tool, '').trim()
                  emits('templateData', isContentActuallyEmpty(formatText), formatText)
                } else {
                  emits('templateData', isContentActuallyEmpty(textAfterDelete), textAfterDelete)
                }

                // 检查并设置textarea背景
                checkAndSetTextareaBackground()
              }
            }, 0)
          }
        })

        // 检查内容是否实际为空（处理特殊字符、空白等）
        const isContentActuallyEmpty = (content: string): boolean => {
          // 移除所有空白字符
          const trimmedContent = content.trim()

          // 检查是否为空字符串
          if (trimmedContent === '') return true

          // 检查是否只包含不可见字符（零宽空格、制表符等）
          if (!/\S/.test(trimmedContent)) return true

          // 检查是否只包含tool元素（针对特定场景）
          if (chatStore.tool && trimmedContent === chatStore.tool) return true

          return false
        }

        // 监听复制粘贴事件
        setupEditor.on('paste', e => {
          // 阻止默认粘贴行为，以便我们可以自定义处理
          e.preventDefault()

          // 获取剪贴板数据
          const clipboardData = e.clipboardData || (window as any).clipboardData

          if (clipboardData && clipboardData.getData) {
            // 获取纯文本内容
            const pastedText = clipboardData.getData('text/plain')

            // 对粘贴内容进行处理（例如过滤特殊字符、格式化等）
            const processedText = pastedText.trim()

            if (processedText) {
              // 使用编辑器API插入文本
              setupEditor.insertContent(setupEditor.dom.encode(processedText))

              // 延迟触发input事件，确保UI状态更新
              setTimeout(() => {
                // 更新内容引用
                content.value = setupEditor.getContent()

                // 使用自定义方法获取实际文本内容
                const textContent = getTextContentWithInputs()

                // 使用新函数检查内容是否实际为空
                const isEmpty = isContentActuallyEmpty(textContent)

                // 触发模板数据更新，根据实际内容状态决定是否为空
                if (chatStore.tool) {
                  const formatText = textContent.replace(chatStore.tool, '').trim()
                  emits('templateData', isContentActuallyEmpty(formatText), formatText)
                } else {
                  emits('templateData', isEmpty, textContent)
                }

                // 触发编辑器内容变化事件，确保状态更新
                setupEditor.fire('input')

                // 检查tool元素是否存在
                checkToolElementExists()

                // 检查并设置textarea背景
                checkAndSetTextareaBackground()
              }, 0)
            }
          }

          // 延迟检查，以确保DOM已更新
          setTimeout(() => {
            checkToolElementExists()
          }, 0)
        })

        // 监听点击事件，处理可编辑和不可编辑元素
        setupEditor.on('click', e => {
          const clickedElement = e.target as HTMLElement
          console.log('编辑器点击事件:', clickedElement.tagName, clickedElement.className)

          // 处理textarea点击，确保正确聚焦
          if (clickedElement.tagName === 'TEXTAREA' && clickedElement.classList.contains('real-input')) {
            // 阻止编辑器的默认处理
            e.preventDefault()
            e.stopPropagation()

            // 直接聚焦到textarea，使用更长的延迟确保DOM完全更新
            setTimeout(() => {
              clickedElement.focus()
              // 设置光标位置到文本末尾
              const length = (clickedElement as HTMLTextAreaElement).value.length
                ; (clickedElement as HTMLTextAreaElement).setSelectionRange(length, length)
            }, 50)

            return
          }

          if (clickedElement.classList.contains('non-editable')) {
            e.preventDefault()
            e.stopPropagation()
            nonEditableElementClicked(clickedElement)
            showDropdownMenu(clickedElement)
          } else {
            // 点击其他地方，关闭下拉框
            showDropdown.value = false
          }
        })

        // 为移动设备添加tap事件处理
        setupEditor.on('tap', e => {
          const clickedElement = e.target as HTMLElement
          console.log('编辑器tap事件:', clickedElement.tagName, clickedElement.className)

          // 处理textarea点击，确保正确聚焦
          if (clickedElement.tagName === 'TEXTAREA' && clickedElement.classList.contains('real-input')) {
            // 阻止编辑器的默认处理
            e.preventDefault()
            e.stopPropagation()

            // 直接聚焦到textarea，使用更长的延迟确保DOM完全更新
            setTimeout(() => {
              clickedElement.focus()
              // 设置光标位置到文本末尾
              const length = (clickedElement as HTMLTextAreaElement).value.length
                ; (clickedElement as HTMLTextAreaElement).setSelectionRange(length, length)
            }, 50)

            return
          }

          if (clickedElement.classList.contains('non-editable')) {
            e.preventDefault()
            e.stopPropagation()
            nonEditableElementClicked(clickedElement)
            showDropdownMenu(clickedElement)
          } else {
            // 点击其他地方，关闭下拉框
            showDropdown.value = false
          }
        })

        // 监听节点变化事件
        setupEditor.on('NodeChange', () => {
          // 检查tool元素是否被删除
          checkToolElementExists()
        })

        // 监听焦点变化事件
        setupEditor.on('blur', e => {
          console.log('编辑器失焦事件')
          // 检查tool元素是否被删除
          checkToolElementExists()
        })
      },
      init_instance_callback: ed => {
        editor.value = ed
        console.log('编辑器完全初始化完成')
      },
    })

    // 添加全局点击事件监听
    document.addEventListener('click', handleClickOutside)
  }
})

// 在组件销毁前清理编辑器实例和事件监听
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }
  document.removeEventListener('click', handleClickOutside)
})
watch(
  () => [chatStore.tool, chatStore.assistantShow, chatStore.specialCourseConfig] as [string, boolean, ISpecialCourseConfig],
  ([newValue, newAssistantShow, newSpecialCourseConfig]: [string, boolean, ISpecialCourseConfig]) => {
    console.log("🚀 ~ newSpecialCourseConfig:", newSpecialCourseConfig)
    console.log('🚀 ~ chatStore:', chatStore)
    console.log('🚀 ~ newValue, newAssistantShow:', newValue, newAssistantShow)
    console.log('🚀 ~ newValue:', newValue)


    // 如果工具值为空且助手未显示，则不做任何处理
    if (!newValue && !newAssistantShow) {
      return
    }

    let content = ''
    if (newValue) {
      content = `<span class="non-editable tool">${newValue}</span>`
    }

    if (!newAssistantShow) {
      if (newValue) {
        content = `<span class="non-editable tool">${newValue}</span>`
      }
    } else {
      const tools = chatStore.tools?.flatMap(item => item.toolList)
      console.log("🚀 ~  chatStore.tools:", chatStore.tools)
      console.log('🚀 ~ tools:', tools)
      console.log('🚀 ~ newValue:', newValue)
      const filterTool: any = tools.find((tool: any) => tool.functionName === newValue)
      console.log('🚀 ~ filterTool:', filterTool)

      // 先添加工具名称
      if (newValue) {

        content = `<span class="non-editable tool">${newValue || newSpecialCourseConfig?.functionName}</span>`
      }

      // 然后添加配置项
      if (filterTool?.functionJson) {
        for (const config of filterTool.functionJson || []) {
          console.log('🚀 ~ config:', config)
          if (config.type === 'text') {
            content += `<span contenteditable="true">${config.text}</span>`
          } else if (config.type === 'select') {
            // 从配置中查找默认值
            // 将整个配置序列化为JSON字符串并存储在data-config属性中
            const configJson = JSON.stringify(config)
            // 默认显示config.text，不预选任何选项
            content += `<span class="non-editable" data-type="select" data-value="" data-config='${configJson}'>${config?.text}</span>`
          } else if (config.type === 'input') {
            // 使用自适应大小的textarea替代input，添加更多交互属性
            content += `
              <textarea 
                class="real-input" 
                rows="1" 
                tabindex="0"
                spellcheck="true"
                data-placeholder-length="${config.placeholder ? config.placeholder.length : 10}"
                placeholder="${config.placeholder}"
                data-mce-tabindex="0" 
                data-input-value="${config.placeholder || ''}"
              ></textarea>
            `
          }
        }
      }
    }

    // 只有当content不为空时才进行处理
    if (content) {
      // 清空编辑器内容
      clearContent()

      nextTick(() => {
        // 插入新内容
        insertHtml(content)

        nextTick(() => {
          // 初始化真实文本输入框的事件监听
          initRealInputs()
          const text = getTextContentWithInputs()
          if (chatStore.tool) {
            const formatText = text.replace(chatStore.tool, '').trim()
            console.log('格式化后的文本:', formatText)

            // 发送模板数据
            emits('templateData', formatText ? false : true, formatText)
          } else {
            emits('templateData', text ? false : true, text)
          }
        })
      })
    }
  },
  { immediate: true } // 立即执行一次watch
)
// 插入HTML内容
const insertHtml = (html: string) => {
  if (editor.value) {
    editor.value.insertContent(html)
    content.value = editor.value.getContent()
  }
}

// 插入纯文本
const insertText = (text: string) => {
  if (editor.value) {
    editor.value.insertContent(editor.value.dom.encode(text))
    content.value = editor.value.getContent()
  }
}

// 设置内容为可编辑
const setEditable = (selector: string) => {
  if (editor.value) {
    const elements = editor.value.dom.select(selector)
    elements.forEach((element: HTMLElement) => {
      element.contentEditable = 'true'
      element.classList.remove('non-editable')
    })
  }
}

// 设置内容为不可编辑
const setNonEditable = (selector: string) => {
  if (editor.value) {
    const elements = editor.value.dom.select(selector)
    elements.forEach((element: HTMLElement) => {
      element.contentEditable = 'false'
      element.classList.add('non-editable')
    })
  }
}

// 创建并插入一个不可编辑元素
const insertNonEditableElement = (text: string, data: any = {}) => {
  if (editor.value) {
    // 创建一个带有自定义数据的不可编辑元素
    const html = `<span class="non-editable" 
                    data-id="${data.id || ''}" 
                    data-type="${data.type || ''}" 
                    data-value="${data.value || ''}">${text}</span>`
    editor.value.insertContent(html)

    // 更新内容引用
    content.value = editor.value.getContent()
  }
}

// 恢复tool元素
const restoreToolElement = () => {
  if (editor.value && !hasToolElement.value) {
    // 检查编辑器内容前缀是否已包含tool元素
    const content = editor.value.getContent()
    if (!content.includes('<span class="non-editable tool">')) {
      // 在编辑器内容开头插入tool元素
      const toolHtml = '<span class="non-editable tool">/文生音乐</span>'
      editor.value.execCommand('mceInsertContent', false, toolHtml, { skip_undo: false })

      // 更新tool元素状态
      hasToolElement.value = true

      console.log('已恢复tool元素')
    }
  }
}

// 不可编辑元素点击回调
const nonEditableElementClicked = (element: HTMLElement) => {
  console.log('非编辑元素被点击了', element)

  // 提取自定义数据属性
  const elementData = {
    id: element.getAttribute('data-id'),
    type: element.getAttribute('data-type'),
    value: element.getAttribute('data-value'),
    text: element.textContent,
  }

  // 这里可以添加自定义处理逻辑
  console.log('元素数据:', elementData)

  // 如果需要，可以触发一个自定义事件
  const event = new CustomEvent('non-editable-click', {
    detail: elementData,
  })
  editorContainer.value?.dispatchEvent(event)
}

// 自动调整textarea大小的函数
const autoResizeTextarea = (textarea: HTMLTextAreaElement) => {
  if (!textarea) return

  // 保存当前滚动位置
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  // 创建一个隐藏的span用于测量文本宽度
  const measureSpan = document.createElement('span')
  measureSpan.style.visibility = 'hidden'
  measureSpan.style.position = 'absolute'
  measureSpan.style.whiteSpace = 'pre'
  measureSpan.style.font = getComputedStyle(textarea).font
  measureSpan.style.padding = '0'
  measureSpan.style.border = '0'
  measureSpan.style.boxSizing = 'content-box'

  // 获取textarea的值和placeholder
  const value = textarea.value || ''
  const placeholder = textarea.getAttribute('placeholder') || ''

  // 确定测量文本：有内容时只测量内容宽度，无内容时才使用placeholder
  const hasContent = value.trim().length > 0
  const textToMeasure = hasContent ? value : placeholder

  // 设置测量文本
  measureSpan.textContent = textToMeasure || ' ' // 至少有一个空格，防止宽度为0
  document.body.appendChild(measureSpan)

  // 获取文本宽度
  let textWidth = measureSpan.offsetWidth
  document.body.removeChild(measureSpan)

  // 加上内边距和额外空间
  const paddingLeft = parseInt(getComputedStyle(textarea).paddingLeft) || 0
  const paddingRight = parseInt(getComputedStyle(textarea).paddingRight) || 0
  const extraSpace = 20 // 增加额外空间，使输入更流畅
  const totalWidth = textWidth + paddingLeft + paddingRight + extraSpace

  // 获取编辑器容器的宽度并计算最大宽度
  let editorWidth = 300 // 默认值
  if (editorContainer.value) {
    editorWidth = editorContainer.value.offsetWidth
  }
  const maxWidth = Math.max(Math.floor(editorWidth * 0.95), 30) // 编辑器宽度的95%，但至少30px

  // 应用宽度，使用内联样式确保精确度
  const minWidth = 30 // 最小宽度
  const finalWidth = Math.min(Math.max(totalWidth, minWidth), maxWidth)

  // 平滑过渡：如果宽度变化不大，使用更平滑的过渡
  const currentWidth = parseInt(textarea.style.width) || 0
  const widthDiff = Math.abs(finalWidth - currentWidth)

  // 如果宽度变化较大，直接设置新宽度
  if (widthDiff > 20 || !currentWidth) {
    textarea.style.width = `${finalWidth}px`
  } else {
    // 对于小的宽度变化，使用CSS过渡效果
    textarea.style.transition = 'width 0.1s ease-out'
    textarea.style.width = `${finalWidth}px`
    // 短暂延迟后移除过渡效果，避免影响下次调整
    setTimeout(() => {
      textarea.style.transition = ''
    }, 100)
  }

  // 移除之前的宽度类
  textarea.classList.remove('textarea-xs', 'textarea-sm', 'textarea-md', 'textarea-lg')

  // 处理高度调整 - 先重置高度，然后根据内容计算
  textarea.style.height = 'auto'

  // 对于多行文本，使用scrollHeight自动调整高度
  const lineCount = value.split('\n').length
  const isMultiLine = lineCount > 1 || textarea.scrollHeight > 22

  textarea.classList.remove('textarea-single-line', 'textarea-multi-line')

  if (isMultiLine) {
    textarea.classList.add('textarea-multi-line')
    textarea.setAttribute('rows', String(Math.min(lineCount, 4)))
    // 设置正确的高度以适应全部内容
    textarea.style.height = `${textarea.scrollHeight}px`
  } else {
    textarea.classList.add('textarea-single-line')
    textarea.setAttribute('rows', '1')
    textarea.style.height = '22px'
  }

  // 如果是空内容，添加额外的CSS类
  if (!value.trim()) {
    textarea.classList.add('textarea-empty')
    textarea.style.height = '22px'
  } else {
    textarea.classList.remove('textarea-empty')
  }

  // 检查并设置背景色标记
  checkAndSetTextareaBackground()

  // 恢复滚动位置，防止页面跳动
  window.scrollTo(0, scrollTop)
}

// 添加防抖函数，避免频繁调整大小导致的性能问题
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function (...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn(...args)
      timer = null
    }, delay)
  }
}

// 防抖处理的自动调整大小函数
const debouncedResize = debounce(autoResizeTextarea, 10)

// 初始化真实文本输入框的事件监听
const initRealInputs = () => {
  if (editor.value) {
    const textareas = editor.value.dom.select('textarea.real-input')
    console.log('找到真实textarea数量:', textareas.length)

    textareas.forEach((textarea: HTMLTextAreaElement, index: number) => {
      // 为每个textarea添加唯一标识
      if (!textarea.hasAttribute('data-input-id')) {
        const inputId = `textarea-${Date.now()}-${Math.floor(Math.random() * 1000)}-${index}`
        textarea.setAttribute('data-input-id', inputId)

        // 创建数据属性存储器
        textarea.setAttribute('data-value', textarea.value || '')

        // 初始化自动调整大小
        autoResizeTextarea(textarea)
      }

      // 添加直接的click事件处理，确保聚焦
      editor.value.dom.unbind(textarea, 'click')
      editor.value.dom.bind(textarea, 'click', (e: Event) => {
        e.stopPropagation()
        e.preventDefault()
        const textareaElement = e.target as HTMLTextAreaElement
        setTimeout(() => {
          textareaElement.focus()
          // 设置光标位置到文本末尾
          const length = textareaElement.value.length
          textareaElement.setSelectionRange(length, length)
        }, 50)
      })

      // 添加mousedown事件，更早地捕获用户交互
      editor.value.dom.unbind(textarea, 'mousedown')
      editor.value.dom.bind(textarea, 'mousedown', (e: Event) => {
        e.stopPropagation()
        const textareaElement = e.target as HTMLTextAreaElement
        // 不立即阻止默认行为，允许原生的光标定位发生
        setTimeout(() => {
          textareaElement.focus()
        }, 10)
      })

      // 添加input事件监听，使用防抖处理
      editor.value.dom.unbind(textarea, 'input')
      editor.value.dom.bind(textarea, 'input', (e: Event) => {
        const textareaElement = e.target as HTMLTextAreaElement
        console.log('textarea值变化:', textareaElement.value)

        // 更新数据属性值
        textareaElement.setAttribute('data-value', textareaElement.value)

        // 使用防抖处理的自动调整大小
        debouncedResize(textareaElement)

        // 触发编辑器内容变化
        editor.value.fire('input')
      })

      // 添加keydown事件，实时响应特殊按键
      editor.value.dom.unbind(textarea, 'keydown')
      editor.value.dom.bind(textarea, 'keydown', (e: KeyboardEvent) => {
        const textareaElement = e.target as HTMLTextAreaElement

        // 对于回车键和删除键，立即调整大小而不等待防抖
        if (e.key === 'Enter' || e.key === 'Backspace' || e.key === 'Delete') {
          // 使用setTimeout确保在DOM更新后调整大小
          setTimeout(() => {
            autoResizeTextarea(textareaElement)
          }, 0)
        }
      })

      // 添加focus事件监听，确保自动调整大小
      editor.value.dom.unbind(textarea, 'focus')
      editor.value.dom.bind(textarea, 'focus', (e: Event) => {
        const textareaElement = e.target as HTMLTextAreaElement
        // 自动调整大小
        autoResizeTextarea(textareaElement)
      })
    })

    // 如果只有一个textarea，自动聚焦它
    if (textareas.length === 1) {
      setTimeout(() => {
        const textareaElement = textareas[0] as HTMLTextAreaElement
        textareaElement.focus()
        // 设置光标位置到文本末尾
        const length = textareaElement.value.length
        textareaElement.setSelectionRange(length, length)
      }, 100)
    }
  }
}

// 检查编辑器内容是否只有textarea，没有select和普通文本
const checkAndSetTextareaBackground = () => {
  if (editor.value) {
    const editorContent = editor.value.getContent()
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = editorContent

    // 查找所有非textarea元素（非tool类的span元素）
    const nonTextareaElements = tempDiv.querySelectorAll('span:not(.tool)')
    const textareas = tempDiv.querySelectorAll('textarea.real-input')

    // 如果只有textarea和tool元素
    const hasOnlyTextareas = nonTextareaElements.length === 0 && textareas.length > 0

    // 在实际DOM中设置或移除类
    if (editor.value) {
      const realTextareas = editor.value.dom.select('textarea.real-input')
      realTextareas.forEach((textarea: HTMLTextAreaElement) => {
        if (hasOnlyTextareas) {
          textarea.classList.add('no-background')
        } else {
          textarea.classList.remove('no-background')
        }
      })
    }
  }
}

// 自定义方法获取包含输入框值的文本内容
const getTextContentWithInputs = () => {
  if (editor.value) {
    // 创建临时DOM以保持内容结构
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = editor.value.getContent()

    // 查找所有textarea并替换为其值
    const textareas = tempDiv.querySelectorAll('textarea.real-input')
    textareas.forEach((textarea: Element) => {
      if (textarea instanceof HTMLTextAreaElement) {
        // 获取textarea的值
        const value = textarea.getAttribute('data-value') || textarea.value || ''

        // 创建文本节点替换textarea
        const textNode = document.createTextNode(value)
        if (textarea.parentNode) {
          textarea.parentNode.replaceChild(textNode, textarea)
        }
      }
    })

    // 查找所有下拉框元素并处理其选择值
    const selectElements = tempDiv.querySelectorAll('span[data-type="select"]')
    selectElements.forEach((selectElement: Element) => {
      if (selectElement instanceof HTMLElement) {
        // 获取下拉框的选择值和显示文本
        const value = selectElement.getAttribute('data-value') || ''
        const displayText = selectElement.textContent || ''

        // 如果有选择值，使用选择项的显示文本
        // 如果没有选择值，保留原始文本（占位符文本）
        // 不需要做任何特殊处理，因为下拉框选择后，其textContent已经被更新为选择项的label
      }
    })

    // 处理可能的非正常空白符（如零宽空格、换行符等）
    let textContent = tempDiv.textContent || ''

    // 规范化空白符，将多个连续空白合并为一个
    textContent = textContent.replace(/\s+/g, ' ').trim()

    // 检测和处理可能的特殊情况，如只包含不可见字符
    if (textContent.length > 0 && !/\S/.test(textContent)) {
      // 如果只包含空白字符，返回空字符串
      return ''
    }

    return textContent
  }
  return ''
}

// 监听编辑器内容变化
watch(
  () => content.value,
  () => {
    // 检查并设置textarea背景
    nextTick(() => {
      checkAndSetTextareaBackground()
    })
  }
)

// 获取编辑器内容（包括textarea值）
const getContent = () => {
  if (editor.value) {
    // 获取基本内容
    const baseContent = editor.value.getContent()

    // 创建临时DOM解析内容
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = baseContent

    // 查找所有textarea元素并确保它们的值被保存
    const textareas = tempDiv.querySelectorAll('textarea.real-input')
    textareas.forEach((textarea: Element) => {
      if (textarea instanceof HTMLTextAreaElement) {
        // 将textarea的值设置为value属性
        const value = textarea.getAttribute('data-value') || textarea.value || ''
        textarea.textContent = value
        textarea.setAttribute('value', value)
      }
    })

    // 查找所有下拉框元素并确保它们的选择值被保存
    const selectElements = tempDiv.querySelectorAll('span[data-type="select"]')
    selectElements.forEach((selectElement: Element) => {
      if (selectElement instanceof HTMLElement) {
        // 下拉框元素的textContent已经是选择项的label，不需要额外处理
        // 确保data-value属性被保留
        const value = selectElement.getAttribute('data-value') || ''
        if (value) {
          selectElement.setAttribute('data-value', value)
        }
      }
    })

    // 返回包含textarea值的内容
    return tempDiv.innerHTML
  }
  return content.value
}

// 设置编辑器内容
const setContent = (html: string) => {
  if (editor.value) {
    editor.value.setContent(html)
    content.value = html
    nextTick(() => {
      initRealInputs()
      checkToolElementExists()
      checkAndSetTextareaBackground()
    })
  }
}

// 清空编辑器内容
const clearContent = () => {
  if (editor.value) {
    editor.value.setContent('')
    content.value = ''
    hasToolElement.value = false
  }
}

const addRecordingText = (text: string) => {
  if (editor.value) {
    editor.value.insertContent(text)
    content.value = editor.value.getContent()
    console.log('🚀 ~ addRecordingText ~ text:', text)
  }
}

// 向外部暴露的方法
defineExpose({
  insertHtml,
  insertText,
  setEditable,
  setNonEditable,
  insertNonEditableElement,
  getContent,
  setContent,
  clearContent,
  getEditor: () => editor.value,
  restoreToolElement,
  checkToolElementExists,
  addRecordingText,
  getTextContentWithInputs,
})
</script>

<style lang="scss">
/* 全局样式，专门用于teleport后的dropdown */
.teleport-dropdown-menu {
  position: absolute;
  z-index: 9999;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  // min-width: 138px;
  max-height: 223px;
  overflow-y: auto;
  overflow-x: hidden;
  opacity: 1;
  transition: opacity 0.15s;
  left: 0;
  bottom: 0;
  border: 1px solid #ebeef5;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 2px;
  }

  .dropdown-item {
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;

    .select-index {
      color: #fff;
      display: inline-flex;
      width: 16px;
      height: 16px;
      min-width: 16px;
      /* 防止压缩 */
      background: #2967f3;
      border-radius: 4px;
      text-align: center;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      font-weight: normal;
    }

    .select-label {
      margin-left: 8px !important;
      margin-right: 6px !important;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .icon-check {
      width: 16px;
      height: 16px;
    }

    // &:hover {
    //   background-color: #f5f7fa;
    // }

    &.is-header {
      font-weight: 500;
      color: #999;
      font-size: 13px;
      padding: 6px 12px;
      cursor: default;

      &:hover {
        background-color: transparent;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.tool-template {
  width: 100%;
  border-radius: 4px;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  .tinymce-editor {
    width: 100%;
    outline: none;
    overflow-wrap: break-word;
    word-wrap: break-word;
    font-size: 14px;
    line-height: 1.5;
    /* 调整与textarea一致 */
    word-break: break-all;

    /* 增加行间距 */
    div,
    p,
    span {
      margin-top: 2px;
      margin-bottom: 2px;
      display: inline-block;
      vertical-align: middle;
      line-height: 1.5;
      /* 确保所有内容行高一致 */
    }

    /* 为真实textarea添加样式 */
    :deep(textarea.real-input) {
      /* 基本样式 */
      font-size: 14px;
      // line-height: 1.8;
      line-height: 1.5;
      padding: 2px 4px;
      border: none;
      border-radius: 4px;
      color: #333333;
      /* 更改文本颜色为更深的黑色 */
      background-color: rgba(0, 87, 255, 0.06);
      transition: border-color 0.2s, height 0.15s, width 0.15s ease-out, background-color 0.2s;
      margin: 2px 4px;
      /* 添加水平和垂直方向间距 */

      /* 交互相关样式 */
      resize: none;
      overflow: hidden;
      min-height: 22px;
      /* 调整与行高1.5匹配 */
      min-width: 30px;
      max-width: 100%;
      display: inline-block;
      vertical-align: middle;
      /* 确保垂直居中对齐 */
      box-sizing: border-box;
      outline: none;
      cursor: text !important;
      position: relative;
      z-index: 2;
      caret-color: #000000;
      /* 设置光标颜色为纯黑色 */

      /* 交互状态样式 */
      &:focus {
        background-color: rgba(0, 87, 255, 0.08);
      }

      &::placeholder {
        color: #c0c4cc;
        transition: opacity 0.2s;
      }

      /* 无背景色类 - 当编辑器中只有textarea时应用 */
      &.no-background {
        background-color: transparent;

        &:focus {
          background-color: rgba(0, 87, 255, 0.03);
        }
      }

      /* 高度类 */
      &.textarea-single-line {
        min-height: 22px;
        /* 调整与行高1.5匹配 */
        height: 22px;
      }

      &.textarea-multi-line {
        min-height: 22px;
      }

      &.textarea-empty {
        height: 22px;
      }
    }

    /* 为不可编辑元素添加样式 */
    :deep(.non-editable) {
      position: static;
      /* 改为static解决光标问题 */
      cursor: pointer;
      color: #000000;
      padding: 0px 9px;
      background: #e5e8f0;
      border-radius: 4px;
      display: inline-flex;
      /* 改为inline-flex，按内容宽度显示不换行 */
      align-items: center;
      vertical-align: middle;
      margin: 2px 4px;
      transition: background-color 0.2s;

      /* 使用伪元素作为三角形，但采用内联元素方式 */
      &:not(.tool)::after {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        margin-left: 4px;
        margin-right: 2px;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 5px solid #333333;
        transition: transform 0.2s ease;
      }

      /* 当下拉框显示时，添加活跃状态 */
      &.active {
        background-color: #e5e8f0;

        /* 箭头向上翻转 */
        &:not(.tool)::after {
          transform: rotate(180deg);
        }
      }

      /* 排除特定的tool元素样式 */
      &.tool {
        padding: 0 4px;
        margin: 2px 4px;
        background-color: #fff;
        display: inline-block;
        /* 恢复默认显示方式 */
        color: #2967f3;
      }
    }

    /* 添加普通文本元素的对齐样式 */
    :deep(span:not(.non-editable)) {
      display: inline-block;
      vertical-align: middle;
      line-height: 1.5;
      /* 调整与textarea一致 */
    }
  }
}
</style>
