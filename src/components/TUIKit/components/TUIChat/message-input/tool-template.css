/* 
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-14 17:15:00
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-05-22 18:13:40
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/message-input/tool-template.css
 * @Description: TinyMCE编辑器自定义样式
*/

/* 非编辑元素样式 */
body {
  font-size: 16px;
}
.non-editable {
  padding: 2px 8px;
  margin: 0 2px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  display: inline-block;
}

/* 修复光标显示问题的样式覆盖 */
.tool-template .tinymce-editor .non-editable {
  position: static !important; /* 覆盖原有的position:relative */
}

/* 模拟输入框样式 */
.simulated-input {
  background: rgba(0, 102, 255, 0.06);
  border-radius: 10px;
  color: #06f;
  display: inline-block;
  /* font-weight: 600; */
  /* line-height: 150%; */
  margin: 2px 3px;
  padding: 2px 6px;
  word-break: break-word;
  min-width: 160px;
  min-height: 24px;
  position: relative;
}

/* 使用after伪元素显示placeholder */
.simulated-input.placeholder-visible::after {
  content: attr(data-placeholder);
  color: #0057ff;
  opacity: 0.5;
  position: absolute;
  left: 6px;
  top: 2px;
  pointer-events: none;
}

/* 编辑器内容的样式 */
.tinymce-editor {
  /* 基础样式 */
  font-size: 14px;
  line-height: 1.5;
  padding: 0;
  margin: 0;
}

/* 增加行间距 */
/* .tinymce-editor div, */
/* .tinymce-editor p {
  margin-bottom: 8px !important;
  padding-bottom: 3px;
} */

/* 虚拟工具元素样式 */
.mce-content-body [contentEditable='false'][data-mce-selected] {
  outline: none;
  cursor: pointer;
}

/* 占位符样式 */
.mce-content-body *[data-mce-placeholder] {
  cursor: text;
  color: #999;
}

/* 文本输入框样式 */
textarea.real-input {
  margin: 3px 4px;
  vertical-align: middle;
}
