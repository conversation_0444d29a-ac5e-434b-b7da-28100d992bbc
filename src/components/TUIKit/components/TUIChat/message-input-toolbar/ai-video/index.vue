<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 14:51:29
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-02 10:48:53
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/message-input-toolbar/clear-message/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-icon class="message-ai-video" @click="handleClick"><Message /></el-icon>
</template>
  <script setup lang="ts">
import { inject, computed } from 'vue'
import { useRoute } from 'vue-router'
import useChatStore from '../../../../../../store/modules/chat'
const chatStore = useChatStore()
console.log('🚀 ~ chatStore:', chatStore)

async function handleClick() {
  chatStore.setShowAiVideo(true)
}
</script>
  <style scoped lang="scss" \>
.message-ai-video {
  border: none;
  margin: 0 10px;
  font-size: 20px;
  // cursor: not-allowed;
  cursor: pointer;
}
// .del-disabled {
//   border: none;
//   margin: 0 10px;
//   font-size: 20px;
//   cursor: not-allowed;
// }
</style>
  