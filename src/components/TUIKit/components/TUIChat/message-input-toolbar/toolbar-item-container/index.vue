<template>
  <div ref="toolbarItemRef" :class="['toolbar-item-container', !isPC && 'toolbar-item-container-h5', isUniFrameWork && 'toolbar-item-container-uni']">
    <div :class="['custom-icon', 'toolbar-item-container-icon', isUniFrameWork && 'toolbar-item-container-uni-icon']" @click="toggleToolbarItem">
      <Icon v-if="props.type === 'icon'" :file="props.iconFile" class="icon" :width="props.iconWidth" :height="props.iconHeight" />
      <div v-if="props.type === 'btn'" class="custom-ai-upload">上传图片</div>
    </div>
    <div v-if="isUniFrameWork" :class="['toolbar-item-container-uni-title']">
      {{ props.title }}
    </div>
    <div
      v-show="showDialog"
      ref="dialogRef"
      :class="['toolbar-item-container-dialog', isDark && 'toolbar-item-container-dialog-dark', !isPC && 'toolbar-item-container-h5-dialog', isUniFrameWork && 'toolbar-item-container-uni-dialog']"
    >
      <BottomPopup v-if="props.needBottomPopup && !isPC" class="toolbar-bottom-popup" :show="showDialog" @touchmove.stop.prevent @onClose="onPopupClose">
        <slot />
      </BottomPopup>
      <slot v-else />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from '../../../../adapter-vue'
import { outsideClick } from '@tencentcloud/universal-api'
import Icon from '../../../common/Icon.vue'
import BottomPopup from '../../../common/BottomPopup/index.vue'
import { isPC, isUniFrameWork } from '../../../../utils/env'
import TUIChatConfig from '../../config'

const props = defineProps({
  iconFile: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  needDialog: {
    type: Boolean,
    default: true,
  },
  iconWidth: {
    type: String,
    default: '20px',
  },
  iconHeight: {
    type: String,
    default: '20px',
  },
  // Whether to display the bottom popup dialog on mobile devices
  // Invalid on PC
  needBottomPopup: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'icon',
  },
})

const emits = defineEmits(['onIconClick', 'onDialogClose', 'onDialogShow'])

const isDark = ref(TUIChatConfig.getTheme() === 'dark')
const showDialog = ref(false)
const toolbarItemRef = ref()
const dialogRef = ref()

const toggleToolbarItem = () => {
  emits('onIconClick', dialogRef)
  if (isPC) {
    outsideClick.listen({
      domRefs: toolbarItemRef.value,
      handler: closeToolbarItem,
    })
  }
  if (!props.needDialog) {
    return
  }
  toggleDialogDisplay(!showDialog.value)
}

const closeToolbarItem = () => {
  showDialog.value = false
  emits('onDialogClose', dialogRef)
}

const toggleDialogDisplay = (showStatus: boolean) => {
  if (showDialog.value === showStatus) {
    return
  }
  showDialog.value = showStatus
  switch (showStatus) {
    case true:
      emits('onDialogShow', dialogRef)
      break
    case false:
      emits('onDialogClose', dialogRef)
  }
}

const onPopupClose = () => {
  showDialog.value = false
}

defineExpose({
  toggleDialogDisplay,
})
</script>
<style lang="scss" scoped src="./style/index.scss"></style>
<style lang="scss" scoped>
.custom-icon {
  width: 100%;
  height: 100%;
}
.custom-ai-upload {
  display: inline-block;
  padding: 8px 16px;
  margin-top: 12px;
  background-color: #409eff;
  color: #ffffff;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50px;
  &:hover {
    background-color: #66b1ff;
  }

  &:active {
    background-color: #3a8ee6;
  }
}
</style>
