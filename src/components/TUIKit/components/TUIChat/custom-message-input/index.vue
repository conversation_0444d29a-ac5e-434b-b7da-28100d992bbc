<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-26 11:19:39
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-27 16:22:14
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/custom-message-input/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="custom-message-input" @click.stop>
    <span @click="focus">{{ toolTile }}</span
    ><input ref="inputRef" type="text" class="custom-message-input-input" placeholder="描述你的创作需求" v-model="inputValue" @keydown="handleDown" @input="handleInput" @click.stop />
  </div>
</template>

<script setup lang="ts">
import useChatStore from '@/store/modules/chat'
import { computed, ref, VNodeRef, watch } from 'vue'
const chatStore = useChatStore()
const inputRef = ref<VNodeRef | null>(null)

const props = defineProps<{
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const inputValue = ref(props.modelValue || '')

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    inputValue.value = newVal || ''
  }
)

const toolTile = computed(() => {
  return '/' + chatStore.toolList.find(item => item.value === chatStore.chooseTool)?.title
})

const handleInput = () => {
  emit('update:modelValue', inputValue.value)
}

const focus = () => {
  inputRef.value.focus()
}

const handleDown = (e: { keyCode: number }) => {
  if (e.keyCode === 8) {
    if (inputValue.value === '') {
      chatStore.setShowAiVideo(false)
    }
  }
}

// 暴露清空方法
const clear = () => {
  inputValue.value = ''
  emit('update:modelValue', '')
}

// 暴露组件方法
defineExpose({
  clear,
})
</script>

<style scoped lang="scss">
.custom-message-input {
  width: 100%;
  height: 100%;
  display: flex;
  justify-items: center;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 31px;
  background: #ffffff;
  background-image: linear-gradient(#fff, #fff), linear-gradient(to right, #4facfe, #00f2fe);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  span {
    white-space: nowrap;
    display: block;
    height: 100%;
    line-height: 1;
    text-align: center;
    font-size: 19px;
    color: #2196f3;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 5px;
    padding: 0 0 0 22px;
  }
  input {
    flex: 1;
    height: 100%;
    outline: none;
    font-size: 19px;
    padding: 0 27px 0 0;
    background: transparent;
    border: none;
    overflow: hidden;
  }
}
</style>
