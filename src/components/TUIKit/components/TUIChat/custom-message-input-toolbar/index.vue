<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-25 17:00:20
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-27 16:18:33
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/custom-message-input-toolbar/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="custom-message-input-toolbar">
    <div class="item-box">
      <div class="item-box-item" v-for="(item, index) in chatStore.toolList" :key="index" :class="{ active: chatStore.chooseTool === item.value }" @click.stop="handleItemClick(item.value)">
        <div class="icon">
          <!-- <img src="@/assets/images/text-to-video.png" alt="icon" /> -->
          <img :src="chatStore.chooseTool === item.value ? item.activeIcon : item.icon" alt="icon" />
        </div>
        <div class="item-box-item-text">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import textToVideo from '@/assets/images/text-to-video.png'
import textToVideoActive from '@/assets/images/text-to-video-active.png'
import imageToVideo from '@/assets/images/image-to-video.png'
import imageToVideoActive from '@/assets/images/image-to-video-active.png'
import cankaoToVideo from '@/assets/images/cankao-to-video.png'
import cankaoToVideoActive from '@/assets/images/cankao-to-video-active.png'
import useChatStore from '@/store/modules/chat'

const chatStore = useChatStore()

const handleItemClick = (value: string) => {
  chatStore.setChooseTool(value)
}
</script>

<style scoped lang="scss">
.custom-message-input-toolbar {
  width: 100%;

  .item-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    .item-box-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      padding: 9px 12px 9px 8px;
      background: #ffffff;
      border-radius: 5px;
      margin-right: 13px;
      margin-bottom: 13px;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: linear-gradient(-90deg, #2abef6, #b183fe);
        .item-box-item-text {
          color: #ffffff;
        }
      }

      .icon {
        width: 18px;
        height: 18px;
        img {
          width: 100%;
        }
      }
      .item-box-item-text {
        font-weight: normal;
        font-size: 16px;
        color: #000000;
        margin-left: 13px;
      }
    }
  }
}
</style>
