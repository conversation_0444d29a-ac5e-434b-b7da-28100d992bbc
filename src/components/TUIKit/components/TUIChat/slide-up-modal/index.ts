/**
 * SlideUpModal 滑动弹窗组件
 *
 * 一个从屏幕底部向上滑动出现的弹窗组件，专为TUIChat聊天界面设计
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 导入主组件
import SlideUpModalComponent from './index.vue'

// 导出主组件
export { SlideUpModalComponent as SlideUpModal }

// 导出类型定义
export type { SlideUpModalProps, SlideUpModalEmits, ModalAnimationState, ModalConfig, SlideUpModalInstance, ModalEventCallbacks, BreakpointKey } from './types'

// 导出组合式函数
export { useSlideUpModal, createModalManager, modalManager } from './useSlideUpModal'

// 导出常量
export { BREAKPOINTS } from './types'

// 默认导出主组件
export default SlideUpModalComponent
