# SlideUpModal 滑动弹窗组件

一个从屏幕底部向上滑动出现的弹窗组件，专为TUIChat聊天界面设计，具有完整的响应式支持和流畅的动画效果。

## 功能特性

- ✅ **从底部滑动**: 弹窗从屏幕底部向上滑动出现，符合移动端交互习惯
- ✅ **完全覆盖**: 弹窗完全覆盖聊天界面，提供沉浸式体验
- ✅ **关闭交互**: 右上角关闭按钮 + 点击遮罩层关闭
- ✅ **平滑动画**: 使用CSS Transition实现300ms ease-out动画
- ✅ **响应式设计**: 移动端和桌面端完美适配
- ✅ **滚动锁定**: 弹窗打开时自动锁定body滚动
- ✅ **TypeScript**: 完整的类型定义支持
- ✅ **无障碍**: 支持键盘导航和屏幕阅读器

## 技术实现

- **Vue 3 Composition API**: 使用最新的Vue 3语法
- **Teleport传送门**: 将弹窗渲染到body元素
- **CSS Transition**: 原生CSS动画，性能优异
- **SCSS预处理**: 模块化样式管理
- **响应式断点**: 支持移动端、平板、桌面端

## 基础用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <button @click="showModal = true">打开弹窗</button>

    <!-- 弹窗组件 -->
    <SlideUpModal v-model:visible="showModal" @close="handleClose">
      <div class="modal-content">
        <h2>弹窗标题</h2>
        <p>这里是弹窗内容</p>
      </div>
    </SlideUpModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SlideUpModal from './slide-up-modal/index.vue'

const showModal = ref(false)

const handleClose = () => {
  console.log('弹窗已关闭')
}
</script>
```

## Props 属性

| 属性名                | 类型      | 默认值  | 说明                       |
| --------------------- | --------- | ------- | -------------------------- |
| `visible`             | `boolean` | `false` | 控制弹窗显示/隐藏状态      |
| `closeOnOverlayClick` | `boolean` | `true`  | 是否允许点击遮罩层关闭弹窗 |

## Events 事件

| 事件名           | 参数               | 说明                   |
| ---------------- | ------------------ | ---------------------- |
| `update:visible` | `(value: boolean)` | 弹窗显示状态变化时触发 |
| `close`          | `()`               | 弹窗关闭时触发         |

## 在TUIChat中集成

### 1. 导入组件

```vue
<script setup lang="ts">
import SlideUpModal from './slide-up-modal/index.vue'
</script>
```

### 2. 添加到模板

```vue
<template>
  <div class="tui-chat">
    <!-- 现有的聊天界面内容 -->
    <ChatHeader />
    <MessageList />
    <MessageInput />

    <!-- 添加弹窗组件 -->
    <SlideUpModal v-model:visible="selectTextShow" @close="handleSelectTextClose">
      <!-- 这里放置您的选择文本界面内容 -->
      <div class="select-text-content">
        <!-- 您的内容 -->
      </div>
    </SlideUpModal>
  </div>
</template>
```

### 3. 添加响应式逻辑

```vue
<script setup lang="ts">
import { ref } from 'vue'

const selectTextShow = ref(false)

const handleSelectTextClose = () => {
  console.log('文本选择弹窗已关闭')
  // 执行其他关闭逻辑
}

// 打开弹窗的方法
const openSelectText = () => {
  selectTextShow.value = true
}
</script>
```

## 样式定制

组件使用scoped样式，如需定制可以通过CSS变量或深度选择器：

```scss
// 定制弹窗背景色
:deep(.slide-up-modal-container) {
  background: #f8f9fa;
}

// 定制关闭按钮样式
:deep(.slide-up-modal-close) {
  background: #007bff;
  color: white;
}

// 定制动画时长
:deep(.modal-enter-active),
:deep(.modal-leave-active) {
  transition: all 0.4s ease-out;
}
```

## 响应式断点

组件内置了响应式断点支持：

- **移动端** (≤480px): 优化触摸交互
- **平板端** (≤768px): 中等屏幕适配
- **桌面端** (>768px): 大屏幕体验

## 无障碍支持

- 关闭按钮包含`aria-label`属性
- 支持`prefers-reduced-motion`媒体查询
- 支持`prefers-contrast`高对比度模式
- 弹窗打开时自动管理焦点

## 性能优化

- 使用`Teleport`避免样式继承问题
- CSS动画比JavaScript动画性能更好
- 组件卸载时自动清理副作用
- 支持减少动画模式

## 浏览器兼容性

- Chrome 85+
- Firefox 85+
- Safari 14+
- Edge 85+

## 注意事项

1. 确保父组件有合适的`z-index`层级管理
2. 弹窗内容过长时会自动出现滚动条
3. 组件会自动处理body滚动锁定
4. 建议在移动端测试触摸交互体验

## 示例文件

查看 `example.vue` 文件获取完整的使用示例和最佳实践。
