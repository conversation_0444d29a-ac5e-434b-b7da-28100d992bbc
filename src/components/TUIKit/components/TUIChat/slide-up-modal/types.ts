/**
 * 滑动弹窗组件的类型定义
 */

export interface SlideUpModalProps {
  /** 控制弹窗显示/隐藏 */
  visible: boolean
  /** 是否允许点击遮罩层关闭弹窗，默认为 true */
  closeOnOverlayClick?: boolean
}

export interface SlideUpModalEmits {
  /** 更新visible状态 */
  (e: 'update:visible', value: boolean): void
  /** 弹窗关闭事件 */
  (e: 'close'): void
}

/**
 * 弹窗动画状态
 */
export type ModalAnimationState = 'entering' | 'entered' | 'leaving' | 'left'

/**
 * 弹窗配置选项
 */
export interface ModalConfig {
  /** 动画持续时间（毫秒） */
  animationDuration?: number
  /** 是否启用滚动锁定 */
  lockScroll?: boolean
  /** 最大高度（vh单位） */
  maxHeight?: number
  /** z-index层级 */
  zIndex?: number
}

/**
 * 弹窗实例方法
 */
export interface SlideUpModalInstance {
  /** 打开弹窗 */
  open: () => void
  /** 关闭弹窗 */
  close: () => void
  /** 切换弹窗状态 */
  toggle: () => void
}

/**
 * 弹窗事件回调
 */
export interface ModalEventCallbacks {
  /** 弹窗打开前回调 */
  onBeforeOpen?: () => void | Promise<void>
  /** 弹窗打开后回调 */
  onAfterOpen?: () => void
  /** 弹窗关闭前回调 */
  onBeforeClose?: () => void | Promise<void>
  /** 弹窗关闭后回调 */
  onAfterClose?: () => void
}

/**
 * 响应式断点
 */
export const BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
} as const

export type BreakpointKey = keyof typeof BREAKPOINTS
