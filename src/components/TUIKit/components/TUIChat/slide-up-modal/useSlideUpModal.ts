import { ref, computed, nextTick, onUnmounted } from 'vue'
import type { ModalConfig, ModalEventCallbacks } from './types'

/**
 * 滑动弹窗组合式函数
 * 提供弹窗状态管理、动画控制和事件处理
 */
export function useSlideUpModal(config: ModalConfig = {}, callbacks: ModalEventCallbacks = {}) {
  // 默认配置
  const defaultConfig: Required<ModalConfig> = {
    animationDuration: 300,
    lockScroll: true,
    maxHeight: 90,
    zIndex: 1000,
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 响应式状态
  const visible = ref(false)
  const isAnimating = ref(false)
  const animationState = ref<'entering' | 'entered' | 'leaving' | 'left'>('left')

  // 计算属性
  const isOpen = computed(() => visible.value)
  const canClose = computed(() => !isAnimating.value)

  // 原始body overflow值
  let originalBodyOverflow = ''

  /**
   * 锁定/解锁body滚动
   */
  const toggleBodyScroll = (lock: boolean) => {
    if (!finalConfig.lockScroll) return

    if (lock) {
      originalBodyOverflow = document.body.style.overflow
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = originalBodyOverflow
    }
  }

  /**
   * 打开弹窗
   */
  const open = async () => {
    if (visible.value || isAnimating.value) return

    try {
      // 执行打开前回调
      if (callbacks.onBeforeOpen) {
        await callbacks.onBeforeOpen()
      }

      isAnimating.value = true
      animationState.value = 'entering'
      visible.value = true

      // 锁定滚动
      toggleBodyScroll(true)

      // 等待DOM更新
      await nextTick()

      // 动画完成后的处理
      setTimeout(() => {
        isAnimating.value = false
        animationState.value = 'entered'
        
        // 执行打开后回调
        if (callbacks.onAfterOpen) {
          callbacks.onAfterOpen()
        }
      }, finalConfig.animationDuration)

    } catch (error) {
      console.error('打开弹窗时发生错误:', error)
      isAnimating.value = false
      visible.value = false
    }
  }

  /**
   * 关闭弹窗
   */
  const close = async () => {
    if (!visible.value || isAnimating.value) return

    try {
      // 执行关闭前回调
      if (callbacks.onBeforeClose) {
        await callbacks.onBeforeClose()
      }

      isAnimating.value = true
      animationState.value = 'leaving'

      // 动画完成后的处理
      setTimeout(() => {
        visible.value = false
        isAnimating.value = false
        animationState.value = 'left'
        
        // 解锁滚动
        toggleBodyScroll(false)
        
        // 执行关闭后回调
        if (callbacks.onAfterClose) {
          callbacks.onAfterClose()
        }
      }, finalConfig.animationDuration)

    } catch (error) {
      console.error('关闭弹窗时发生错误:', error)
      isAnimating.value = false
    }
  }

  /**
   * 切换弹窗状态
   */
  const toggle = () => {
    if (visible.value) {
      close()
    } else {
      open()
    }
  }

  /**
   * 强制关闭（忽略动画）
   */
  const forceClose = () => {
    visible.value = false
    isAnimating.value = false
    animationState.value = 'left'
    toggleBodyScroll(false)
    
    if (callbacks.onAfterClose) {
      callbacks.onAfterClose()
    }
  }

  /**
   * 处理ESC键关闭
   */
  const handleEscapeKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && visible.value && canClose.value) {
      close()
    }
  }

  /**
   * 处理遮罩层点击
   */
  const handleOverlayClick = (closeOnOverlayClick: boolean = true) => {
    if (closeOnOverlayClick && canClose.value) {
      close()
    }
  }

  /**
   * 获取弹窗样式
   */
  const getModalStyles = computed(() => ({
    zIndex: finalConfig.zIndex,
    '--modal-max-height': `${finalConfig.maxHeight}vh`,
    '--animation-duration': `${finalConfig.animationDuration}ms`,
  }))

  // 监听键盘事件
  const addKeyboardListeners = () => {
    document.addEventListener('keydown', handleEscapeKey)
  }

  const removeKeyboardListeners = () => {
    document.removeEventListener('keydown', handleEscapeKey)
  }

  // 组件卸载时清理
  onUnmounted(() => {
    removeKeyboardListeners()
    toggleBodyScroll(false)
  })

  return {
    // 状态
    visible,
    isOpen,
    isAnimating,
    animationState,
    canClose,

    // 方法
    open,
    close,
    toggle,
    forceClose,
    handleOverlayClick,

    // 工具
    getModalStyles,
    addKeyboardListeners,
    removeKeyboardListeners,

    // 配置
    config: finalConfig,
  }
}

/**
 * 创建全局弹窗管理器
 */
export function createModalManager() {
  const modals = new Map<string, ReturnType<typeof useSlideUpModal>>()

  const register = (id: string, modal: ReturnType<typeof useSlideUpModal>) => {
    modals.set(id, modal)
  }

  const unregister = (id: string) => {
    modals.delete(id)
  }

  const get = (id: string) => {
    return modals.get(id)
  }

  const closeAll = () => {
    modals.forEach(modal => modal.close())
  }

  const getOpenModals = () => {
    return Array.from(modals.entries())
      .filter(([, modal]) => modal.isOpen.value)
      .map(([id]) => id)
  }

  return {
    register,
    unregister,
    get,
    closeAll,
    getOpenModals,
    size: computed(() => modals.size),
  }
}

// 全局弹窗管理器实例
export const modalManager = createModalManager()
