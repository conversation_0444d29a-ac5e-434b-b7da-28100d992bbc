<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div v-if="visible" class="slide-up-modal-overlay" @click="handleOverlayClick">
        <div class="slide-up-modal-container" @click.stop>
          <!-- 弹窗头部 -->
          <div class="slide-up-modal-header">
            <div class="header-left"></div>
            <div class="header-center">
              <span class="title">选择文字</span>
            </div>
            <div class="header-right">
              <span class="close-btn" @click="handleClose">
                <img :src="closeIcon" />
              </span>
            </div>
          </div>

          <!-- 弹窗内容区域 -->
          <div class="slide-up-modal-content">
            <div class="custom-ai-message-md-container">
              <div class="custom-ai-message-md" v-html="md.render(mdInfo)"></div>
            </div>
            <slot />
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted, onUnmounted, computed } from 'vue'
import type { SlideUpModalProps, SlideUpModalEmits } from './types'
import markdownit from 'markdown-it'
import useChatStore from '@/store/modules/chat'
import { JSONToObject } from '../../../utils/index'
import closeIcon from '@/assets/images/confirm/close.png'
const props = withDefaults(defineProps<SlideUpModalProps>(), {
  closeOnOverlayClick: true,
})
const chatStore = useChatStore()
const md = markdownit({
  breaks: true,
  html: true,
  linkify: true,
  typographer: true,
})
// 提取消息内容解析逻辑
const parseMessageContent = (payload: any, from: string): string => {
  // 如果没有 payload.data，直接返回 payload.text
  if (!payload?.data) {
    return payload?.text || ''
  }

  const data = JSONToObject(payload.data)
  if (!data) {
    return payload?.text || ''
  }

  // 处理 agent 消息
  if (from?.includes('agent')) {
    return data?.text || ''
  }

  // 处理普通消息
  const cmdText = data?.content?.data?.cmd || ''
  const contentText = data?.content?.data?.text || ''
  const combinedText = cmdText + contentText

  return combinedText || payload?.text || ''
}

const mdInfo = computed(() => {
  const { payload, from } = chatStore.selectMessage || {}
  return parseMessageContent(payload, from)
})
// Enable tables in markdown-it
md.enable(['table'])
const emits = defineEmits<SlideUpModalEmits>()
// 处理关闭操作
const handleClose = () => {
  emits('update:visible', false)
  emits('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  if (props.closeOnOverlayClick) {
    handleClose()
  }
}

// 处理ESC键关闭
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 监听visible变化，处理body滚动锁定
watch(
  () => props.visible,
  newVisible => {
    nextTick(() => {
      if (newVisible) {
        // 弹窗打开时锁定body滚动
        document.body.style.overflow = 'hidden'
        // 添加键盘监听
        document.addEventListener('keydown', handleEscapeKey)
      } else {
        // 弹窗关闭时恢复body滚动
        document.body.style.overflow = ''
        // 移除键盘监听
        document.removeEventListener('keydown', handleEscapeKey)
      }
    })
  },
  { immediate: true }
)

// 组件卸载时清理
onUnmounted(() => {
  document.body.style.overflow = ''
  document.removeEventListener('keydown', handleEscapeKey)
})
</script>

<style scoped lang="scss">
.slide-up-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.slide-up-modal-container {
  width: 100%;
  max-width: 100vw;
  background: rgba(242, 245, 252, 1);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  max-height: 964px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 94%;
}

.slide-up-modal-header {
  position: relative;
  padding: 24px 25px 114px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 56px;
  user-select: none;
  .header-left,
  .header-right {
    flex: 0 0 40px; // 固定宽度确保标题居中
    display: flex;
    align-items: center;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      height: 19px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 19px;
      color: #000000;
      line-height: 19px;
      text-align: center;
    }
  }

  .header-right {
    justify-content: flex-end;

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 15px;
      height: 15px;
      cursor: pointer;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 0.5;
      }

      img {
        width: 15px;
        height: 15px;
        display: block;
      }
    }
  }
}

.slide-up-modal-close {
  position: absolute;
  top: 16px;
  right: 20px;
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;

  &:hover {
    background: #e8e8e8;
    color: #333;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.slide-up-modal-content {
  flex: 1;
  overflow-y: auto;
  .custom-ai-message-md-container {
    width: 100%;
    max-width: 100%;
    padding: 0 40px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #000000;
    line-height: 29px;
    // Style for the markdown content
    :deep(.custom-ai-message-md) {
      width: 100%;

      // Table container with horizontal scrolling
      table {
        border-collapse: collapse;
        width: auto; // Allow table to be sized by content
        margin: 12px 0;
        font-size: 14px;
        display: block;
        overflow-x: auto; // Enable horizontal scrolling
        white-space: nowrap; // Keep table headers and content on one line
        max-width: 100%;
        // max-height: 500px;
      }

      th,
      td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
        word-break: break-word;
        max-width: 500px;
        width: min-content;
        white-space: normal;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-all;
        -webkit-hyphens: auto;
        -moz-hyphens: auto;
        -ms-hyphens: auto;
        hyphens: auto;
      }

      th {
        background-color: #dce3f3;
        color: #333;
        font-weight: 500;
        position: sticky; // Make headers sticky
        top: 0;
        z-index: 1;
      }

      tr:nth-child(even) {
        background-color: #f2f5fc;
      }

      tr:hover {
        background-color: #f2f5fc;
      }
    }
  }

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // Firefox滚动条样式
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

// 动画效果
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease-out;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .slide-up-modal-container,
.modal-leave-to .slide-up-modal-container {
  transform: translateY(100%);
}

.modal-enter-to .slide-up-modal-container,
.modal-leave-from .slide-up-modal-container {
  transform: translateY(0);
}

// 确保动画流畅
.slide-up-modal-container {
  transition: transform 0.3s ease-out;
}
</style>
