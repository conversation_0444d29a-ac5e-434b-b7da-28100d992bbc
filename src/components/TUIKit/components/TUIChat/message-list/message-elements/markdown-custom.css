/* Markdown容器样式 */
.info-container,
.success-container,
.warning-container,
.danger-container {
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid;
  border-radius: 4px;
}

.info-container {
  background-color: #e8f4fd;
  border-color: #2196f3;
}

.success-container {
  background-color: #e8f5e9;
  border-color: #4caf50;
}

.warning-container {
  background-color: #fffde7;
  border-color: #ff9800;
}

.danger-container {
  background-color: #ffebee;
  border-color: #f44336;
}

/* 图片注释样式 */
figure.figure {
  margin: 16px 0;
  text-align: center;
}

figcaption {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

/* 数学公式样式 */
.katex-display {
  margin: 16px 0;
  overflow-x: auto;
}

/* 任务列表样式 */
.task-list-item {
  list-style-type: none;
  margin-left: -20px;
}

.task-list-item-checkbox {
  margin-right: 8px;
}

/* 链接样式 */
a {
  color: #2266ff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 代码块样式 */
pre {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
}

code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

th, td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #ddd;
}

th {
  background-color: #f5f7fa;
}

/* 脚注样式 */
.footnote {
  font-size: 13px;
  color: #666;
}

.footnote-ref {
  font-size: 12px;
  vertical-align: super;
}

.footnote-backref {
  text-decoration: none;
}

/* 定义列表样式 */
dl {
  margin: 16px 0;
}

dt {
  font-weight: bold;
}

dd {
  margin-left: 20px;
  margin-bottom: 8px;
}

/* 缩写样式 */
abbr {
  cursor: help;
  border-bottom: 1px dotted #666;
} 