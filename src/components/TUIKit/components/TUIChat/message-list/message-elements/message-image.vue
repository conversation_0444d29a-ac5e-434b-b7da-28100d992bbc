<!-- 废弃了 -->

<template>
  <div ref="skeletonDomRef" class="image-container" style="width: 300px; height: auto" @click.self="toggleShow">
    <!-- <img :class="['message-image', !isPC && 'message-image-h5']" :src="props.content.url" :width="props.content.width" :height="props.content.height" /> -->
    <IMImagePreview :src="imageUrl || src" @edit="handleEdit" @save="handleSave" @quote="handleQuote" />
    <div v-if="!imageUrl" class="image-loading">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from '../../../../adapter-vue'
import { IMessageModel, TUIChatService } from '@tencentcloud/chat-uikit-engine'
import { isPC } from '../../../../utils/env'

import { handleSkeletonSize } from '../../utils/utils'
import type { IImageMessageContent } from '../../../../interface'
import IMImagePreview from '@/components/IMImagePreview/index.vue'
import { ElMessage } from 'element-plus'
import useChatStore from '@/store/modules/chat'
import { jsbridge } from 'msb-public-library'
import { saveMediaViaJSBridge, addMessageToQuoteList, saveMediaInElectron } from '@/utils/messageUtils'
import { isElectron, isMobileNative } from '../../../../utils/env'

const chatStore = useChatStore()
const emits = defineEmits(['previewImage'])

// 直接定义props，避免默认值引起的类型错误
const props = defineProps<{
  content: IImageMessageContent
  messageItem: IMessageModel
  imageUrl: string
}>()

const skeletonDomRef = ref()
const imageCutDiagramRef = ref(null)
const src = ref(props.content?.url || '')
const isLoading = ref(!src.value && !props.imageUrl)

onMounted(() => {
  if (props.messageItem?.status === 'success' || props.messageItem?.status === 'fail' || props.messageItem?.progress === 1) {
    autoFixSkeletonSize()
  }
})

watch(
  () => props.content?.height,
  (newVal, oldVal) => {
    if (newVal && oldVal && newVal > oldVal) {
      autoFixSkeletonSize()
    }
  }
)

// 监听src和imageUrl变化
watch([() => src.value, () => props.imageUrl], ([newSrc, newImageUrl]) => {
  isLoading.value = !newSrc && !newImageUrl
})

const handleSave = () => {
  console.log('保存', props.messageItem)

  // 检查当前环境
  if (isElectron()) {
    // 如果是Electron环境，使用Electron专用的保存方法
    console.log('使用Electron方式保存图片')
    saveMediaInElectron(props.messageItem)
  } else if (isMobileNative()) {
    // 如果是移动端原生环境(Android/iOS)，使用JSBridge保存
    console.log('使用原生JSBridge方式保存图片')
    saveMediaViaJSBridge(props.messageItem)
  } else {
    // 其他环境，回退到JSBridge方法，或者可以实现浏览器下载
    console.log('使用默认方式保存图片')
    saveMediaViaJSBridge(props.messageItem)
  }
}

const handleQuote = () => {
  console.log('引用1', props.messageItem)
  addMessageToQuoteList(props.messageItem)
}

const handleEdit = () => {
  console.log('编辑', props.messageItem)
  chatStore.setAiCutoutsShow(true)
  chatStore.setAiCutoutsSrc(props.messageItem?.payload?.imageInfoArray[0]?.imageUrl)
}

function autoFixSkeletonSize() {
  const { width = 0, height = 0 } = props.content || {}
  if (width === 0 || height === 0) return
  const containerWidth = document.getElementById('app')?.clientWidth || 0
  const max = !isPC ? Math.min(containerWidth - 180, 300) : 300
  const size = handleSkeletonSize(width, height, max, max)
  skeletonDomRef?.value?.style && (skeletonDomRef.value.style.width = `${size.width}px`)
  skeletonDomRef?.value?.style && (skeletonDomRef.value.style.height = `${size.height}px`)
}

function toggleShow() {
  if (props.messageItem?.status === 'success' || props.messageItem?.progress === 1) {
    emits('previewImage', props.messageItem)
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/styles/common';

.image-container {
  overflow: hidden;
  background-color: #f4f4f4;
  border-radius: 18px;
  border: 2px solid #2266ff;
  position: relative;

  .message-image {
    max-width: min(calc(100vw - 180px), 300px);
    max-height: min(calc(100vw - 180px), 300px);
    width: inherit;
    height: inherit;

    &-h5 {
      max-width: calc(100vw - 180px);
      max-height: calc(100vw - 180px);
    }
  }

  .image-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.05);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 10px;
    z-index: 2;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.8);
      border-top: 3px solid #2266ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      backdrop-filter: blur(2px);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 10px;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
