/** * @description 视频消息组件 * 用于在聊天界面中显示和播放视频消息 * 支持PC端和移动端不同的显示和交互方式 * 包含视频预览和全屏播放功能 */
<template>
  <!-- 视频消息容器 -->
  <div class="message-video">
    <!-- 视频预览框 -->
    <div ref="skeleton" class="message-video-box" :class="[!isPC && 'message-video-cover']">
      <!-- 视频播放器容器 -->
      <div v-if="isInitialized" class="video-container" :style="containerStyle">
        <div v-if="!isVideoLoaded" class="video-loading" :style="{ backgroundImage: `url(${poster})` }">
          <div class="loading-spinner"></div>
        </div>
        <VideoPlayer
          v-show="isVideoLoaded"
          class="message-img"
          :class="[!isPC ? 'video-h5' : 'video-web']"
          :message="props.messageItem"
          :src="props.messageItem?.payload?.remoteVideoUrl"
          :poster="poster"
          :fluid="true"
          :playable="props.messageItem.status === 'success'"
          @loaded="handleVideoLoaded"
        />
      </div>
    </div>

    <!-- 移动端全屏播放弹窗 -->
    <div v-if="isShow && !isPC" class="dialog-video">
      <div class="dialog-video-close" @click.stop="toggleVideoPreviewer">
        <img :src="closeSVG" class="close-icon" />
      </div>
      <div class="dialog-video-box" :class="[!isPC ? 'dialog-video-h5' : '']">
        <VideoPlayer
          :message="props.messageItem"
          :src="props.messageItem?.payload?.remoteVideoUrl"
          :poster="poster"
          :fluid="true"
          :playable="props.messageItem.status === 'success'"
          class="video-player"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watchEffect, withDefaults } from '../../../../adapter-vue'
import { IMessageModel } from '@tencentcloud/chat-uikit-engine'
import { handleSkeletonSize } from '../../utils/utils'
import closeSVG from '../../../../assets/icon/icon-close.svg'
import type { IVideoMessageContent } from '../../../../interface'
import { isPC } from '../../../../utils/env'
import VideoPlayer from '@/components/VideoPlayer/index.vue'
import { getVideoBase64 } from '@/components/TUIKit/utils/tool'

interface ContainerSize {
  width: number
  height: number
}

// 组件属性定义
const props = withDefaults(
  defineProps<{
    content: IVideoMessageContent
    messageItem: IMessageModel
  }>(),
  {
    content: () => ({}) as IVideoMessageContent,
    messageItem: () => ({}) as IMessageModel,
  }
)

// 响应式状态定义
const isShow = ref(false)
const poster = ref('')
const posterWidth = ref(0)
const posterHeight = ref(0)
const skeleton = ref<HTMLElement | null>(null)
const containerSize = ref<ContainerSize>({ width: 0, height: 0 }) // 初始尺寸设为0
const isVideoLoaded = ref(false)
const isInitialized = ref(false)

// 计算属性
const containerStyle = computed(() => ({
  width: `${containerSize.value.width}px`,
  height: `${containerSize.value.height}px`,
  opacity: isInitialized.value ? 1 : 0, // 在初始化完成前隐藏容器
}))

// 处理视频加载完成事件
const handleVideoLoaded = () => {
  isVideoLoaded.value = true
}

// 初始化视频尺寸和封面图
const initializeVideo = async () => {
  if (!props.content?.url) return

  try {
    // 获取视频第一帧作为封面图
    poster.value = await getVideoBase64(props.content.url)

    // 计算容器尺寸
    const containerWidth = document.getElementById('messageScrollList')?.clientWidth || 0
    const max = !isPC ? Math.min(containerWidth - 172, 300) : 300

    // 使用获取到的封面图尺寸计算容器大小
    const dimensions = handleSkeletonSize(posterWidth.value, posterHeight.value, max, max)

    // 更新容器尺寸
    if (skeleton.value) {
      skeleton.value.style.width = `${dimensions.width}px`
      skeleton.value.style.height = `${dimensions.height}px`
    }
    containerSize.value = { width: dimensions.width, height: dimensions.height }

    // 标记初始化完成
    isInitialized.value = true

    // 如果视频上传成功，预加载视频
    if (props.messageItem.status === 'success') {
      preloadVideo(props.content.url)
    }
  } catch (error) {
    console.error('Failed to initialize video:', error)
  }
}

// 监听内容变化，初始化视频
watchEffect(() => {
  // console.log('URL变化:', props.content.url, 'messageItem:', props.messageItem?.payload?.remoteVideoUrl)
  if (props.content?.url) {
    initializeVideo()
  }
})

/**
 * 预加载视频资源
 */
function preloadVideo(url: string) {
  if (!url) return

  const preloader = document.createElement('video')
  preloader.style.display = 'none'
  preloader.src = url
  preloader.preload = 'auto'

  preloader.addEventListener(
    'canplaythrough',
    () => {
      isVideoLoaded.value = true
      setTimeout(() => {
        document.body.contains(preloader) && document.body.removeChild(preloader)
      }, 1000)
    },
    { once: true }
  )

  document.body.appendChild(preloader)

  // 10秒后清理预加载元素
  setTimeout(() => {
    if (document.body.contains(preloader)) {
      document.body.removeChild(preloader)
    }
  }, 10000)
}

/**
 * 切换视频预览器显示状态
 */
function toggleVideoPreviewer() {
  // 只在移动端且视频上传成功时才允许全屏预览
  if (!isPC && props.messageItem.status === 'success') {
    isShow.value = !isShow.value
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/styles/common';

.message-video {
  position: relative;
  display: flex;
  justify-content: center;
  overflow: hidden;
  border-radius: 18px;
  border: 2px solid #2266ff;

  &-box {
    max-width: min(calc(100vw - 180px), 300px);
    font-size: 0;

    .video-container {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 1;
      transition: opacity 0.3s ease;

      .video-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.05);
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        border-radius: 10px;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(255, 255, 255, 0.8);
          border-top: 3px solid #2266ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          backdrop-filter: blur(2px);
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 10px;
        }
      }

      :deep(.video-js) {
        width: 100% !important;
        height: 100% !important;
        border-radius: 10px;
        overflow: hidden;

        .vjs-big-play-button {
          z-index: 2;
        }
      }
    }
  }

  &-cover {
    display: inline-block;
    position: relative;

    &::before {
      position: absolute;
      z-index: 1;
      content: '';
      width: 0;
      height: 0;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      transform: translate(5px, 0);
      background: url('@/assets/images/play.png') no-repeat center !important;
      background-size: contain !important;
      border: none !important;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.dialog-video {
  position: fixed;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.9);

  &-close {
    display: flex;
    justify-content: flex-end;
    background: #000;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;

    .close-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  &-box {
    display: flex;
    flex: 1;
    max-height: 100%;
    padding: 6rem;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;

    :deep(.video-js) {
      width: 100% !important;
      height: 100% !important;
      border-radius: 8px;

      .vjs-big-play-button {
        z-index: 2;
      }
    }
  }
}

.dialog-video-h5 {
  width: 100%;
  height: 100%;
  background: #000;
  padding: 30px 0;
}
</style>
