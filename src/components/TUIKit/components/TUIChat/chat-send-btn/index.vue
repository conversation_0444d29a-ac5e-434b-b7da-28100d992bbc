<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-26 11:19:39
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-27 16:45:19
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/TUIChat/chat-send-btn/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="chat-send-btn" :class="{ 'chat-send-btn--disabled': disabled }" @click.stop="handleSend"></div>
</template>

<script setup lang="ts">
const props = defineProps<{
  disabled?: boolean
}>()
console.log('🚀 ~ 发送按钮状态:', props.disabled ? '禁用' : '启用')
const emit = defineEmits<{
  (e: 'send'): void
}>()
const handleSend = () => {
  if (props.disabled) {
    console.log('发送按钮被禁用，不触发发送事件')
    return
  }
  console.log('发送按钮被点击，触发发送事件')
  emit('send')
}
</script>

<style scoped lang="scss">
.chat-send-btn {
  width: 64px;
  height: 64px;
  background-image: url('@/assets/images/chat-send-btn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  &--disabled {
    background-image: url('@/assets/images/send-disabled.png');
    cursor: not-allowed;
  }
}
</style>
