.tui-contact-info {
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  display: flex;
  padding: 30px;
  box-sizing: border-box;
  flex-direction: column;
  overflow: hidden;

  &-basic {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;

    &-text {
      flex: 1;

      &-name {
        font-size: 24px;
        padding-bottom: 10px;
      }

      &-other {
        font-size: 16px;
        padding: 6px 0;
        font-weight: 400;
        color: #999;
      }
    }

    &-avatar {
      width: 80px;
      height: 80px;
    }
  }

  &-more {
    padding: 15px 0;
    overflow: hidden;

    &-item {
      display: flex;
      padding: 6px 0;
      font-size: 16px;
      font-weight: 400;
      min-height: 56px;

      &-label {
        color: #999;
        height: fit-content;
      }

      &-left {
        flex-direction: row;
        align-items: center;

        .tui-contact-info-more-item-label {
          width: 80px;
        }
      }

      &-top {
        flex-direction: column;
      }

      &-content {
        flex: 1;
        display: flex;
        flex-direction: row;
        color: #333;
        overflow: hidden;

        &-text {
          display: flex;
          overflow: hidden;

          &-data {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &-icon {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
          }
        }

        &-input,
        &-textarea {
          flex: 1;
          border: 1px solid #e8e8e9;
          border-radius: 4px;
          padding: 4px;
          color: inherit;
        }

        &-input {
          height: 22px;
          font-size: 16px;
        }

        &-textarea {
          resize: none;
          height: 100px;
        }
      }
    }
  }

  &-button {
    display: flex;
    padding: 30px;
    justify-content: center;

    &-item {
      margin: 15px;
      min-width: 142px;
      height: 36px;
      padding: 8px 20px;
      border-radius: 4px;
      border: none;
      font-size: 14px;
      text-align: center;
      line-height: 20px;
      font-weight: 400;
      letter-spacing: 0;
      cursor: pointer;
      user-select: none;

      &-submit {
        border: 1px solid #006eff;
        background: #006eff;
        color: #fff;
      }

      &-cancel {
        border: 1px solid #e54545;
        background: transparent;
        color: #e54545;
      }
    }
  }
}
