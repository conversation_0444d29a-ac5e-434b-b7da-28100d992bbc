<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-11 17:26:06
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-04-16 14:11:32
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/components/common/RadioSelect/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <div class="radio-select" :class="{ disabled: props.disabled }" @click="handleClick"> -->
  <div class="radio-select" :class="{ disabled: props.disabled }">
    <img :src="currentIcon" class="select-icon" :class="{ disabled: props.disabled }" />
  </div>
</template>

<script lang="ts" setup>
import defaultIcon from '@/assets/images/select-icon/default.png'
import disableIcon from '@/assets/images/select-icon/disable.png'
import selectIcon from '@/assets/images/select-icon/select.png'
import { computed } from 'vue'

interface IProps {
  isSelected: boolean
  disabled?: boolean
}

interface IEmits {
  (e: 'onChange', value: boolean): void
}

const emits = defineEmits<IEmits>()
const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
})

const currentIcon = computed(() => {
  if (props.disabled) {
    return disableIcon
  }
  if (props.isSelected) {
    return selectIcon
  }
  return defaultIcon
})

// function handleClick() {
//   if (props.disabled) {
//     return
//   }
//   emits('onChange', !props.isSelected)
// }
</script>
<style lang="scss" scoped>
:not(not) {
  display: flex;
  flex-direction: column;
  min-width: 0;
  box-sizing: border-box;
}

.radio-select {
  flex: 1;
  flex-direction: column;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  justify-content: flex-start;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.select-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}
</style>
