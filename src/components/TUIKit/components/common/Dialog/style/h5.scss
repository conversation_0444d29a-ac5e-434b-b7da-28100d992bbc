.dialog-h5 {
  height: 100%;
  top: 0;
  align-items: inherit;

  .dialog {
    &-main {
      border-radius: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      width: 100%;
      min-height: 80px;
      min-width: 120px;

      &-content {
        flex: 1;
        min-width: 0;
        min-height: 0;
        overflow: hidden;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 0;

        &-uniapp {
          padding: 40px 0;
        }
      }

      &-footer {
        border-top: 1px solid #DDD;

        .btn {
          flex: 1;
          margin: 0;
          background: none;
          border-right: 1px solid #DDD;

          &-default {
            color: #FF584C;
            border: none;
          }
        }
      }
    }
  }
}

.center {
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}
