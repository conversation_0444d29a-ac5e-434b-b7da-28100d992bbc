.avatar {
  width: 36px;
  height: 36px;
  border-radius: 5px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main {
  box-sizing: border-box;
  width: 620px;
  height: 394px;
  display: flex;
  border-radius: 8px;
  padding: 20px 0;

  .transfer-header {
    font-size: 14px;
    line-height: 14px;
    padding-bottom: 20px;

    input {
      box-sizing: border-box;
      width: 100%;
      border-radius: 30px;
      font-size: 10px;
      line-height: 14px;
      padding: 9px 12px;
    }
  }

  .transfer-list {
    flex: 1;
    display: flex;
    flex-direction: column;

    .transfer-text {
      font-size: 10px;
      line-height: 14px;
    }

    &-item {
      padding: 6px 0;
      display: flex;
      align-items: center;
      font-size: 14px;
      text-align: left;

      &-content {
        flex: 1;
        display: flex;
        align-items: center;
      }

      .avatar {
        margin: 0 5px 0 8px;
        border-radius: 50%;
      }

      .name {
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }
    }
  }

  .right {
    padding: 0 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .transfer-right-footer {
      align-self: flex-end;

      .btn-cancel {
        margin-right: 12px;
      }
    }

    .transfer-list {
      padding-right: 20px;
      overflow-y: auto;
    }
  }

  .left {
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;

    .transfer-header {
      padding: 0 20px;
    }

    .transfer-left-main {
      flex: 1;
      overflow-y: auto;
      padding: 0 13px;
    }
  }
}



.btn {
  padding: 4px 28px;
  font-size: 12px;
  line-height: 24px;
  border-radius: 4px;
}

.btn-no {
  padding: 4px 28px;
  font-size: 12px;
  line-height: 24px;
  border-radius: 4px;
}

.space-between {
  justify-content: space-between;
}

.select-all {
  padding-left: 8px;
  font-size: 14px;
}

.more {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}
