## [2.4.0] (2025-01-16)

### Fix
- 修复 sendMessage 时携带的默认信息，支持用户自定义配置。

## [2.3.8] (2025-01-10)

### Features
- 支持文本消息 url 高亮跳转

## Fix
- 修复自定义大表情失效问题
- 修复 login 时好友列表数据未重置问题

## [2.3.6] (2024-12-06)

### Fix
- 修复 marked 依赖丢失 ts 类型声明

## [2.3.5] (2024-12-06)

### Features
- 支持消息 markdown 展示
- 支持消息流式输出效果

## Fix
- 修复 Toast 失效问题

## [2.3.3] (2024-10-28)

### Features
- 支持群组禁言后发送消息错误提示国际化

## Fix
- 修复集成 CallKit 点击通话按钮偶现无响应问题

## [2.3.2] (2024-10-28)

## Fix
- 修复群聊中群管理入口偶现的丢失问题

## [2.3.1] (2024-10-25)

## Fix
- 修复 H5 安全区遮挡问题
- 修复 H5 键盘遮挡输入框问题
- 修复撤回消息中撤回人为空问题
- 修复 TUIContact 修改好友备注后列表显示未更新问题

## [2.2.9] (2024-10-17)

## Fix
- 修复 Callkit 融合被叫超时无应答英文词条翻译错误问题

## [2.2.8] (2024-09-24)

### Features
- 支持繁体中文语言
- 支持新增自定义语言类型

## Fix
- 修复 MessageInput 内容中间回车发送消息引入换行问题

## [2.2.7] (2024-09-13)

### Features
- 优化 C2C 会话音视频通话信令上屏(对齐微信体验)

## [2.2.6] (2024-09-06)

### Features
- Work 类型群组支持普通群成员修改群名称、群公告信息

## [2.2.3] (2024-07-05)

### Features
- 支持会话草稿
- 支持富文本复制
- 支持文本部分复制
- 语音消息使用红点提示是否播放

## Fix
- 修复邀请加入群聊选人组件未过滤已有群成员问题
- 修复 TUISearch 搜索文本消息出现 [系统消息] 前缀问题
- 修复消息引用语音但展示为"聊天记录"的问题
- 修复 Callkit 在 H5 环境下布局只有半屏的问题

## [2.2.0] (2024-06-17)

### Features
- 支持消息多选、消息逐条转发、消息合并转发
- 支持表情包自定义
- 支持中英文 github readme
- 被引用消息撤回时隐藏原始消息内容并提醒已撤回

## [2.1.4] (2024-05-20)

### Fix
- 修复头像组件因加载图片失败导致的循环加载问题

## [2.1.3] (2024-05-17)

### Features
- 点击空白区域时收起小表情面板和工具栏
- callkit 提供音视频通话中途加人能力
- 更新 roomkit 引用方式

### Fix

- 修复视频一定概率无法播放的问题
- 修复消息引用的视觉左侧未对齐的问题

## [2.1.1] (2024-04-26)

### Features
- 支持语音转文字
- 文本消息转翻译兼容小表情上屏，兼容提及所有人

### Fix
- 优化已读回执详情列表超长昵称的显示效果
- 解决转发消息已读回执失效的问题
- 解决加没有群申请时额外请求用户信息的问题

## [2.1.0] (2024-04-12)

### Features
- 新增消息翻译功能

## [2.0.9] (2024-03-29)

### Features
- ScrollButton 支持未读新消息提示
- 群未决申请展示优化
- 兼容支持 H5 IOS longPress 事件

### Update
- 下线本地审核相关入口

### Fix
- 修复 nick 过长样式溢出问题

## [2.0.8] (2024-03-15)

### Fix
- 修复已知问题，提升稳定性

## [2.0.7] (2024-03-15)

### Fix
- TUIConversation network 断网显示优化
- 修复消息列表滚动到顶部后"回到底部"按钮消失的问题
- 修复一定概率下消息列表为空的问题

## [2.0.6] (2024-03-01)

### Features

- 升级 universal api 引入方式

## [2.0.5] (2024-02-04)

### Features

- 新增表情回复功能(需购买旗舰版)
- 聊天界面更新黄脸小表情
- 添加音频播放动画

### Fix

- 语音场景优化 修复了语音播放相关的体验问题

## [2.0.4] (2024-01-19)

### Features

- 支持文本消息复制

## [2.0.3] (2024-01-12)

### Features

- TUIContact 关系链支持用户在线状态。
- TUIContact 中获取客服列表的时机调整为 Engine 设置商业化能力位之后。

### Fix

- 修复已知问题，提升稳定性

## [2.0.2] (2024-01-05)

### Fix

- 修复 IOS13 消息无法发送问题
- 修复已知问题，提升稳定性

## [2.0.0] (2023-12-21)

### Features

- 全面支持 Vue2 & Vue3，包括以下主体功能：
  - TUIChat: 负责消息界面展示，包括多类型消息收发，消息引用/删除/撤回/转发、查询消息已读回执详情等功能。
  - TUIConversation: 负责会话列表的展示和编辑，包括会话置顶、会话消息免打扰、会话删除等功能.
  - TUISearch: 负责消息云端搜索，包括全局搜索与会话内搜索，支持文本、图片、文件等多类型消息混合搜索，支持搜索时间范围选择与搜索结果定位，免费试用请点击 https://cloud.tencent.com/document/product/269/92648#ae4e3f5c-94db-4df3-8a49-65d23ce417b8 开通。
  - TUIGroup: 负责群聊的创建以及群资料、群成员、群组权限、群公告、群禁言的管理。
  - TUIContact: 负责联系人与群组展示，添加好友，移入黑名单，好友备注，信息展示等功能。
- 同时，我们还提供了功能丰富的插件系统：
  - TUICustomerService: 在线客服插件，支持灵活的路由排队、客服接待、智能机器人功能，配合功能丰富的管理端与数据分析能力，支持客服多终端办公，免费试用请点击 https://cloud.tencent.com/document/product/269/92648#ae4e3f5c-94db-4df3-8a49-65d23ce417b8 开通。
  - TUICallKit: 音视频通话 UI 组件，支持两人或多人进行音视频通话，覆盖游戏社交、在线客服、视频客服、在线问诊、保险咨询等场景，免费试用请点击 https://cloud.tencent.com/document/product/269/79861#step1 开通。
