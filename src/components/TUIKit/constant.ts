/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-17 14:51:29
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-24 14:42:44
 * @FilePath: /miaobi-admin-magic-touch/src/components/TUIKit/constant.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%
 */
export const CONV_OPERATION = {
  DELETE: "delete",
  ISPINNED: "ispinned",
  DISPINNED: "dispinned",
  MUTE: "mute",
  NOTMUTE: "notmute",
};

export const CONV_CREATE_TYPE = {
  TYPEC2C: "isC2C",
  TYPEGROUP: "isGroup",
  JOINGROUP: "joinGroup",
};

export const CHAT_MSG_CUSTOM_TYPE = {
  SERVICE: "consultion",
  EVALUATE: "evaluation",
  LINK: "text_link",
  CALL: 1,
  ORDER: "order",
  AIMESSAGE: "ai_message",
  AISIGNAL: "ai_signal",
  HIDDENMESSAGE: "hidden_message",
  AI_CUSTOM_MSG: "ai_custom_msg",
};

export const DIALOG_CONTENT = {
  USERLIST: "userList",
  GROUPINFORMATION: "groupInformation",
};

export const EMOJI_TYPE = {
  BASIC: "basic",
  BIG: "big",
  CUSTOM: "CUSTOM",
};

export const CONTACT_INFO_LABEL_POSITION = {
  LEFT: "left",
  TOP: "top",
};

export const CONTACT_INFO_MORE_EDIT_TYPE = {
  INPUT: "input",
  TEXTAREA: "textarea",
  SWITCH: "switch",
};

export const CONTACT_INFO_BUTTON_TYPE = {
  SUBMIT: "submit",
  CANCEL: "cancel",
};
