const TUIGroup = {
 "输入groupID搜索": "Enter groupId search",
  "群名称": "Group name",
  "群ID": "Group ID",
  "加入群聊": "Join",
  "解散群聊": "Dissolution",
  "退出群聊": "Quit",
  "群类型": "Group type",
  "请填写验证信息": "Please fill in the verification information",
  "申请加入": "Apply Join",
  "群公告": "Group notice",
  "群成员": "Group member",
  "群管理": "Manage group",
  "好友工作群": "Work",
  "陌生人社交群": "Public",
  "临时会议群": "Meeting",
  "直播群": "AVChatRoom",
  "社群": "Community",
  "自由加入": "Join freely",
  "需要验证": "Require approval",
  "禁止加群": "Disallow group joining",
  "人": "",
  "确认": "Confirm",
  "群头像": "Group profile photo",
  "加群方式": "Group joining mode",
  "转让群组": "Transfer ownership",
  "退出群组": "Quit group",
  "群管理员": "Group admin",
  "全员禁言": "Mute All",
  "全员禁言开启后，只允许群主和管理员发言。": "If Mute All is enabled, only the group owner and admin can speak.",
  "单独禁言人员": "Mute a member",
  "删除成员": "Remove member",
  "确定从群聊中删除该成员？": "Are you sure you want to remove this member from the group chat?",
  "确定从群聊中删除所选成员？": "Are you sure you want to remove selected member(s) from the group chat?",
  "暂无公告": "No notice",
  "发布": "Post",
  "编辑": "Edit",
  "查看更多": "View more",
  "管理员": "Admin",
  "群主": "Group owner",
  "我": "me",
  "添加成员": "Add member",
  "新增管理员": "Add admin",
  "移除管理员": "Revoke admin",
  "新增禁言用户": "Add muted member",
  "移除禁言用户": "Remove muted member",
  "修改群聊名称": "Edit group name",
  "修改群聊名称后，将在群内通知其他成员": "After modifying the group chat name, other members will be notified in the group",
  "仅限中文、字母、数字和下划线，2-20个字": "Chinese, letters, numbers and underscores only, 2-20 words",
  "请先注册 TUIGroup 模块": "Please register the TUIGroup module first",
  "该用户不存在": "The user does not exist",
  "该用户不在群组内": "The user is not in the group",
  "添加群聊": "Add a group chat",
  "该群组不存在": "The group does not exist",
  "创建群聊，请注册 TUIGroup 模块": "To create a group chat, please register the TUIGroup module",
  "创建成功": "Creation successful",
  "发起多人会话（群聊）": "New group chat",
  "选填": "Optional",
  "取消": "Cancel",
  "创建": "Create",
  "类似普通微信群，创建后仅支持已在群内的好友邀请加群，且无需被邀请方同意或群主审批。详见": "Similar to a WeChat group. Users can join the group only via invitation by existing members. The invitation does not need to be agreed by the invitee or approved by the group owner. See the documentation for details.",
  "类似 QQ 群，创建后群主可以指定群管理员，用户搜索群 ID 发起加群申请后，需要群主或管理员审批通过才能入群。详见": "Similar to a QQ group. After a public group is created, the group owner can designate group admins. To join the group, a user needs to search the group ID and send a request, which needs to be approved by the group owner or an admin before the user can join the group. See the documentation for details. ",
  "创建后可以随意进出，且支持查看入群前消息；适合用于音视频会议场景、在线教育场景等与实时音视频产品结合的场景。详见": "After the group is created, a user can join and quit the group freely and can view the messages sent before joining the group. It is suitable for scenarios that integrate Tencent Real-Time Communication (TRTC), such as audio and video conferences and online education. See the documentation for details.",
  "创建后可以随意进出，没有群成员数量上限，但不支持历史消息存储；适合与直播产品结合，用于弹幕聊天场景。详见": "After creation, a user can join and quit the group freely. The group can have an unlimited number of members, but it does not store message history. It can be combined with Live Video Broadcasting (LVB) to support on-screen comment scenarios. See the documentation for details. ",
  "创建后可以随意进出，最多支持100000人，支持历史消息存储，用户搜索群 ID 发起加群申请后，无需管理员审批即可进群。详见": "After creation, you can enter and leave at will, support up to 100,000 people, support historical message storage, and after users search for group ID and initiate a group application, they can join the group without administrator approval. See product documentation for details. ",
  "产品文档": "product documentation",
  "设置群名称": "Group name",
  "请输入群名称": "Enter the group name",
  "设置群ID": "Group ID",
  "请输入群ID": "Enter the group ID",
  "选择群类型": "Group type",
  "陌生人社交群（Public）": "Stranger social group (Public)",
  "临时会议群（Meeting）": "Temporary meeting group (Meeting)",
  "好友工作群（Work）": "Friends work group (Work)",
  "直播群（AVChatroom）": "Live Streaming Group (AVChatroom)",
  "社群（Community）": "Community（Community）",
  "群组创建成功": "Create group success",
  "群组解散成功": "Dismiss group success",
  "禁言设置成功": "Mute all success",
  "取消禁言成功": "Remove mute success",
  "群名称不能为空": "Group name cannot be empty",
  "群名称修改成功": "Group name modified success",
  "群公告字数超出限制，最大长度为150": "The number of characters in the group announcement exceeds the limit, the maximum length is 150",
};

export default TUIGroup;
