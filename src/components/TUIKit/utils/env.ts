// import { getPlatform } from "@tencentcloud/universal-api";

declare const uni: any;

// export const isPC = getPlatform() === "pc";

// export const isH5 = getPlatform() === "h5";

// export const isWeChat = getPlatform() === "wechat";

// export const isApp = getPlatform() === "app";
export const isPC = false;
export const isH5 = true;
export const isWeChat = false;
export const isApp = false;
export const isUniFrameWork = typeof uni !== "undefined";

// 检测是否在Electron环境中
export const isElectron = () => {
  return window && window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf('Electron') >= 0;
};

// 检测是否是Android或iOS环境
export const isMobileNative = () => {
  const userAgent = window.navigator.userAgent;
  return /android/i.test(userAgent) || /iphone|ipad|ipod/i.test(userAgent);
};

// H5, mini programs, and apps are all considered mobile.
// If you need to unify the mobile UI style, you can directly use isMobile to control
export const isMobile = isH5 || isWeChat || isApp;
