<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-08 16:48:59
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-21 11:34:42
 * @FilePath: /msb-users-magic-brush/src/components/IMImagePreview/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="image-preview-container">
    <img :src="src" alt="图片" :object-fit="fit" @click.stop="handleClick" lazy class="image-preview-img" />
    <div class="image-preview-long-text" v-if="isBigImage">长图</div>
    <div v-if="chatStore.imagePreviewOpen && isCurrentPreview" class="image-preview" @click.stop>
      <div class="image-preview-header">
        <div class="close-btn" @click="closeDialog">
          <img :src="back" alt="" />
        </div>
        <div class="tool-box">
          <!-- <div class="icon-box" @click.stop="saveToCourse" v-if='isTeacher'>
            <img :src="save2course" alt="" />
          </div> -->
          <div class="icon-box" @click.stop="handleJoin">
            <img :src="joinImg" alt="" />
          </div>
          <div class="icon-box" @click.stop="handleSave">
            <img :src="save" alt="" />
          </div>
        </div>
      </div>
      <div :class="isBigImage ? 'image-preview-content-big' : 'image-preview-content'">
        <img :src="fullScreenUrl" alt="图片" :object-fit="fit" />
      </div>

      <div class="image-preview-btn" v-if="!src.includes('blob:') && !isBigImage">
        <div class="btn-box" @click.stop="handleQuote" v-if="!isBigImage">
          <img :src="quote" alt="" />
          <span>引用</span>
        </div>
        <div class="btn-box" @click="handleCutouts" v-if="!isBigImage">
          <img :src="cutouts" alt="" />
          <span>抠图</span>
        </div>
        <div class="btn-box" @click.stop="handleEdit" v-if="!isBigImage">
          <img :src="edit" alt="" />
          <span>消除</span>
        </div>
        <div class="btn-box" @click="handleRedraw" v-if="!isBigImage">
          <img :src="review" alt="" />
          <span>局部重绘</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import save from '@/assets/images/img-pre/save.png'
import quote from '@/assets/images/img-pre/quote.png'
import cutouts from '@/assets/images/img-pre/ai-cutouts.png'
import back from '@/assets/images/img-pre/back.png'
import joinImg from '@/assets/images/img-pre/join.png'
import save2course from '@/assets/images/img-pre/save2course.png'
import edit from '@/assets/images/img-pre/edit.png'
import review from '@/assets/images/img-pre/review.png'
import { joinMediaViaJSBridge } from '@/utils/messageUtils'
import { useRoute } from 'vue-router'
import useChatStore from '@/store/modules/chat'
import { jsbridge } from 'msb-public-library'
import { Toast } from '../TUIKit/components/common/Toast'
import { ElMessage } from 'element-plus'
export default defineComponent({
  name: 'IMImagePreview',
  props: {
    src: {
      type: String,
      required: true,
    },
    fit: {
      type: String,
      default: 'cover',
    },
    message: {
      type: Object,
      required: true,
    },
    fullScreenUrl: {
      type: String,
      default: '',
    },
    isBigImage: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  emits: ['edit', 'save', 'quote', 'cutouts'],
  setup(props, { emit }) {
    console.log('🚀 ~ setup ~ props:', props)
    const route = useRoute()
    const message = props.message
    const chatStore = useChatStore()
    const isTeacher = computed(() => {
      return route.query.role === 'teacher'
    })

    // 生成当前组件的唯一标识
    const componentId = `${props.message.ID || 'unknown'}_${props.index}`

    // 判断当前组件是否是正在预览的组件
    const isCurrentPreview = computed(() => {
      return chatStore.currentPreviewId === componentId
    })

    const handleClick = () => {
      console.log('click')
      // 设置当前预览的组件ID并打开预览
      chatStore.setCurrentPreviewId(componentId)
      chatStore.setImagePreviewOpen(true)
    }

    const closeDialog = () => {
      chatStore.setImagePreviewOpen(false)
      chatStore.setCurrentPreviewId('')
    }

    const handleEdit = () => {
      // 通过store打开ImageEditor
      const imageUrl = props.fullScreenUrl || props.src
      chatStore.openImageEditor(imageUrl, 'edit')
      closeDialog()
    }

    const handleRedraw = () => {
      // 通过store打开ImageEditor
      const imageUrl = props.fullScreenUrl || props.src
      chatStore.openImageEditor(imageUrl, 'cutouts')
      closeDialog()
    }
    const handleSave = () => {
      emit('save', props.index)
      closeDialog()
    }

    const handleQuote = () => {
      emit('quote', props.index)
      closeDialog()
    }

    const handleJoin = () => {
      const index = props.index
      joinMediaViaJSBridge(message, index).then(() => {
        closeDialog()
      })
    }
    const handleCutouts = () => {
      emit('cutouts', props.src)
      closeDialog()
    }

    // const saveToCourse = () => {
    //   jsbridge?.sendMsg({
    //     action: 'classCourseWareUrl',
    //     params: {
    //       url: props.fullScreenUrl,
    //     },
    //   })
    //   ElMessage.success('保存成功至课件')
    // }
    return {
      isTeacher,
      chatStore,
      isCurrentPreview,
      handleClick,
      closeDialog,
      handleEdit,
      handleSave,
      handleQuote,
      handleJoin,
      handleCutouts,
      // saveToCourse,
      handleRedraw,
      message,
      save,
      quote,
      cutouts,
      back,
      joinImg,
      edit,
      save2course,
      review,
    }
  },
})
</script>

<style lang="scss" scoped>
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview-long-text {
  position: absolute;
  top: 4px;
  right: 9px;
  width: 45px;
  height: 23px;
  background: #2967f3;
  border-radius: 3px;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 13px;
  color: #fefeff;
  line-height: 23px;
  text-align: center;
}

.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-header {
    height: 65px;
    width: 100%;
    background: #000;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .close-btn {
      width: 23px;
      height: 23px;

      img {
        width: 100%;
      }

      margin-left: 21px;
    }

    .tool-box {
      display: flex;
      flex-direction: row;
      align-items: center;

      .icon-box {
        width: 28px;
        height: 25px;
        margin-right: 30px;

        img {
          width: 100%;
        }
      }
    }
  }

  .image-preview-content-big {
    width: 100vw;
    // height: 78.6vh;
    flex: 1;
    overflow: scroll; // 移除滚动条，确保图片完全适应容器
    display: flex;
    flex-direction: column;
    // justify-content: center; // 垂直居中
    align-items: center;

    img {
      max-width: 100%;
      // max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain; // 保持原有比例
    }

    background: rgba(49, 49, 49, 1);
  }

  .image-preview-content {
    flex: 1;
    overflow: hidden; // 移除滚动条，确保图片完全适应容器
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(49, 49, 49, 1);
    width: 100%;

    img {
      // max-width: 100%;
      max-height: 100%;
      // width: auto;
      height: auto;
      object-fit: contain; // 保持原有比例
      width: 100%;
    }


  }


  .image-preview-btn {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 107px;
    background: #000;
    padding-left: 37px;

    .btn-box {
      display: flex;
      flex-direction: row;
      background: #2967f3;
      padding: 10px;
      border-radius: 5px;
      // min-width: 87px;
      margin-right: 20px;
      justify-content: space-between;

      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }

      span {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #ffffff;
        flex: 1;
        text-align: center;
      }
    }
  }
}
</style>
