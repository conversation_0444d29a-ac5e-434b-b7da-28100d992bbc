/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-15 16:09:17
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-17 17:18:40
 * @FilePath: /miaobi-admin-magic-touch/src/permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp, isPathMatch } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register']

const isWhiteList = (path: any): any => {
  return whiteList.some(pattern => isPathMatch(pattern, path))
}

router.beforeEach((to, from, next) => {
  console.log('🚀 ~ router.beforeEach ~ to:', to)
  NProgress.start()

  // 设置路由属性到body和app元素
  document.body.setAttribute('data-route', to.path)
  document.getElementById('app')?.setAttribute('data-route', to.path)

  if (to.path === '/ai-chat' || to.path === '/ai-schedule' || to.path === '/ai-chat/image-cut-diagram') {
    next()
    NProgress.done()
  } else {
    if (getToken()) {
      to.meta.title && useSettingsStore().setTitle(to.meta.title)
      /* has token*/
      if (to.path === '/login') {
        next({ path: '/' })
        NProgress.done()
      } else if (isWhiteList(to.path)) {
        next()
      } else {
        if (useUserStore().roles.length === 0) {
          isRelogin.show = true
          // 判断当前用户是否已拉取完user_info信息
          useUserStore()
            .getInfo()
            .then(() => {
              isRelogin.show = false
              usePermissionStore()
                .generateRoutes()
                .then(accessRoutes => {
                  // 根据roles权限生成可访问的路由表
                  accessRoutes.forEach(route => {
                    if (!isHttp(route.path)) {
                      router.addRoute(route) // 动态添加可访问路由表
                    }
                  })
                  next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
                })
            })
            .catch(err => {
              useUserStore()
                .logOut()
                .then(() => {
                  ElMessage.error(err)
                  next({ path: '/' })
                })
            })
        } else {
          next()
        }
      }
    } else {
      // 没有token
      if (isWhiteList(to.path)) {
        // 在免登录白名单，直接进入
        next()
      } else {
        next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
        NProgress.done()
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
