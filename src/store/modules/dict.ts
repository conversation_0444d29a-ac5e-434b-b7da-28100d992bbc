/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/store/modules/dict.ts
 * @Description: TypeScript版本 - 自动转换
 */

import { defineStore } from 'pinia';

const useDictStore = defineStore(
  'dict',
  {
    state: (): any => ({
      dict: new Array()
    }),
    actions: {
      // 获取字典
      getDict(_key: any): void {
        if (_key == null && _key == "") {
          return null;
        }
        try {
          for (let i = 0; i < this.dict.length; i++) {
            if (this.dict[i].key == _key) {
              return this.dict[i].value;
            }
          }
        } catch (e) {
          return null;
        }
      },
      // 设置字典
      setDict(_key: any, value: any): void {
        if (_key !== null && _key !== "") {
          this.dict.push({
            key: _key,
            value: value
          });
        }
      },
      // 删除字典
      removeDict(_key: any): void {
        var bln = false;
        try {
          for (let i = 0; i < this.dict.length; i++) {
            if (this.dict[i].key == _key) {
              this.dict.splice(i, 1);
              return true;
            }
          }
        } catch (e) {
          bln = false;
        }
        return bln;
      },
      // 清空字典
      cleanDict(): void {
        this.dict = new Array();
      },
      // 初始字典
      initDict(): void {
      }
    }
  })

export default useDictStore
