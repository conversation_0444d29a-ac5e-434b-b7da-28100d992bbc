/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/store/modules/app.ts
 * @Description: TypeScript版本 - 自动转换
 */

import { defineStore } from 'pinia';

import Cookies from 'js-cookie'

const useAppStore = defineStore(
  'app',
  {
    state: (): any => ({
      sidebar: {
        opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
        withoutAnimation: false,
        hide: false
      },
      device: 'desktop',
      size: Cookies.get('size') || 'default'
    }),
    actions: {
      toggleSideBar(withoutAnimation: any): void {
        if (this.sidebar.hide) {
          return false;
        }
        this.sidebar.opened = !this.sidebar.opened
        this.sidebar.withoutAnimation = withoutAnimation
        if (this.sidebar.opened) {
          Cookies.set('sidebarStatus', 1)
        } else {
          Cookies.set('sidebarStatus', 0)
        }
      },
      closeSideBar(withoutAnimation: any): void {
        Cookies.set('sidebarStatus', 0)
        this.sidebar.opened = false
        this.sidebar.withoutAnimation = withoutAnimation
      },
      toggleDevice(device: any): void {
        this.device = device
      },
      setSize(size: any): void {
        this.size = size;
        Cookies.set('size', size)
      },
      toggleSideBarHide(status: any): void {
        this.sidebar.hide = status
      }
    }
  })

export default useAppStore
