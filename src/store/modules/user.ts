/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 16:30:39
 * @FilePath: src/store/modules/user.ts
 * @Description: TypeScript版本 - 自动转换
 */

import { defineStore } from 'pinia';

import { login, logout, getInfo } from "@/api/login";
import { getSign } from "@/api/common";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { isHttp, isEmpty } from "@/utils/validate";
import defAva from "@/assets/images/profile.jpg";
import TUIChatEngine, {
  TUITranslateService,
} from "@tencentcloud/chat-uikit-engine";

const useUserStore = defineStore("user", {
  state: (): any => ({
    token: getToken(),
    id: "",
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    userId: "",
    sign: "",
    orgId: undefined,
  }),
  actions: {
    // 登录
    login(userInfo: any): void {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            setToken(res.token);
            this.token = res.token;
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo(): void {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.user;
            let avatar = user.avatar || "";
            if (!isHttp(avatar)) {
              avatar = isEmpty(avatar)
                ? defAva
                : import.meta.env.VITE_APP_BASE_API + avatar;
            }
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles;
              this.permissions = res.permissions;
            } else {
              this.roles = ["ROLE_DEFAULT"];
            }
            this.id = user.userId;
            this.name = user.userName;
            this.avatar = avatar;
            this.orgId = res.orgId;
            
            // IM初始化
            this.initIMIfNeeded();
            
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 初始化IM（在需要时）
    initIMIfNeeded(): void {
      // 延迟导入auth模块，避免循环依赖
      const auth = import('@/plugins/auth').then(module => {
        const hasPermi = module.default.hasPermi;
        if (this.token && hasPermi("txy:im:user:sig")) {
          getSign().then((res) => {
            this.userId = res.data?.userId;
            this.sign = res.data?.sig;
            this.initIMLogin(res.data);
          });
        }
      });
    },
    // IM登录初始化
    initIMLogin(data: any): void {
      const initLogin = async () => {
        try {
          await TUIChatEngine.login({
            SDKAppID:
              window.location.host === "admin.miaobi.cn"
                ? 1600073492
                : 1600069673, // 测试：1600069673 线上：1600073492
            userID: data?.userId,
            userSig: data?.sig,
            useUploadPlugin: true, // 是否开启上传插件，用于发送图片、语音、视频、文件等消息
          });
        } catch (error) {
          console.error("IM登录失败", error);
        }
      };
      initLogin();
    },
    // 退出系统
    logOut(): void {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    setCurrentIMUser(data: any): void {
      this.userId = data.userId;
      this.sign = data.sig;
    },
  },
});

export default useUserStore;
