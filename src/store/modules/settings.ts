/*
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/store/modules/settings.ts
 * @Description: TypeScript版本 - 自动转换
 */

import { defineStore } from 'pinia'

import defaultSettings from '@/settings'
import { useDark, useToggle } from '@vueuse/core'
import { useDynamicTitle } from '@/utils/dynamicTitle'

const isDark = useDark()
const toggleDark = useToggle(isDark)

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings

const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''

const useSettingsStore = defineStore('settings', {
  state: (): any => ({
    title: '',
    theme: storageSetting.theme || '#409EFF',
    sideTheme: storageSetting.sideTheme || sideTheme,
    showSettings: showSettings,
    topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
    tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
    fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
    sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
    dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle,
    isDark: isDark.value,
  }),
  actions: {
    // 修改布局设置
    changeSetting(data: SettingData): void {
      const { key, value } = data
      if (Object.prototype.hasOwnProperty.call(this, key)) {
        ;(this as any)[key] = value
      }
    },
    // 设置网页标题
    setTitle(title: any): void {
      this.title = title
      useDynamicTitle()
    },
    // 切换暗黑模式
    toggleTheme(): void {
      this.isDark = !this.isDark
      toggleDark()
    },
  },
})

export default useSettingsStore
