declare module 'markdown-it-emoji'
declare module 'markdown-it-sub'
declare module 'markdown-it-sup'
declare module 'markdown-it-footnote'
declare module 'markdown-it-deflist'
declare module 'markdown-it-abbr'
declare module 'markdown-it-container'
declare module 'markdown-it-task-lists'
declare module 'markdown-it-anchor'
declare module 'markdown-it-toc-done-right'
declare module 'markdown-it-highlight'
declare module 'markdown-it-image-figures'
declare module 'markdown-it-katex'
declare module 'markdown-it-mathjax3'

// 扩展类型声明，让TS可以识别插件参数
declare module 'markdown-it' {
  import MarkdownIt from 'markdown-it'

  interface MarkdownItOptions {
    html?: boolean
    xhtmlOut?: boolean
    breaks?: boolean
    langPrefix?: string
    linkify?: boolean
    typographer?: boolean
    quotes?: string | string[]
    highlight?: (str: string, lang: string) => string
  }

  interface MarkdownItContainer {
    validate?(params: string): boolean
    render?(tokens: any[], idx: number): string
  }

  interface MarkdownItKatexOptions {
    throwOnError?: boolean
    errorColor?: string
  }

  interface MarkdownItFiguresOptions {
    figcaption?: boolean
    tabindex?: boolean
    classes?: string
    copyAttrs?: string
  }
}
