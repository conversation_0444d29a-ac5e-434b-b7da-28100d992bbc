/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-23 14:27:40
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-04-23 14:27:48
 * @FilePath: /miaobi-admin-magic-touch/src/types/lunar-javascript.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
declare module 'lunar-javascript' {
  export class Solar {
    static fromDate(date: Date): Solar
    getMonth(): number
    getDay(): number
    getLunar(): Lunar
  }

  export class Lunar {
    getMonth(): number
    getDay(): number
    getDayInChinese(): string
    getMonthInChinese(): string
  }

  export class HolidayUtil {
    static getSolarFestival(month: number, day: number): string | null
    static getLunarFestival(month: number, day: number): string | null
  }
}
