/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-16 10:16:34
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-02-08 15:44:06
 * @FilePath: /miaobi-admin-magic-touch/src/api/application/list.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

// 查询应用管理列表
export function getApplicationList(query: any): any {
  return request({
    url: `/app/application/list?pageNum=${query?.pageNum}&pageSize=${query?.pageSize}`,
    method: "post",
    data: query,
  });
}

// 查询应用管理详细
export function getApplicationDetail(id: any): any {
  return request({
    url: "/app/application/" + id,
    method: "get",
  });
}

// 新增应用管理
export function addApplication(data: any): any {
  return request({
    url: "/app/application/add",
    method: "post",
    data: data,
  });
}

// 修改应用管理
export function updateApplicationContent(data: any): any {
  return request({
    url: "/app/application/edit",
    method: "put",
    data: data,
  });
}
// 保存编排内容
export function saveApplicationContent(data: any): any {
  return request({
    url: "/app/application/save",
    method: "put",
    data: data,
  });
}
// 技能列表
export function getSkillList(): any {
  return request({
    url: "/app/application/skill/list",
    method: "get",
  });
}
