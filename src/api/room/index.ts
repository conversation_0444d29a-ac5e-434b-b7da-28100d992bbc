/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/room/index.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询智慧教室列表
export function listRoom(query: any): any {
  return request({
    url: '/system/room/list',
    method: 'get',
    params: query,
    headers: {
      needOrgId: true
    },
  })
}

// 查询智慧教室详细
export function getRoom(id: any): any {
  return request({
    url: '/system/room/' + id,
    method: 'get',
    headers: {
      needOrgId: true
    },
  })
}

// 新增智慧教室
export function addRoom(data: any): any {
  return request({
    url: '/system/room',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}

// 修改智慧教室
export function updateRoom(data: any): any {
  return request({
    url: '/system/room',
    method: 'put',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}

// 删除智慧教室
export function delRoom(id: any): any {
  return request({
    url: '/system/room/' + id,
    method: 'delete'
  })
}
