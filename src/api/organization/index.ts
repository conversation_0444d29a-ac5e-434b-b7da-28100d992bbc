/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/organization/index.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询机构列表
export function listOrganization(query: any): any {
  return request({
    url: '/system/organization/list',
    method: 'get',
    params: query
  })
}
// 查询机构密码
export function showPasswordFn(id: any): any {
  return request({
    url: `/system/organization/showPassword/${id}`,
    method: 'get',
  })
}

// 查询机构详细
export function getOrganization(id: any): any {
  return request({
    url: '/system/organization/' + id,
    method: 'get'
  })
}

// 新增机构
export function addOrganization(data: any): any {
  return request({
    url: '/system/organization',
    method: 'post',
    data: data
  })
}
//  添加课时
export function addClassHour(data: any): any {
  return request({
    url: '/org/hour/add',
    method: 'post',
    data: data
  })
}

// 修改机构
export function updateOrganization(data: any): any {
  return request({
    url: '/system/organization',
    method: 'put',
    data: data
  })
}
export function updatePwd(data: any): any {
  return request({
    url: '/system/organization/updatePwd',
    method: 'post',
    params: data
  })
}

// 删除机构
export function delOrganization(id: any): any {
  return request({
    url: '/system/organization/' + id,
    method: 'delete'
  })
}
