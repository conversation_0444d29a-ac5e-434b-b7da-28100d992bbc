/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-14 15:58:39
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-02-14 16:09:11
 * @FilePath: /miaobi-admin-magic-touch/src/api/common.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

export function getAliyun(): any {
  return request({
    url: "/aliyun/oss/sts",
    method: "get",
  });
}
export function getSign(): any {
  return request({
    url: "/txy/im/user/sig",
    method: "get",
  });
}

export function setMessage(data: any): any {
  return request({
    url: "/txy/im/clear/c2c/msg",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

export function getAliyun2(): any {
  return request({
    url: "/clientApi/v1/oss/anon/sts",
    method: "get",
  });
}