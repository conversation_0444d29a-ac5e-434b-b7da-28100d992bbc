/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/monitor/jobLog.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询调度日志列表
export function listJobLog(query: any): any {
  return request({
    url: '/monitor/jobLog/list',
    method: 'get',
    params: query
  })
}

// 删除调度日志
export function delJobLog(jobLogId: any): any {
  return request({
    url: '/monitor/jobLog/' + jobLogId,
    method: 'delete'
  })
}

// 清空调度日志
export function cleanJobLog(): any {
  return request({
    url: '/monitor/jobLog/clean',
    method: 'delete'
  })
}
