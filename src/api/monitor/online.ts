/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/monitor/online.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询在线用户列表
export function list(query: any): any {
  return request({
    url: '/monitor/online/list',
    method: 'get',
    params: query
  })
}

// 强退用户
export function forceLogout(tokenId: any): any {
  return request({
    url: '/monitor/online/' + tokenId,
    method: 'delete'
  })
}
