/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/monitor/operlog.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询操作日志列表
export function list(query: any): any {
  return request({
    url: '/monitor/operlog/list',
    method: 'get',
    params: query
  })
}

// 删除操作日志
export function delOperlog(operId: any): any {
  return request({
    url: '/monitor/operlog/' + operId,
    method: 'delete'
  })
}

// 清空操作日志
export function cleanOperlog(): any {
  return request({
    url: '/monitor/operlog/clean',
    method: 'delete'
  })
}
