/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-15 16:05:15
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-01-20 15:28:36
 * @FilePath: /miaobi-admin-magic-touch/src/api/login.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

// 登录方法
export function login(username: any, password: any, code: any, uuid: any): any {
  const data = {
    username,
    password,
    code,
    uuid,
  };
  return request({
    url: "/login",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: data,
  });
}

// 注册方法
export function register(data: any): any {
  return request({
    url: "/register",
    headers: {
      isToken: false,
    },
    method: "post",
    data: data,
  });
}

// 获取用户详细信息
export function getInfo(): any {
  return request({
    url: "/getInfo",
    method: "get",
  });
}

// 退出方法
export function logout(): any {
  return request({
    url: "/logout",
    method: "post",
  });
}

// 获取验证码
export function getCodeImg(): any {
  return request({
    url: "/captchaImage",
    headers: {
      isToken: false,
    },
    method: "get",
    timeout: 20000,
  });
}
