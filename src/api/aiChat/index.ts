/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-16 10:16:34
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-26 10:39:42
 * @FilePath: /miaobi-admin-magic-touch/src/api/aiChat/index.js
 * @Description: AI聊天相关接口
 */
import request from '@/utils/request'
import axios from 'axios'

// 获取AI聊天签名
export function getAiChatSign(data: any): any {
  return request({
    url: '/clientApi/v1/txy/im/anon/enter/group',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      // deviceId: "abc",
      // appVersion: "1.0.0",
      // deviceType: "android",
    },
    data: data,
  })
}
// 清除消息
export function setH5Message(data: any): any {
  return request({
    url: '/clientApi/v1/txy/im/anon/clear/c2c/msg',
    data: data,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 保存三维图片
export function sendMsg(data: any, params: any): any {
  const token = localStorage.getItem('aiChatToken')
  return axios({
    url: `/clientApi/v1/txy/im/anon/special/send/msg?fromAccount=${params.fromAccount}&groupId=${params.groupId}`,
    method: 'post',
    data,
    headers: {
      // deviceId: "abc",
      // appVersion: "1.0.0",
      // deviceType: "android",
      // Authorization: token ? `${token}` : "",
    },
  })
}

// 发送录音消息转文本
export function sendAudioMessageToText(data: any): any {
  return request({
    url: '/clientApi/v1/draw/anon/audio2word',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 清空所有消息
export function clearAllMessage(data: any): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/clear/group/msg?groupId=${data.groupId}`,
    method: 'post',
  })
}

// 获取AI工具
export function getTools(): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/get/tools`,
    method: 'get',
  })
}

// 合并视频
export function mergeMusic(data: any): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/video/merge/music`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 获取音乐
export function getMusic(): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/get/musics`,
    method: 'get',
  })
}
// 获取图片切图
export function getImageCurDiagram(params: any, onUploadProgress?: any, onDownloadProgress?: any): any {
  return request({
    url: '/clientApi/v1/draw/anon/cut/diagram',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/json',
    },
    onUploadProgress,
    onDownloadProgress,
  })
}

// 获取图片切图
export function saveMergeDiagram(params: any): any {
  return request({
    url: '/clientApi/v1/draw/anon/merge/diagram',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
// /v1/draw/anon/merge/diagram?jsonFile=xxxx&ids=1,2,3

export async function getToken(appid: string, accessKey: string) {
  const result = await request('/bytedance/api/v1/sts/token', {
    method: 'POST',
    headers: {
      Authorization: `Bearer; ${accessKey}`,
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      appid,
      duration: 300, // 单位秒，默认1小时
    }),
  })
  console.log('🚀 ~ getToken ~ result:', result)
  // .then(res => res.json())
  // .then(res => res.jwt_token)
  return result
}
export function buildFullUrl(url: string, auth: Record<string, string>) {
  const arr = []
  for (const key in auth) {
    arr.push(`${key}=${encodeURIComponent(auth[key])}`)
  }
  return `${url}?${arr.join('&')}`
}

export function getToolsFour(params: any): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/tools/quickPanel`,
    method: 'get',
    params,
  })
}

export function getToolsAll(params: any): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/tools/classify/list`,
    method: 'get',
    params,
  })
}
// 获取音乐列表
export function getMusicCategory(): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/get/category/name`,
    method: 'get',
  })
}

// 获取音乐分类下的音乐
export function getMusicByCategory(categoryId: number): any {
  return request({
    url: `/clientApi/v1/txy/im/anon/get/category/musics?categoryId=${categoryId}`,
    method: 'get',
  })
}

// 发送消息监控数据到服务端 - 原封不动发送lastMessage
export function sendMessageMonitor(messageData: any): any {
  return request({
    url: '/clientApi/v1/txy/im/anon/message/monitor',
    method: 'post',
    data: messageData,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
