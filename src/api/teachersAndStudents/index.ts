/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/teachersAndStudents/index.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询老师列表
export function listTeacher(query: any): any {
  return request({
    url: '/organization/teacher/list',
    method: 'get',
    headers: {
      needOrgId: true
    },
    params: query
  })
}
// 新增老师
export function addTeacher(data: any): any {
  return request({
    url: '/organization/teacher/addTeacher',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 修改老师
export function updateTeacher(data: any): any {
  return request({
    url: '/organization/teacher/updateTeacher',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 启用禁用老师
export function changeTeacherDisable(params: any): any {
  return request({
    url: '/organization/teacher/disable',
    method: 'get',
    params
  })
}
// 查询学员列表
export function listUser(query: any): any {
  return request({
    url: '/organization/user/list',
    method: 'get',
    headers: {
      needOrgId: true
    },
    params: query
  })
}
// 新增学员
export function addUser(data: any): any {
  return request({
    url: '/organization/user/addUser',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 修改学员
export function updateUser(data: any): any {
  return request({
    url: '/organization/user/updateUser',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 启用禁用学员
export function changeUserDisable(params: any): any {
  return request({
    url: '/organization/user/disable',
    method: 'get',
    params
  })
}

// 查询老师密码
export function showPasswordFn(params: any): any {
  return request({
    url: `/organization/teacher/showPassword`,
    method: 'get',
    params
    
  })
}

// 获取学生详情
export function getUserDetail(id: string): any {
  return request({
    url: `/organization/user/${id}`,
    method: 'get',
    headers: {
      needOrgId: true
    }
  })
}
// 获取老师详情
export function getTeacherDetail(id: string): any {
  return request({
    url: `/organization/teacher/detail?id=${id}`,
    method: 'get',
    headers: {
      needOrgId: true
    }
  })
}
// 修改老师账号状态
export function updateAccountStatus(data: any): any {
  const {type,...params} = data
  let url = '/organization/teacher/disable'
  if (type === 'client') {
    url = '/organization/teacher/app/disable'
  } 
  return request({
    url,
    method: 'get',
    params,
  })
}


export function showJobPwd(params: any): any {
  return request({
    url: `/organization/teacher/showJobPwd`,
    method: 'get',
    params
  })
}


// 修改老师密码
export function updateTeacherPassword(data: any): any {
  const {oldPwd,newPwd, ...params} = data
  return request({
    url: '/organization/teacher/updatePwd',
    method: 'post',
    params: {
      ...params,
      pwd: newPwd,
      oldPwd
    },
    // headers: {
    //   needOrgId: true
    // }
  })
}

// 修改学生密码
export function updateUserPassword(data: any): any {
  return request({
    url: '/organization/user/updatePwd',
    method: 'post',
    params: data,
    // headers: {
    //   needOrgId: true
    // }
  })
}

// 获取学生密码
export function showUserPassword(params: any): any {
  return request({
    url: `/organization/user/showPassword`,
    method: 'get',
    params
  })
}

// 获取学生作品列表
export function listUserWorks(query: any): any {
  return request({
    url: '/organization/user/task/list',
    method: 'get',
    headers: {
      needOrgId: true
    },
    params: query
  })
}

// 下载学生作品
export function downloadUserWork(id: string): any {
  return request({
    url: `/organization/user/works/download/${id}`,
    method: 'get',
    responseType: 'blob',
    headers: {
      needOrgId: true
    }
  })
}
