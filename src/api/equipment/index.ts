/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-11 14:23:08
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-04-23 17:11:37
 * @FilePath: /miaobi-admin-magic-touch/src/api/equipment/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询设备列表
export function getEquipmentList(data: any): any {
  return request({
    url: '/device/list',
    method: 'post',
    data: data,
  })
}
// 设备序列号列表
export function getEquipmentModelList(query: any ={}): any {
  return request({
    url: '/device/sn/list',
    method: 'get',
    params: query,
    headers: {
      needOrgId: true
    }
  })
}
// 查询设备详细
export function getEquipmentDetail(id: any): any {
  return request({
    url: '/device/detail',
    method: 'get',
    params: { id },
  })
}

// 新增设备
export function addEquipment(data: any): any {
  return request({
    url: '/device/create',
    method: 'post',
    data: data,
  })
}

// 修改设备
export function updateEquipment(data: any): any {
  return request({
    url: '/device/update',
    method: 'post',
    data: data,
  })
}

// 删除设备
export function deleteEquipment(id: any): any {
  return request({
    url: '/device/update',
    method: 'post',
    data: {
      id,
      del: 1,
    },
  })
}

// 获取机构列表
export function getOrgList(query: any): any {
  return request({
    url: '/system/organization/list/all',
    method: 'get',
    params: query,
  })
}

// 导出设备数据
export function exportEquipment(query: any): any {
  return request({
    url: '/equipment/export',
    method: 'get',
    params: query,
    responseType: 'blob',
  })
}
