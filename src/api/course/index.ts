/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/api/course/index.ts
 * @Description: TypeScript版本 - 自动转换
 */

import request from '@/utils/request'

// 查询课件列表
export function listCourse(params: any): any {
  const { pageNum, pageSize, ...data } = params
  return request({
    url: '/course/detail/list',
    method: 'post',
    params: {pageNum, pageSize},
    headers: {
      needOrgId: true
    },
    data
  })
}
export function listCourseCopy(params: any): any {
  const { pageNum, pageSize, ...data } = params
  return request({
    url: '/course/detail/listForCopy',
    method: 'post',
    params: {pageNum, pageSize},
    headers: {
      needOrgId: true
    },
    data
  })
}
// 新增课件
export function addCourse(data: any): any {
  return request({
    url: '/course/detail/create',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 编辑课件
export function editCourse(data: any): any {
  return request({
    url: '/course/detail/update',
    method: 'put',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 查看课件详情
export function checkCourseDetail(id: any): any {
  return request({
    url: `/course/detail/${id}`,
    method: 'get',
  })
}

// 启用禁用课件
export function changeCourseDisable(params: any): any {
  return request({
    url: '/course/detail/changeStatus',
    method: 'put',
    params
  })
}
// 删除课件
export function removeCourse(params: any): any {
  return request({
    url: '/course/detail/remove',
    method: 'delete',
    params
  })
}


// 官方提供（课包列表）
export function listPackageOrg(params: any): any {
  const { pageNum, pageSize, ...data } = params
  return request({
    url: '/course/package/listToOrg',
    method: 'post',
    params: {pageNum, pageSize},
    data
  })
}

// 启用禁用课包（官方提供）
export function changePackageOrgDisable(params: any): any {
  return request({
    url: '/course/package/changeStatusToOrg',
    method: 'put',
    params
  })
}

// 查询课包列表
export function listPackage(params: any): any {
  const { pageNum, pageSize, ...data } = params
  return request({
    url: '/course/package/list',
    params: {pageNum, pageSize},
    method: 'post',
    headers: {
      needOrgId: true
    },
    data
  })
}
// 新增课包
export function addPackage(data: any): any {
  return request({
    url: '/course/package/create',
    method: 'post',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 编辑课包
export function editPackage(data: any): any {
  return request({
    url: '/course/package/update',
    method: 'put',
    data: data,
    headers: {
      needOrgId: true
    },
  })
}
// 查看课包详情
export function checkPackageDetail(id: any): any {
  return request({
    url: `/course/package/${id}`,
    method: 'get',
  })
}

// 启用禁用课包
export function changePackageDisable(params: any): any {
  return request({
    url: '/course/package/changeStatus',
    method: 'put',
    params
  })
}
// 删除课包
export function removePackage(params: any): any {
  return request({
    url: '/course/package/remove',
    method: 'delete',
    params
  })
}
