/**
 * 客户端数据接收器
 * 提供简单的全局方法接收客户端通过webview传输的数据
 */

type ClientDataCallback = (data: any) => void

class ClientDataReceiver {
  private callbacks: ClientDataCallback[] = []

  /**
   * 注册数据接收回调
   */
  public onReceive(callback: ClientDataCallback): void {
    this.callbacks.push(callback)
  }

  /**
   * 移除数据接收回调
   */
  public offReceive(callback: ClientDataCallback): void {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  /**
   * 接收客户端数据的全局方法
   */
  public receiveData(data: any): void {
    try {
      console.log('收到客户端数据:', data)
      
      // 通知所有注册的回调
      this.callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('处理客户端数据回调时出错:', error)
        }
      })
    } catch (error) {
      console.error('接收客户端数据时出错:', error)
    }
  }
}

// 创建全局实例
const clientReceiver = new ClientDataReceiver()

// 挂载到window对象
declare global {
  interface Window {
    receiveClientData: (data: any) => void
    clientReceiver: ClientDataReceiver
  }
}

window.receiveClientData = (data: any) => clientReceiver.receiveData(data)
window.clientReceiver = clientReceiver

export default clientReceiver