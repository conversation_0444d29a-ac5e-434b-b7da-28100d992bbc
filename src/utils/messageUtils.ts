import { getVideoBase64 } from '@/components/TUIKit/utils/tool'
import useChatStore from '@/store/modules/chat'
import { jsbridge } from 'msb-public-library'
import { nextTick } from 'vue'
import { isElectron, isMobileNative } from '@/components/TUIKit/utils/env'
import { ElMessage } from 'element-plus'

// 为Electron API添加类型声明
declare global {
  interface Window {
    electronAPI?: {
      saveMedia: (urls: string[]) => void
    }
  }
}

interface QuoteMessage {
  messageID: string
  data: string
  type: string
  userName: string
  cover: string
}

/**
 * 根据消息类型获取唯一标识符用于去重
 * @param item QuoteMessage 对象
 * @returns 唯一标识符字符串
 */
function getQuoteItemUniqueKey(item: QuoteMessage): string {
  // 文本类型使用 messageID 去重
  if (item.type === 'text') {
    return `messageID:${item.messageID}`
  }
  // 非文本类型优先使用 data 内容去重，如果 data 为空则回退到 messageID
  const dataKey = item.data && item.data.trim() !== '' ? item.data : item.messageID
  return `data:${dataKey}`
}

/**
 * 对引用列表进行去重处理
 * @param quoteList 引用列表
 * @returns 去重后的引用列表
 */
function deduplicateQuoteList(quoteList: QuoteMessage[]): QuoteMessage[] {
  // 使用 Map 进行高效去重
  const uniqueMap = new Map<string, QuoteMessage>()
  quoteList.forEach(item => {
    const key = getQuoteItemUniqueKey(item)
    uniqueMap.set(key, item)
  })
  return Array.from(uniqueMap.values())
}

/**
 * 添加消息到引用列表
 * @param message 消息对象
 * @param customData 自定义数据对象（视频、封面等）
 */
export const addMessageToQuoteList = (message: any, customData?: { video?: string; poster?: string }) => {
  const chatStore = useChatStore()
  const quoteMessage: QuoteMessage = {
    messageID: message.ID || '',
    data: '',
    type: '',
    userName: message.nick || '',
    cover: '',
  }

  // 如果是音视频相关的自定义引用
  if (customData?.video) {
    quoteMessage.data = customData.video
    quoteMessage.type = 'video'
    quoteMessage.cover = customData.poster || ''
  }
  // 根据消息来源解析数据
  else if (message.from?.includes('agent')) {
    const payloadData = JSON.parse(message.payload?.data || '{}')
    if (payloadData?.text) {
      quoteMessage.data = payloadData.text
      quoteMessage.type = 'text'
    } else if (payloadData?.images?.length) {
      // 处理新的图片数组格式，提取所有有效的图片URL
      const imageUrls = payloadData.images
        .filter((image: any) => {
          // 过滤掉无效的图片项
          if (typeof image === 'string') return image.trim() !== ''
          if (typeof image === 'object' && image !== null) {
            return image.url && image.url.trim() !== ''
          }
          return false
        })
        .map((image: any) => {
          // 提取URL，兼容字符串和对象格式
          if (typeof image === 'string') return image
          if (typeof image === 'object' && image !== null && image.url) {
            return image.url
          }
          return ''
        })
        .filter((url: string) => url !== '') // 再次过滤空字符串

      if (imageUrls.length > 0) {
        quoteMessage.data = imageUrls.join(',')
        quoteMessage.type = 'image'
      }
    } else if (payloadData?.video?.url) {
      quoteMessage.data = payloadData.video.url
      quoteMessage.type = 'video'
      quoteMessage.cover = payloadData.video.cover
    } else if (payloadData?.audio?.url) {
      quoteMessage.data = payloadData.audio.url
      quoteMessage.type = 'audio'
    }
  }
  // 处理图片消息
  else if (message.type === 'TIMImageElem' && message.payload?.imageInfoArray?.length > 0) {
    quoteMessage.data = message.payload.imageInfoArray[0].imageUrl
    quoteMessage.type = 'image'
  }
  // 普通消息
  else {
    if (message.type === 'TIMTextElem') {
      quoteMessage.data = message.payload?.text || ''
      quoteMessage.type = 'text'
    } else if (message.type === 'TIMCustomElem') {
      const payloadData = JSON.parse(message.payload?.data || '{}')
      if (payloadData?.content?.name === 'image') {
        quoteMessage.data = payloadData?.content?.data[0]?.url
        quoteMessage.type = 'image'
      } else if (payloadData?.content?.name === 'video') {
        quoteMessage.data = payloadData?.content?.data[0]?.url
        quoteMessage.type = 'video'
        quoteMessage.cover = payloadData?.content?.data[0]?.cover
      } else if (payloadData?.content?.name === 'cmd_msg') {
        quoteMessage.data = payloadData?.content?.data?.text
        quoteMessage.type = 'text'
      }
    } else {
      quoteMessage.data = message.payload?.text || ''
      quoteMessage.type = 'text'
    }
  }

  // 添加到引用列表并去重
  const initQuoteList = chatStore.quoteList
  const newQuoteList = [...initQuoteList, quoteMessage]
  const uniqueQuoteList = deduplicateQuoteList(newQuoteList)

  chatStore.setQuoteList(uniqueQuoteList)
  scrollStreamMessageToBottom()
  return uniqueQuoteList
}

/**
 * 添加指定索引的消息到引用列表
 * @param message 消息对象
 * @param index 图片索引
 * @param customData 自定义数据对象（视频、封面等）
 */
export const addMessageToQuoteListByIndex = (message: any, index: number = 0, customData?: { video?: string; poster?: string }) => {
  const chatStore = useChatStore()
  const quoteMessage: QuoteMessage = {
    messageID: message.ID || '',
    data: '',
    type: '',
    userName: message.nick || '',
    cover: '',
  }

  // 如果是音视频相关的自定义引用
  if (customData?.video) {
    quoteMessage.data = customData.video
    quoteMessage.type = 'video'
    quoteMessage.cover = customData.poster || ''
  }
  // 根据消息来源解析数据
  else if (message.from?.includes('agent')) {
    const payloadData = JSON.parse(message.payload?.data || '{}')
    if (payloadData?.text) {
      quoteMessage.data = payloadData.text
      quoteMessage.type = 'text'
    } else if (payloadData?.images?.length) {
      // 处理指定索引的图片
      // 首先检查是否包含 LONG_IMAGE 类型的图片
      const longImageItems = payloadData.images.filter((img: any) => {
        return typeof img === 'object' && img.type === 'LONG_IMAGE'
      })

      let imageUrl = ''

      if (longImageItems.length > 0 && index === 1) {
        // 如果有 LONG_IMAGE 类型的图片且索引为1，取第一个 LONG_IMAGE
        const longImage = longImageItems[0]
        if (longImage.url && longImage.url.trim() !== '') {
          imageUrl = longImage.url
        }
      } else if (index >= 0 && index < payloadData.images.length) {
        // 正常的索引处理
        const image = payloadData.images[index]
        if (typeof image === 'string' && image.trim() !== '') {
          imageUrl = image
        } else if (typeof image === 'object' && image !== null && image.url && image.url.trim() !== '') {
          imageUrl = image.url
        }
      }

      if (imageUrl) {
        quoteMessage.data = imageUrl
        quoteMessage.type = 'image'
      }
    } else if (payloadData?.video?.url) {
      quoteMessage.data = payloadData.video.url
      quoteMessage.type = 'video'
      quoteMessage.cover = payloadData.video.cover
    } else if (payloadData?.audio?.url) {
      quoteMessage.data = payloadData.audio.url
      quoteMessage.type = 'audio'
    }
  }
  // 处理图片消息
  else if (message.type === 'TIMImageElem' && message.payload?.imageInfoArray?.length > 0) {
    if (index >= 0 && index < message.payload.imageInfoArray.length) {
      quoteMessage.data = message.payload.imageInfoArray[index].imageUrl
      quoteMessage.type = 'image'
    }
  }
  // 普通消息
  else {
    if (message.type === 'TIMTextElem') {
      quoteMessage.data = message.payload?.text || ''
      quoteMessage.type = 'text'
    } else if (message.type === 'TIMCustomElem') {
      const payloadData = JSON.parse(message.payload?.data || '{}')
      if (payloadData?.content?.name === 'image') {
        quoteMessage.data = payloadData?.content?.data[0]?.url
        quoteMessage.type = 'image'
      } else if (payloadData?.content?.name === 'video') {
        quoteMessage.data = payloadData?.content?.data[0]?.url
        quoteMessage.type = 'video'
        quoteMessage.cover = payloadData?.content?.data[0]?.cover
      } else if (payloadData?.content?.name === 'cmd_msg') {
        quoteMessage.data = payloadData?.content?.data?.text
        quoteMessage.type = 'text'
      }
    } else {
      quoteMessage.data = message.payload?.text || ''
      quoteMessage.type = 'text'
    }
  }

  // 添加到引用列表并去重
  const initQuoteList = chatStore.quoteList
  const newQuoteList = [...initQuoteList, quoteMessage]
  const uniqueQuoteList = deduplicateQuoteList(newQuoteList)

  chatStore.setQuoteList(uniqueQuoteList)
  scrollStreamMessageToBottom()
  return uniqueQuoteList
}

/**
 * 从消息中提取媒体URL
 * @param message 消息对象
 * @returns 提取的媒体URL数组
 */
export const extractMediaUrlsFromMessage = (message: any): string[] => {
  if (!message) return []

  let urls: string[] = []
  try {
    if (message.from?.includes('agent')) {
      const customData = JSON.parse(message?.payload?.data || '{}')
      if (customData?.video?.url) {
        urls = [customData.video.url]
      } else if (customData?.images?.length) {
        // 处理新的图片数组格式，支持不同类型的图片对象
        urls = customData.images
          .filter((image: any) => {
            // 过滤掉无效的图片项
            if (typeof image === 'string') return image.trim() !== ''
            if (typeof image === 'object' && image !== null) {
              return image.url && image.url.trim() !== ''
            }
            return false
          })
          .map((image: any) => {
            // 提取URL，兼容字符串和对象格式
            if (typeof image === 'string') return image
            if (typeof image === 'object' && image !== null && image.url) {
              return image.url
            }
            return ''
          })
          .filter((url: string) => url !== '') // 再次过滤空字符串
      }
    } else if (message.type === 'TIMImageElem' && message.payload?.imageInfoArray?.length > 0) {
      // 处理TIMImageElem类型的消息
      urls = message.payload.imageInfoArray.map((item: any) => item.imageUrl)
    } else {
      const customData = JSON.parse(message?.payload?.data || '{}')
      if (customData?.content?.data?.[0]?.url) {
        urls = [customData.content.data[0].url]
      }
    }
  } catch (error) {}
  return urls
}

/**
 * Electron环境下保存媒体文件
 * @param message 消息对象
 * @returns 是否保存成功
 */
export const saveMediaInElectron = (message: any): boolean => {
  const urls = extractMediaUrlsFromMessage(message)
  if (urls.length === 0) {
    return false
  }

  try {
    // 调用Electron的API进行文件保存操作
    // if (window.electronAPI) {
    //   window.electronAPI.saveMedia(urls)
    //   return true
    // } else {
    //
    //   return false
    // }

    window.parent?.postMessage(JSON.stringify(urls), '*')
    return true
  } catch (error) {
    return false
  }
}

/**
 * 从消息中提取指定索引的媒体URL
 * @param message 消息对象
 * @param index 图片索引
 * @returns 提取的媒体URL数组
 */
export const extractMediaUrlsFromMessageByIndex = (message: any, index: number = 0): string[] => {
  if (!message) return []

  let urls: string[] = []
  try {
    if (message.from?.includes('agent')) {
      const customData = JSON.parse(message?.payload?.data || '{}')
      if (customData?.video?.url) {
        // 视频只有一个，索引无意义，直接返回
        urls = [customData.video.url]
      } else if (customData?.images?.length) {
        // 处理指定索引的图片
        // 首先检查是否包含 LONG_IMAGE 类型的图片
        const longImageItems = customData.images.filter((img: any) => {
          return typeof img === 'object' && img.type === 'LONG_IMAGE'
        })
        if (longImageItems.length > 0) {
          // 如果有 LONG_IMAGE 类型的图片且索引为1，取第一个 LONG_IMAGE
          const longImage = longImageItems[1]
          if (longImage.url && longImage.url.trim() !== '') {
            urls = [longImage.url]
          }
        } else if (index >= 0 && index < customData.images.length) {
          // 正常的索引处理
          const image = customData.images[index]
          if (typeof image === 'string' && image.trim() !== '') {
            urls = [image]
          } else if (typeof image === 'object' && image !== null && image.url && image.url.trim() !== '') {
            urls = [image.url]
          }
        }
      }
    } else if (message.type === 'TIMImageElem' && message.payload?.imageInfoArray?.length > 0) {
      // 处理TIMImageElem类型的消息
      if (index >= 0 && index < message.payload.imageInfoArray.length) {
        urls = [message.payload.imageInfoArray[index].imageUrl]
      }
    } else {
      const customData = JSON.parse(message?.payload?.data || '{}')
      if (customData?.content?.data?.[0]?.url) {
        // 这种情况通常只有一个媒体文件
        urls = [customData.content.data[0].url]
      }
    }
  } catch (error) {}
  return urls
}

/**
 * 通过JSBridge发送保存指定索引媒体文件请求
 * @param message 消息对象
 * @param index 图片索引
 * @param callback 完成后的回调函数
 */
export const saveMediaViaJSBridgeByIndex = (message: any, index: number = 0, callback?: () => void) => {
  const urls: any = extractMediaUrlsFromMessageByIndex(message, index)
  console.log('🚀 ~ urls:', urls)

  if (urls.length === 0) {
    return false
  }

  // 判断环境并执行相应的保存方法
  if (isElectron()) {
    // Electron环境

    window.parent?.postMessage(
      JSON.stringify({
        action: 'saveImagesOrVideos',
        params: { urls: urls },
      }),
      '*'
    )
  } else if (isMobileNative()) {
    // iOS/Android原生环境

    jsbridge?.sendMsg({
      action: 'saveImagesOrVideos',
      params: { urls: urls },
    })
  } else {
    // 浏览器环境，使用原生下载方法

    try {
      urls.forEach((url: string) => {
        fetch(url)
          .then(res => res.blob())
          .then(blob => {
            const a = document.createElement('a')
            const url = window.URL.createObjectURL(blob)
            a.href = url
            a.download = url.split('/').pop() || 'download'
            a.click()
          })
      })
    } catch (error) {
      return false
    }
  }

  if (callback) {
    setTimeout(callback, 100)
  }

  return true
}

/**
 * 通过JSBridge发送保存媒体文件请求
 * @param message 消息对象
 * @param callback 完成后的回调函数
 */
export const saveMediaViaJSBridge = (message: any, callback?: () => void) => {
  const urls: any = extractMediaUrlsFromMessage(message)

  if (urls.length === 0) {
    return false
  }

  // 判断环境并执行相应的保存方法
  if (isElectron()) {
    // Electron环境

    window.parent?.postMessage(
      JSON.stringify({
        action: 'saveImagesOrVideos',
        params: { urls: urls },
      }),
      '*'
    )
  } else if (isMobileNative()) {
    // iOS/Android原生环境

    jsbridge?.sendMsg({
      action: 'saveImagesOrVideos',
      params: { urls: urls },
    })
  } else {
    // 浏览器环境，使用原生下载方法

    try {
      urls.forEach((url: string) => {
        fetch(url)
          .then(res => res.blob())
          .then(blob => {
            const a = document.createElement('a')
            const url = window.URL.createObjectURL(blob)
            a.href = url
            a.download = url.split('/').pop() || 'download'
            a.click()
          })
      })
    } catch (error) {
      return false
    }
  }

  if (callback) {
    setTimeout(callback, 100)
  }

  return true
}

/**
 * 通过JSBridge加入作品集
 * @param message 消息对象
 * @param callback 完成后的回调函数
 */
export const joinMediaViaJSBridge = async (message: any, index: number, callback?: () => void) => {
  let params: any = {}
  if (message.from.includes('agent')) {
    const customData = JSON.parse(message?.payload?.data)
    if (customData?.video?.url) {
      const { width, height } = await getVideoBase64(customData?.video?.url)
      params = {
        videoUrl: customData?.video?.url,
        videoCoverUrl: customData?.video?.cover,
        videoCoverWidth: width,
        videoCoverHeight: height,
      }
      // 判断环境并执行相应的保存方法
      if (isMobileNative()) {
        // iOS/Android原生环境

        jsbridge?.sendMsg({
          action: 'saveVideoToMyWork',
          params,
        })
      } else {
        // Electron环境
        // return saveMediaInElectron(message)
        window.parent?.postMessage(
          JSON.stringify({
            action: 'saveVideoToMyWork',
            params,
          }),
          '*'
        )
      }
    } else if (customData?.images?.length) {
      // 获取图片宽高
      const img = new Image()

      // 处理新的图片数组格式，根据LONG_IMAGE类型和索引获取图片URL
      let url = ''
      let cover = ''
      // 首先检查是否包含 LONG_IMAGE 类型的图片
      const longImageItems = customData.images.filter((img: any) => {
        return typeof img === 'object' && img.type === 'LONG_IMAGE'
      })

      if (longImageItems.length > 0) {
        // 如果有 LONG_IMAGE 类型的图片且索引为1，取第一个 LONG_IMAGE
        const longImage = longImageItems[1]
        const longCover = longImageItems[0]
        if (longImage.url && longImage.url.trim() !== '') {
          url = longImage.url
          cover = longCover.url
        }
      } else if (index >= 0 && index < customData.images.length) {
        // 按照指定索引获取图片
        const targetImage = customData.images[index]
        if (typeof targetImage === 'string') {
          url = targetImage
          cover = targetImage
        } else if (typeof targetImage === 'object' && targetImage !== null && targetImage.url) {
          url = targetImage.url
          cover = targetImage.url
        }
      }

      if (!url) {
        return
      }
      img.src = cover

      const toast = ElMessage.info('保存中...')
      // 获取宽高赋值params
      await new Promise(resolve => {
        img.onload = () => {
          resolve({ width: img.width, height: img.height })
        }
      })
      params = {
        cover: cover,
        imgUrl: url,
        width: img.width,
        height: img.height,
      }
      console.log('🚀 ~ messageUtils.ts:575 ~ joinMediaViaJSBridge ~ params:', params)
      // 判断环境并执行相应的保存方法
      if (isMobileNative()) {
        // iOS/Android原生环境

        jsbridge?.sendMsg({
          action: 'saveImageToMyWork',
          params,
        })
      } else {
        // Electron环境

        window.parent?.postMessage(
          JSON.stringify({
            action: 'saveImageToMyWork',
            params,
          }),
          '*'
        )
      }
      toast.close()
    }
  } else {
    const customData = JSON.parse(message?.payload?.data)

    if (customData?.content?.name === 'video') {
      params = {
        videoUrl: customData?.content?.data[0]?.url,
        videoCoverUrl: customData?.content?.data[0]?.cover,
        videoCoverWidth: customData?.content?.data[0]?.width,
        videoCoverHeight: customData?.content?.data[0]?.height,
      }

      if (isMobileNative()) {
        // iOS/Android原生环境

        jsbridge?.sendMsg({
          action: 'saveVideoToMyWork',
          params,
        })
      } else {
        // return saveMediaInElectron(message)
        window.parent?.postMessage(
          JSON.stringify({
            action: 'saveVideoToMyWork',
            params,
          }),
          '*'
        )
      }
    } else if (customData?.content?.name === 'image') {
      params = {
        imgUrl: customData?.content?.data[0]?.url,
        imgCoverUrl: customData?.content?.data[0]?.url,
        width: customData?.content?.data[0]?.width,
        height: customData?.content?.data[0]?.height,
      }

      if (isMobileNative()) {
        // iOS/Android原生环境

        jsbridge?.sendMsg({
          action: 'saveImageToMyWork',
          params,
        })
      } else {
        // return saveMediaInElectron(message)
        window.parent?.postMessage(
          JSON.stringify({
            action: 'saveImageToMyWork',
            params,
          }),
          '*'
        )
      }
    }
  }

  if (callback) {
    setTimeout(callback, 100)
  }
}

/**
 * 滚动消息列表到底部
 * 该方法可以在任何地方调用，用于将消息列表滚动到最新消息位置
 */
export const scrollToLatestMessage = () => {
  nextTick(() => {
    const messageListElement = document.getElementById('messageScrollList')
    if (messageListElement) {
      messageListElement.scrollTop = messageListElement.scrollHeight
    }
  })
}

/**
 * 滚动消息列表到指定位置
 * @param config 滚动配置
 * @param config.scrollToBottom 是否滚动到底部
 * @param config.scrollToMessage 滚动到指定消息
 * @param config.scrollToOffset 滚动到指定偏移量
 */
export const scrollToPosition = (config: {
  scrollToBottom?: boolean
  scrollToMessage?: any
  scrollToOffset?: {
    top?: number
    bottom?: number
  }
}) => {
  return new Promise<void>(resolve => {
    nextTick(() => {
      const messageListElement = document.getElementById('messageScrollList')
      if (!messageListElement) {
        resolve()
        return
      }

      if (config.scrollToBottom) {
        messageListElement.scrollTop = messageListElement.scrollHeight
      } else if (config.scrollToMessage?.ID) {
        const targetElement = document.getElementById(`tui-${config.scrollToMessage.ID}`)
        if (targetElement) {
          targetElement.scrollIntoView({ block: 'center' })
        }
      } else if (config.scrollToOffset) {
        if (config.scrollToOffset.top !== undefined) {
          messageListElement.scrollTop = config.scrollToOffset.top
        } else if (config.scrollToOffset.bottom !== undefined) {
          messageListElement.scrollTop = messageListElement.scrollHeight - config.scrollToOffset.bottom
        }
      }
      resolve()
    })
  })
}

/**
 * 滚动流式消息到底部
 * 用于处理流式消息（如AI回复）时保持滚动到底部
 */
export const scrollStreamMessageToBottom = () => {
  // 移除debugger语句
  nextTick(() => {
    // 尝试多种可能的选择器
    const messageListElement = document.getElementById('messageScrollList') || document.querySelector('.message-list') || document.querySelector('.tui-message-list')

    if (!messageListElement) {
      return
    }

    // 直接滚动到底部，不做额外判断
    messageListElement.scrollTop = messageListElement.scrollHeight

    // 如果上面的方法不生效，尝试使用scrollTo方法
    messageListElement.scrollTo({
      top: messageListElement.scrollHeight,
      behavior: 'auto',
    })
  })
}

/**
 * 滚动流式消息到底部
 * 用于处理流式消息（如AI回复）时保持滚动到底部
 */
export const audioPlayStatusJSBridge = (status: 'play' | 'pause' | 'stop') => {
  jsbridge?.sendMsg({
    action: 'audioPlayStatus',
    params: { status },
  })
}
