/*
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-03-28 15:40:04
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-03-28 15:40:39
 * @FilePath: src/utils/generator/css.ts
 * @Description: TypeScript版本 - 自动转换
 */

const styles = {
  'el-rate': '.el-rate{display: inline-block; vertical-align: text-top;}',
  'el-upload': '.el-upload__tip{line-height: 1.2;}'
}

function addCss(cssList: any, el: any): any {
  const css = styles[el.tag]
  css && cssList.indexOf(css) === -1 && cssList.push(css)
  if (el.children) {
    el.children.forEach(el2 => addCss(cssList, el2))
  }
}

export function makeUpCss(conf: any): any {
  const cssList = []
  conf.fields.forEach(el => addCss(cssList, el))
  return cssList.join('\n')
}
