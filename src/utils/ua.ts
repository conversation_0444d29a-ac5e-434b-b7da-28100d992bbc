/*
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-04-25 18:32:16
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-04-25 20:15:49
 * @FilePath: /miaobi-admin-magic-touch/src/utils/ua.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * 解析 query格式字符串为 object
 */
export function parseQuery(query: string): { [key: string]: string } {
  const queryWithoutStart: string = query.indexOf('?') === 0 ? query.substring(1) : query
  const uaFormated: { [key: string]: string } = {}
  const strs: string[] = queryWithoutStart.split('&')
  for (const querySingle of strs) {
    uaFormated[querySingle.split('=')[0]] = unescape(querySingle.split('=')[1])
  }
  return uaFormated
}
export const uaParsed: { [key: string]: string } = typeof navigator !== 'undefined' ? parseQuery(navigator.userAgent) : {}

export const isMSB1V1App = () => {
  //   const uaParsed1: any = typeof navigator !== 'undefined' && typeof uaParsed.ua !== 'undefined' && (uaParsed.ua.includes('ydy') || uaParsed.ua.includes('vwb'))
  //   return uaParsed1
  const { appName } = parseQuery(location.search)
  return !appName
}

