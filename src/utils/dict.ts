/*
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-01-15 16:09:17
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-01-21 11:24:37
 * @FilePath: /miaobi-admin-magic-touch/src/utils/dict.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEß
 */
import useDictStore from "@/store/modules/dict";
import { getDicts } from "@/api/system/dict/data";

/**
 * 获取字典数据
 */
export function useDict(...args: any): any {
  const res = ref({});
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDicts(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));
          useDictStore().setDict(dictType, res.value[dictType]);
        });
      }
    });
    return toRefs(res.value);
  })();
}
