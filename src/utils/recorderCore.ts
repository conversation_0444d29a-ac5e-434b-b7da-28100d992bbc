/**
 * recorderCore.ts
 * 封装 recorder-core 并使用 RecordApp 提供跨平台录音支持
 */
//必须引入的Recorder核心（文件路径是 /src/recorder-core.js 下同），使用import、require都行；recorder-core会自动往window（浏览器环境）或Object（非浏览器环境）下挂载名称为Recorder对象，全局可调用Recorder
import 'recorder-core' //注意如果未引用Recorder变量，可能编译时会被优化删除（如vue3 tree-shaking），请改成 import 'recorder-core'，或随便调用一下 Recorder.a=1 保证强引用

//按需引入你需要的录音格式支持文件，如果需要多个格式支持，把这些格式的编码引擎js文件统统引入进来即可
import 'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine' //如果此格式有额外的编码引擎（*-engine.js）的话，必须要加上

//可选的插件支持项，把需要的插件按需引入进来即可
import 'recorder-core/src/extensions/waveview'

/****以上均为Recorder的相关文件，下面是RecordApp需要的支持文件****/

//必须引入的RecordApp核心文件（文件路径是 /src/app-support/app.js）。注意：app.js会自动往window（浏览器环境）或Object（非浏览器环境）下挂载名称为RecordApp对象，全局可调用RecordApp
import RecordApp from 'recorder-core/src/app-support/app'

//引入特定平台环境下的支持文件（也可以统统引入进来，非对应的环境下运行时会忽略掉）
//import 'recorder-core/src/app-support/app-native-support.js' //App下的原生录音支持文件（App中未提供原生支持时可以不提供，统统走H5录音）
//import 'recorder-core/src/app-support/app-miniProgram-wx-support.js' //微信小程序下的录音支持文件
//import '@/uni_modules/Recorder-UniCore/app-uni-support.js' //uni-app下的支持文件，请参考本文档目录下的demo_UniApp测试项目

//ts import 提示：npm包内已自带了.d.ts声明文件（不过是any类型）

// 声明全局 Recorder 对象
declare global {
  interface Window {
    Recorder: any
  }
  var Recorder: any
}

// 修补 Recorder，添加缺失的 i18n 对象
// 由于 mp3.js 引擎尝试访问 Recorder.i18n，我们需要在使用前为其提供 i18n 对象
if (typeof window !== 'undefined' && window.Recorder && !window.Recorder.i18n) {
  // 创建基本的 i18n 对象
  window.Recorder.i18n = {
    // 提供一个 $T 函数，用于处理翻译
    $T: (text: string) => text,
    // 可以在这里添加更多i18n相关属性和方法
  }
} else if (typeof globalThis !== 'undefined' && (globalThis as any).Recorder && !(globalThis as any).Recorder.i18n) {
  // 非浏览器环境
  ;(globalThis as any).Recorder.i18n = {
    $T: (text: string) => text,
  }
}

// 导出 RecordApp 作为主要的录音接口
export { RecordApp }

// 导出 Recorder 以便需要时使用
export default typeof window !== 'undefined' ? window.Recorder : (globalThis as any).Recorder

// RecordApp 录音器接口定义
export interface RecordAppOptions {
  type?: string
  sampleRate?: number
  bitRate?: number
  onProcess?: (buffers: any, powerLevel: number, bufferDuration: number, bufferSampleRate: number, newBufferIdx: number, asyncEnd: any) => void
  audioTrackSet?: {
    echoCancellation?: boolean
    noiseSuppression?: boolean
    autoGainControl?: boolean
  }
}

// RecordApp 录音器类
export class RecordAppRecorder {
  private options: RecordAppOptions
  private isRecording: boolean = false
  private hasPermission: boolean = false
  private processTime: number = 0
  private watchDogTimer: number = 0
  private wdtPauseT: number = 0

  constructor(options: RecordAppOptions = {}) {
    this.options = {
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16,
      ...options,
    }
  }

  // 请求录音权限
  async requestPermission(): Promise<void> {
    return new Promise((resolve, reject) => {
      RecordApp.RequestPermission(
        () => {
          this.hasPermission = true
          resolve()
        },
        (msg: string, isUserNotAllow: boolean) => {
          const errorMsg = isUserNotAllow ? '您拒绝了麦克风权限' : `无法录音: ${msg}`
          reject(new Error(errorMsg))
        }
      )
    })
  }

  // 开始录音
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.hasPermission) {
        reject(new Error('请先请求录音权限'))
        return
      }

      const startTime = Date.now()
      this.processTime = 0
      this.wdtPauseT = 0

      RecordApp.Start(
        {
          ...this.options,
          onProcess: (buffers: any, powerLevel: number, bufferDuration: number, bufferSampleRate: number, newBufferIdx: number, asyncEnd: any) => {
            this.processTime = Date.now()
            // 调用用户提供的 onProcess 回调
            if (this.options.onProcess) {
              this.options.onProcess(buffers, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd)
            }
          },
        },
        () => {
          this.isRecording = true

          // 启动看门狗定时器
          if (RecordApp.Current.CanProcess()) {
            this.watchDogTimer = window.setInterval(() => {
              if (this.watchDogTimer === 0) {
                clearInterval(this.watchDogTimer)
                return
              }
              if (Date.now() < this.wdtPauseT) return
              if (Date.now() - (this.processTime || startTime) > 1500) {
                clearInterval(this.watchDogTimer)
                this.watchDogTimer = 0
                console.error(this.processTime ? '录音被中断' : '录音未能正常开始')
                reject(new Error(this.processTime ? '录音被中断' : '录音未能正常开始'))
              }
            }, 1000)
          }

          resolve()
        },
        (msg: string) => {
          reject(new Error(`开始录音失败：${msg}`))
        }
      )
    })
  }

  // 暂停录音
  pause(): void {
    if (this.isRecording && RecordApp.GetCurrentRecOrNull()) {
      RecordApp.Pause()
      this.wdtPauseT = Date.now() * 2 // 永不监控onProcess超时
    }
  }

  // 继续录音
  resume(): void {
    if (this.isRecording && RecordApp.GetCurrentRecOrNull()) {
      RecordApp.Resume()
      this.wdtPauseT = Date.now() + 1000 // 1秒后再监控onProcess超时
    }
  }

  // 停止录音
  async stop(): Promise<{ arrayBuffer: ArrayBuffer; duration: number; mime: string; blob?: Blob }> {
    return new Promise((resolve, reject) => {
      this.watchDogTimer = 0 // 停止监控onProcess超时

      RecordApp.Stop(
        (arrayBuffer: ArrayBuffer, duration: number, mime: string) => {
          this.isRecording = false

          // 如果当前环境支持Blob，也可以直接构造成Blob文件对象
          let blob: Blob | undefined
          if (typeof Blob !== 'undefined' && typeof window === 'object') {
            blob = new Blob([arrayBuffer], { type: mime })
          }

          resolve({ arrayBuffer, duration, mime, blob })
        },
        (msg: string) => {
          this.isRecording = false
          reject(new Error(`录音失败: ${msg}`))
        }
      )
    })
  }

  // 获取录音状态
  getIsRecording(): boolean {
    return this.isRecording
  }

  // 获取权限状态
  getHasPermission(): boolean {
    return this.hasPermission
  }

  // 清理资源
  destroy(): void {
    if (this.watchDogTimer) {
      clearInterval(this.watchDogTimer)
      this.watchDogTimer = 0
    }
    this.isRecording = false
    this.hasPermission = false
  }
}

// 导出创建 RecordApp 录音实例的辅助函数
export function createRecordAppRecorder(options: RecordAppOptions = {}): RecordAppRecorder {
  return new RecordAppRecorder(options)
}

// 为了向后兼容，保留原有的 createRecorder 函数，但使用 RecordApp
export function createRecorder(options = {}) {
  return new RecordAppRecorder(options)
}

// 销毁录音器
export function destroyRecorder(recorder: RecordAppRecorder) {
  recorder.destroy()
}
