/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-04-22 11:35:45
 * @modify date 2025-04-22 11:36:01
 * @desc [判断设备类型]
 */

// 获取 User-Agent 字符串
const userAgent = window.navigator.userAgent;
// 获取屏幕宽度
const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
// 判断是否是手机
export const isMobile = /Mobile/i.test(userAgent) || screenWidth < 768;
// 判断是否是平板电脑
export const isTablet = /Tablet/i.test(userAgent) || (screenWidth >= 768 && screenWidth <= 1024);
// 判断是否是PC
export const isPC = !isMobile && !isTablet;
