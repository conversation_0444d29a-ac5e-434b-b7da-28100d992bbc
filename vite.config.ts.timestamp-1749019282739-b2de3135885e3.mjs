// vite.config.ts
import { defineConfig, loadEnv } from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/vite/dist/node/index.js";
import path2 from "path";
import { UploadPlugin } from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/@msb-next/vite-plugins/lib/index.cjs";

// vite/plugins/index.js
import vue from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { viteMockServe } from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/vite-plugin-mock/dist/index.mjs";

// vite/plugins/auto-import.js
import autoImport from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: [
      "vue",
      "vue-router",
      "pinia"
    ],
    dts: false
  });
}

// vite/plugins/svg-icon.js
import { createSvgIconsPlugin } from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import path from "path";
function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons/svg")],
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isBuild
  });
}

// vite/plugins/compression.js
import compression from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/vite-plugin-compression/dist/index.mjs";
function createCompression(env) {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/setup-extend.js
import setupExtend from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
function createSetupExtend() {
  return setupExtend({});
}

// vite/plugins/index.js
function createVitePlugins(viteEnv, isBuild = false, command) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    viteMockServe({
      localEnabled: command === "serve",
      // 保证开发阶段可以使用 mock 接口
      mockPath: "mock",
      // 模拟数据的路径
      logger: true,
      // 是否在控制台输出日志
      supportTs: false,
      // 是否支持 TypeScript
      injectCode: `
        import { setupMockServer } from '@/mock/index';
        setupMockServer();
      `
    })
  ];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}

// package.json
var package_default = {
  name: "miaobi-admin-magic-touch",
  version: "0.0.1",
  description: "\u5999\u7B14AI\u540E\u53F0\u7BA1\u7406\u7CFB\u7EDF",
  author: "msb-front-end",
  license: "MIT",
  type: "module",
  scripts: {
    dev: "vite --config vite.config.ts",
    "build:live": "vite build --mode live --config vite.config.ts",
    "build:test": "vite build --mode test --config vite.config.ts",
    preview: "vite preview --config vite.config.ts"
  },
  dependencies: {
    "@element-plus/icons-vue": "2.3.1",
    "@msb-next/swiper": "^6.8.4",
    "@msb-next/vite-plugins": "latest",
    "@tencentcloud/chat-uikit-engine": "^2.4.0",
    "@tencentcloud/chat-uikit-vue": "^2.4.0",
    "@tinymce/tinymce-vue": "^6.1.0",
    "@types/howler": "^2.2.12",
    "@types/markdown-it": "^14.1.2",
    "@types/video.js": "^7.3.58",
    "@videojs-player/vue": "^1.0.0",
    "@vueup/vue-quill": "1.2.0",
    "@vueuse/core": "10.11.0",
    "ali-oss": "^6.22.0",
    axios: "0.28.1",
    "byted-ailab-speech-sdk": "^4.0.9",
    clipboard: "2.0.11",
    echarts: "5.5.1",
    "element-plus": "^2.9.5",
    "file-saver": "2.0.5",
    "fuse.js": "6.6.2",
    glob: "7.2.3",
    "highlight.js": "^11.11.1",
    howler: "^2.2.4",
    "js-beautify": "1.14.11",
    "js-cookie": "3.0.5",
    jsencrypt: "3.3.2",
    "lunar-javascript": "^1.7.2",
    "markdown-it": "^14.1.0",
    "markdown-it-image-figures": "^2.1.1",
    "mavon-editor": "3.0.1",
    md5: "^2.3.0",
    "msb-public-library": "git+https://opserver:<EMAIL>/msb-fe/msb-public-library.git#0.7.8",
    nprogress: "0.2.0",
    pinia: "2.1.7",
    "recorder-core": "^1.3.25011100",
    splitpanes: "3.1.5",
    three: "^0.174.0",
    "tim-js-sdk": "^2.27.6",
    tinymce: "^7.8.0",
    uuid: "^11.1.0",
    "video.js": "^8.22.0",
    vue: "3.4.31",
    "vue-cropper": "1.1.1",
    "vue-router": "4.4.0",
    vuedraggable: "4.1.0",
    xgplayer: "^3.0.21"
  },
  devDependencies: {
    "@types/ali-oss": "^6.16.11",
    "@types/uuid": "^10.0.0",
    "@vitejs/plugin-vue": "5.0.5",
    "@vitejs/plugin-vue-jsx": "^4.1.1",
    "@vue/babel-plugin-jsx": "^1.2.5",
    eruda: "^3.4.1",
    mockjs: "^1.1.0",
    prettier: "^3.5.3",
    sass: "1.77.5",
    "sass-loader": "10.1.1",
    tslib: "^2.8.1",
    typescript: "^5.7.3",
    "unplugin-auto-import": "0.17.6",
    "unplugin-vue-setup-extend-plus": "1.0.1",
    vite: "5.3.2",
    "vite-plugin-compression": "0.5.1",
    "vite-plugin-mock": "^3.0.2",
    "vite-plugin-svg-icons": "2.0.1"
  }
};

// vite.config.ts
import dayjs from "file:///Users/<USER>/Documents/workspace/mb-users-magic-brush/node_modules/dayjs/dayjs.min.js";
var __vite_injected_original_dirname = "/Users/<USER>/Documents/workspace/mb-users-magic-brush";
var vite_config_default = defineConfig(({ mode, command }) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd()),
    VITE_APP_TIME: `v.${package_default.version}.${dayjs().format("YY.MMDD.HHmm")}`
  };
  console.log("VITE_APP_ENV:", process.env.VITE_APP_ENV);
  console.log("VITE_APP_TIME:", process.env.VITE_APP_TIME);
  const isProd = process.env.VITE_APP_ENV && process.env.VITE_APP_ENV !== "dev";
  const plugins = [createVitePlugins(process.env, command === "build")];
  let base = "/";
  if (isProd) {
    const prefix = `${package_default.name}/${process.env.VITE_APP_ENV}`;
    base = `https://static.newbrush.com/${prefix}`;
    plugins.push(
      UploadPlugin({
        prefix,
        exclude: [],
        bucket: "meta-show"
      })
    );
  }
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base,
    build: {
      sourcemap: process.env.VITE_APP_ENV !== "live",
      // 添加以下配置以提高构建性能
      commonjsOptions: {
        transformMixedEsModules: true
        // 处理混合模块
      },
      // 限制警告日志，提高性能
      reportCompressedSize: false,
      // chunk 分割策略优化
      rollupOptions: {
        output: {
          manualChunks: {
            "vue-libs": ["vue", "vue-router", "pinia"],
            "element-plus": ["element-plus", "@element-plus/icons-vue"],
            "tim-sdk": ["@tencentcloud/chat-uikit-engine"],
            tinymce: ["tinymce", "@tinymce/tinymce-vue"]
            // 添加TinyMCE
          }
        }
      }
    },
    plugins,
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path2.resolve(__vite_injected_original_dirname, "./"),
        // 设置别名
        "@": path2.resolve(__vite_injected_original_dirname, "./src")
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      // 添加 HMR 配置
      hmr: {
        overlay: false
        // 禁用报错浮层，提高性能
      },
      // 添加热更新预热配置，预热您常用的文件
      warmup: {
        clientFiles: [
          "./src/views/aiChat/index.vue",
          // 您常用的文件
          "./src/components/Chat/index.vue",
          "./src/components/FullscreenVideo/index.vue",
          "./src/main.ts",
          // 入口文件
          "./src/App.vue"
          // 根组件
        ]
      },
      // 调整开发环境 sourcemap 配置
      sourcemap: "inline",
      // 更快的 sourcemap 生成方式
      // 优化文件监听配置
      watch: {
        usePolling: false,
        // 默认使用基于文件系统事件的监听，通常更高效
        ignored: ["**/node_modules/**", "**/dist/**", "**/.git/**"]
      },
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/api": {
          target: "http://admin-test.ai.miaobi.cn",
          changeOrigin: true
          // rewrite: p => p.replace(/^\/api/, ''),
        },
        "/uploadApi": {
          target: "https://oss-cn-hangzhou.aliyuncs.com/",
          changeOrigin: true
          // rewrite: p => p.replace(/^\/api/, ''),
        },
        // https://cn.vitejs.dev/config/#server-proxy
        "/clientApi": {
          target: "https://api-test.maliang.miaobi.cn",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/clientApi/, "")
        },
        "/bytedance": {
          target: "https://openspeech.bytedance.com",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/bytedance/, "")
        }
      }
    },
    // 优化依赖预构建配置
    optimizeDeps: {
      // 强制预构建 TIM SDK 相关依赖，避免运行时编译
      include: [
        "@tencentcloud/chat-uikit-engine",
        "vue-router",
        "pinia",
        "@element-plus/icons-vue",
        "element-plus",
        "tinymce",
        "@tinymce/tinymce-vue"
        // 添加TinyMCE相关依赖
      ],
      // 特例：对大型依赖的处理
      esbuildOptions: {
        target: "es2020"
        // 支持现代浏览器特性可提高构建速度
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              }
            }
          }
        ]
      },
      // 优化 CSS 处理
      preprocessorOptions: {
        scss: {
          // 如果有全局 SCSS 变量文件，可以在这里配置
          // additionalData: `@import "@/styles/variables.scss";`
        }
      }
    },
    // 优化 HMR 日志输出
    logLevel: isProd ? "info" : "warn"
    // 开发环境中减少日志输出
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
