/*
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-21 17:26:32
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-04-30 15:30:36
 * @FilePath: /miaobi-admin-magic-touch/vite.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import { UploadPlugin } from '@msb-next/vite-plugins'
import createVitePlugins from './vite/plugins'
import packageConf from './package.json'
import dayjs from 'dayjs'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd()),
    VITE_APP_TIME: `v.${packageConf.version}.${dayjs().format('YY.MMDD.HHmm')}`,
  }

  console.log('VITE_APP_ENV:', process.env.VITE_APP_ENV)
  console.log('VITE_APP_TIME:', process.env.VITE_APP_TIME)

  const isProd = process.env.VITE_APP_ENV && process.env.VITE_APP_ENV !== 'dev'
  const plugins = [createVitePlugins(process.env, command === 'build')]
  let base = '/'
  if (isProd) {
    const prefix = `${packageConf.name}/${process.env.VITE_APP_ENV}`
    base = `https://static.newbrush.com/${prefix}`
    plugins.push(
      UploadPlugin({
        prefix,
        exclude: [],
        bucket: 'meta-show',
        alwaysUpload: [/.*\.html$/], // 覆盖上传后缀为html
      })
    )
  }
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base,
    build: {
      sourcemap: process.env.VITE_APP_ENV !== 'live',
      // 添加以下配置以提高构建性能
      commonjsOptions: {
        transformMixedEsModules: true, // 处理混合模块
      },
      // 限制警告日志，提高性能
      reportCompressedSize: false,
      // chunk 分割策略优化
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-libs': ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            'tim-sdk': ['@tencentcloud/chat-uikit-engine'],
            tinymce: ['tinymce', '@tinymce/tinymce-vue'], // 添加TinyMCE
          },
        },
      },
    },
    plugins,
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      // 添加 HMR 配置
      hmr: {
        overlay: false, // 禁用报错浮层，提高性能
      },
      // 添加热更新预热配置，预热您常用的文件
      warmup: {
        clientFiles: [
          './src/views/aiChat/index.vue', // 您常用的文件
          './src/components/Chat/index.vue',
          './src/components/FullscreenVideo/index.vue',
          './src/main.ts', // 入口文件
          './src/App.vue', // 根组件
        ],
      },
      // 调整开发环境 sourcemap 配置
      sourcemap: 'inline', // 更快的 sourcemap 生成方式

      // 优化文件监听配置
      watch: {
        usePolling: false, // 默认使用基于文件系统事件的监听，通常更高效
        ignored: ['**/node_modules/**', '**/dist/**', '**/.git/**'],
      },

      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/api': {
          target: 'http://admin-test.ai.miaobi.cn',
          changeOrigin: true,
          // rewrite: p => p.replace(/^\/api/, ''),
        },
        '/uploadApi': {
          target: 'https://oss-cn-hangzhou.aliyuncs.com/',
          changeOrigin: true,
          // rewrite: p => p.replace(/^\/api/, ''),
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/clientApi': {
          target: 'https://api-test.maliang.miaobi.cn',
          changeOrigin: true,
          rewrite: p => p.replace(/^\/clientApi/, ''),
        },
        '/bytedance': {
          target: 'https://openspeech.bytedance.com',
          changeOrigin: true,
          rewrite: p => p.replace(/^\/bytedance/, ''),
        },
      },
    },

    // 优化依赖预构建配置
    optimizeDeps: {
      // 强制预构建 TIM SDK 相关依赖，避免运行时编译
      include: [
        '@tencentcloud/chat-uikit-engine',
        'vue-router',
        'pinia',
        '@element-plus/icons-vue',
        'element-plus',
        'tinymce',
        '@tinymce/tinymce-vue', // 添加TinyMCE相关依赖
      ],
      // 特例：对大型依赖的处理
      esbuildOptions: {
        target: 'es2020', // 支持现代浏览器特性可提高构建速度
      },
    },

    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              },
            },
          },
        ],
      },
      // 优化 CSS 处理
      preprocessorOptions: {
        scss: {
          // 如果有全局 SCSS 变量文件，可以在这里配置
          // additionalData: `@import "@/styles/variables.scss";`
        },
      },
    },

    // 优化 HMR 日志输出
    logLevel: isProd ? 'info' : 'warn', // 开发环境中减少日志输出
  }
})
