{"bracketSameLine": false, "bracketSpacing": true, "printWidth": 200, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "jsxBracketSameLine": false, "arrowParens": "avoid", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "endOfLine": "lf", "vueIndentScriptAndStyle": false}